# 🤖 Zara AI Assistant

**An Advanced Multilingual AI Voice Assistant**

Created by **<PERSON><PERSON> ** | Based on Nova AI Architecture

---

## 🌟 Overview

Zara is a sophisticated AI assistant that combines voice interaction, visual analysis, and system automation capabilities. Built with cutting-edge technologies including LiveKit, Google's Realtime Model, and Firebase integration.

### ✨ Key Features

- 🗣️ **Multilingual Voice Interaction** - Primarily Hindi with English support
- 👁️ **Visual Analysis** - Real-time camera-based scene understanding
- 🛠️ **50+ Built-in Tools** - System control, web search, automation, and more
- 🔥 **Firebase Integration** - Cloud storage and real-time data sync
- 🎯 **Smart Automation** - GUI control, window management, and task automation
- 🔒 **Security Features** - System scanning and network analysis
- 📊 **Data Analysis** - Advanced visualization and reporting capabilities

---

## 🚀 Quick Start

### Prerequisites

- Python 3.11 or higher
- Windows 10/11 (primary support)
- Microphone and camera (optional for visual features)
- Internet connection for AI services

### Installation

1. **Clone or download the project**
   ```bash
   cd Project1
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup Firebase (Optional but recommended)**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or use existing: `my-chat-62345`
   - Generate service account key
   - Save as `zara_service_account.json` in project folder

4. **Launch Zara**
   ```bash
   python run_zara.py
   ```

### Alternative Launch Methods

```bash
# GUI Mode (default)
python run_zara.py gui

# Voice-only mode
python run_zara.py voice

# Check dependencies
python run_zara.py check

# Show help
python run_zara.py help
```

---

## 🛠️ Configuration

### Firebase Setup

Your Firebase configuration is already included:
- **Project ID**: `my-chat-62345`
- **Database URL**: `https://my-chat-62345-default-rtdb.firebaseio.com`
- **Auth Domain**: `my-chat-62345.firebaseapp.com`

To enable full Firebase features:
1. Download service account key from Firebase Console
2. Save as `zara_service_account.json`
3. Restart Zara

### Environment Variables

Create a `.env` file for additional configuration:
```env
GOOGLE_API_KEY=your_google_api_key
YOUTUBE_API_KEY=your_youtube_api_key
OPENAI_API_KEY=your_openai_api_key
```

---

## 🎯 Available Tools & Capabilities

### 🌐 Information & Search
- Weather information retrieval
- Wikipedia and web search
- Real-time data lookup
- News and updates

### 💻 System Control
- Power management (shutdown, restart, lock)
- Window and application management
- Desktop automation
- File system operations

### 📱 Communication
- WhatsApp message sending
- Email automation
- Text input and notepad writing
- Voice-to-text conversion

### 🎵 Media & Entertainment
- YouTube video playback
- Music streaming control
- Media file management
- Entertainment recommendations

### 🔍 Analysis & Security
- System virus scanning
- Network security analysis
- Data visualization and reporting
- Performance monitoring

### 👁️ Visual Intelligence
- Real-time scene analysis
- Object and text recognition
- Camera-based assistance
- Visual context understanding

---

## 🗣️ Voice Commands Examples

### Hindi Commands
- "मौसम कैसा है?" (How's the weather?)
- "YouTube पर गाना बजाओ" (Play song on YouTube)
- "WhatsApp पर मैसेज भेजो" (Send WhatsApp message)
- "कंप्यूटर बंद करो" (Shutdown computer)
- "आज की याददाश्त बताओ" (Tell today's reminders)

### English Commands
- "What's the weather like?"
- "Search for information about..."
- "Take a screenshot"
- "Open calculator"
- "Analyze what you see"

---

## 📁 Project Structure

```
Project1/
├── zara_voice_assistant.py    # Core AI assistant logic
├── prompts.py                 # AI prompts and personality
├── tools.py                   # 50+ tool functions
├── firebase_config.py         # Firebase integration
├── zara_gui.py               # GUI application
├── run_zara.py               # Main launcher
├── requirements.txt          # Python dependencies
├── README.md                 # This file
└── zara_memory/              # Local data storage
```

---

## 🔧 Customization

### Personality Modification
Edit `prompts.py` to customize Zara's personality, language preferences, and behavior patterns.

### Adding New Tools
Add new functions to `tools.py` following the existing pattern:
```python
async def your_new_tool(parameter: str) -> str:
    """Tool description for AI understanding"""
    # Your implementation
    return "Result message"
```

### UI Customization
Modify `zara_gui.py` to change the interface design, colors, and layout.

---

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   pip install -r requirements.txt
   python run_zara.py check
   ```

2. **Firebase Connection Issues**
   - Verify service account key file
   - Check internet connection
   - Ensure Firebase project is active

3. **Voice Recognition Problems**
   - Check microphone permissions
   - Verify audio device settings
   - Test with different voice commands

4. **GUI Not Loading**
   - Install PyQt5: `pip install PyQt5`
   - Check display settings
   - Try voice-only mode: `python run_zara.py voice`

### Logs and Debugging

- Check `zara.log` for detailed error messages
- Enable debug mode in `run_zara.py`
- Use `python run_zara.py check` to verify setup

---

## 🤝 Contributing

This project is based on the Nova AI architecture. To contribute:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

## 📄 License

This project is created by **Sanjay Singh** and is based on the Nova AI architecture. Please respect the original creator's work and use responsibly.

---

## 🙏 Acknowledgments

- **Sanjay Singh** - Original creator and developer
- **Nova AI** - Base architecture and inspiration
- **LiveKit** - Real-time communication framework
- **Google AI** - Realtime Model and voice capabilities
- **Firebase** - Cloud infrastructure and data storage

---

## 📞 Support

For support and questions:
- Check the troubleshooting section above
- Review the logs in `zara.log`
- Ensure all dependencies are properly installed

**Enjoy using Zara AI Assistant! 🚀**
