@echo off
title Zara AI Assistant Launcher
color 0A

echo.
echo ========================================
echo    🤖 ZARA AI ASSISTANT LAUNCHER 🤖
echo ========================================
echo    Created by: <PERSON><PERSON><PERSON>
echo    Version: 1.0
echo    Based on: Nova AI Architecture
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.11 or higher
    pause
    exit /b 1
)

echo ✅ Python found!
echo.

echo 🚀 Starting Zara AI Assistant...
echo.

python run_zara.py

if errorlevel 1 (
    echo.
    echo ❌ <PERSON>ara failed to start
    echo Check the error messages above
    echo.
    pause
) else (
    echo.
    echo ✅ <PERSON>ara finished successfully
)

pause
