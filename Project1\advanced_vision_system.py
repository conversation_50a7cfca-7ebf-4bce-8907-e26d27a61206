#!/usr/bin/env python3
"""
Advanced Vision System for Zara
Human-like visual perception, understanding, and interaction
"""

import cv2
import numpy as np
import asyncio
import threading
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import pyautogui
import pytesseract
from PIL import Image, ImageGrab, ImageDraw, ImageFont
import easyocr
import mediapipe as mp

# Advanced computer vision
try:
    import torch
    import torchvision.transforms as transforms
    from ultralytics import YOLO
    ADVANCED_CV_AVAILABLE = True
except ImportError:
    ADVANCED_CV_AVAILABLE = False
    print("⚠️ Advanced CV libraries not available. Install: pip install torch ultralytics")

class AdvancedVisionSystem:
    """
    Advanced Vision System - Human-like visual perception
    """
    
    def __init__(self):
        self.is_active = False
        self.current_frame = None
        self.previous_frame = None
        self.detected_objects = []
        self.ui_elements = []
        self.text_regions = []
        self.face_locations = []
        self.gesture_data = []
        
        # Initialize OCR engines
        self.pytesseract_engine = pytesseract
        try:
            self.easyocr_engine = easyocr.Reader(['en', 'hi'])  # English and Hindi
            self.ocr_available = True
        except:
            self.ocr_available = False
            print("⚠️ EasyOCR not available")
        
        # Initialize MediaPipe for hand/face detection
        self.mp_hands = mp.solutions.hands
        self.mp_face = mp.solutions.face_detection
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        self.face_detection = self.mp_face.FaceDetection(
            model_selection=0,
            min_detection_confidence=0.5
        )
        
        # Initialize YOLO for object detection if available
        if ADVANCED_CV_AVAILABLE:
            try:
                self.yolo_model = YOLO('yolov8n.pt')  # Nano model for speed
                self.object_detection_available = True
            except:
                self.object_detection_available = False
        else:
            self.object_detection_available = False
        
        # Visual memory and learning
        self.visual_memory = []
        self.learned_patterns = {}
        self.ui_element_database = {}
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def start_vision_system(self):
        """Start the advanced vision system"""
        self.is_active = True
        self.logger.info("👁️ Advanced Vision System activated")
        
        # Start parallel vision processes
        tasks = [
            self._continuous_screen_capture(),
            self._object_detection_loop(),
            self._ui_element_detection_loop(),
            self._text_recognition_loop(),
            self._gesture_recognition_loop(),
            self._visual_learning_loop()
        ]
        
        await asyncio.gather(*tasks)
    
    async def _continuous_screen_capture(self):
        """Continuously capture and process screen"""
        while self.is_active:
            try:
                # Capture screen
                screenshot = ImageGrab.grab()
                self.previous_frame = self.current_frame
                self.current_frame = np.array(screenshot)
                
                # Store in visual memory
                self._update_visual_memory()
                
                await asyncio.sleep(0.1)  # 10 FPS
                
            except Exception as e:
                self.logger.error(f"Screen capture error: {e}")
                await asyncio.sleep(1)
    
    def _update_visual_memory(self):
        """Update visual memory with current frame"""
        if self.current_frame is not None:
            memory_entry = {
                'timestamp': datetime.now(),
                'frame_hash': hash(self.current_frame.tobytes()),
                'detected_objects': self.detected_objects.copy(),
                'ui_elements': self.ui_elements.copy(),
                'text_regions': self.text_regions.copy()
            }
            
            self.visual_memory.append(memory_entry)
            
            # Keep only last 100 frames in memory
            if len(self.visual_memory) > 100:
                self.visual_memory.pop(0)
    
    async def _object_detection_loop(self):
        """Detect objects in the current frame"""
        while self.is_active:
            try:
                if self.current_frame is not None and self.object_detection_available:
                    # Run YOLO detection
                    results = self.yolo_model(self.current_frame)
                    
                    self.detected_objects = []
                    for result in results:
                        boxes = result.boxes
                        if boxes is not None:
                            for box in boxes:
                                # Extract object information
                                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                                confidence = box.conf[0].cpu().numpy()
                                class_id = int(box.cls[0].cpu().numpy())
                                class_name = self.yolo_model.names[class_id]
                                
                                if confidence > 0.5:  # Confidence threshold
                                    self.detected_objects.append({
                                        'class': class_name,
                                        'confidence': float(confidence),
                                        'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                        'center': [int((x1+x2)/2), int((y1+y2)/2)]
                                    })
                
                await asyncio.sleep(0.5)  # 2 FPS for object detection
                
            except Exception as e:
                self.logger.error(f"Object detection error: {e}")
                await asyncio.sleep(1)
    
    async def _ui_element_detection_loop(self):
        """Detect UI elements like buttons, text fields, etc."""
        while self.is_active:
            try:
                if self.current_frame is not None:
                    self.ui_elements = self._detect_ui_elements(self.current_frame)
                
                await asyncio.sleep(0.3)  # ~3 FPS for UI detection
                
            except Exception as e:
                self.logger.error(f"UI detection error: {e}")
                await asyncio.sleep(1)
    
    def _detect_ui_elements(self, frame: np.ndarray) -> List[Dict]:
        """Advanced UI element detection"""
        ui_elements = []
        
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Detect buttons using template matching and contour detection
            buttons = self._detect_buttons(gray)
            ui_elements.extend(buttons)
            
            # Detect text fields
            text_fields = self._detect_text_fields(gray)
            ui_elements.extend(text_fields)
            
            # Detect clickable areas
            clickable_areas = self._detect_clickable_areas(gray)
            ui_elements.extend(clickable_areas)
            
            return ui_elements
            
        except Exception as e:
            self.logger.error(f"UI element detection error: {e}")
            return []
    
    def _detect_buttons(self, gray_frame: np.ndarray) -> List[Dict]:
        """Detect button-like UI elements"""
        buttons = []
        
        # Use edge detection to find rectangular shapes
        edges = cv2.Canny(gray_frame, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 500 < area < 50000:  # Button-like size
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                
                # Check if it looks like a button
                if 0.3 < aspect_ratio < 10 and w > 30 and h > 15:
                    buttons.append({
                        'type': 'button',
                        'bbox': [x, y, x+w, y+h],
                        'center': [x + w//2, y + h//2],
                        'area': area,
                        'confidence': 0.7
                    })
        
        return buttons[:20]  # Limit to top 20
    
    def _detect_text_fields(self, gray_frame: np.ndarray) -> List[Dict]:
        """Detect text input fields"""
        text_fields = []
        
        # Look for rectangular areas that might be text fields
        # This is a simplified implementation
        contours, _ = cv2.findContours(gray_frame, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 1000 < area < 20000:  # Text field size
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                
                # Text fields are usually wider than tall
                if aspect_ratio > 2 and h > 20 and h < 50:
                    text_fields.append({
                        'type': 'text_field',
                        'bbox': [x, y, x+w, y+h],
                        'center': [x + w//2, y + h//2],
                        'area': area,
                        'confidence': 0.6
                    })
        
        return text_fields[:10]
    
    def _detect_clickable_areas(self, gray_frame: np.ndarray) -> List[Dict]:
        """Detect other clickable areas"""
        clickable_areas = []
        
        # Use corner detection to find potential clickable areas
        corners = cv2.goodFeaturesToTrack(gray_frame, maxCorners=100, qualityLevel=0.01, minDistance=10)
        
        if corners is not None:
            for corner in corners:
                x, y = corner.ravel()
                clickable_areas.append({
                    'type': 'clickable_area',
                    'center': [int(x), int(y)],
                    'bbox': [int(x-10), int(y-10), int(x+10), int(y+10)],
                    'confidence': 0.5
                })
        
        return clickable_areas[:15]
    
    async def _text_recognition_loop(self):
        """Recognize and extract text from screen"""
        while self.is_active:
            try:
                if self.current_frame is not None:
                    self.text_regions = await self._extract_text_regions(self.current_frame)
                
                await asyncio.sleep(1.0)  # 1 FPS for text recognition
                
            except Exception as e:
                self.logger.error(f"Text recognition error: {e}")
                await asyncio.sleep(2)
    
    async def _extract_text_regions(self, frame: np.ndarray) -> List[Dict]:
        """Extract text regions with advanced OCR"""
        text_regions = []
        
        try:
            # Use EasyOCR for better accuracy
            if self.ocr_available:
                results = self.easyocr_engine.readtext(frame)
                
                for (bbox, text, confidence) in results:
                    if confidence > 0.5:  # Confidence threshold
                        # Convert bbox to standard format
                        x1, y1 = bbox[0]
                        x2, y2 = bbox[2]
                        
                        text_regions.append({
                            'text': text,
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'center': [int((x1+x2)/2), int((y1+y2)/2)],
                            'confidence': confidence,
                            'language': 'auto'
                        })
            
            # Fallback to pytesseract
            else:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                text = pytesseract.image_to_string(gray)
                
                if text.strip():
                    text_regions.append({
                        'text': text.strip(),
                        'bbox': [0, 0, frame.shape[1], frame.shape[0]],
                        'center': [frame.shape[1]//2, frame.shape[0]//2],
                        'confidence': 0.5,
                        'language': 'auto'
                    })
            
            return text_regions
            
        except Exception as e:
            self.logger.error(f"Text extraction error: {e}")
            return []
    
    async def _gesture_recognition_loop(self):
        """Recognize hand gestures and face expressions"""
        while self.is_active:
            try:
                if self.current_frame is not None:
                    # Detect hands
                    hands_results = self.hands.process(cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB))
                    
                    # Detect faces
                    face_results = self.face_detection.process(cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB))
                    
                    # Process results
                    self._process_gesture_results(hands_results, face_results)
                
                await asyncio.sleep(0.2)  # 5 FPS for gesture recognition
                
            except Exception as e:
                self.logger.error(f"Gesture recognition error: {e}")
                await asyncio.sleep(1)
    
    def _process_gesture_results(self, hands_results, face_results):
        """Process gesture recognition results"""
        self.gesture_data = []
        
        # Process hand landmarks
        if hands_results.multi_hand_landmarks:
            for hand_landmarks in hands_results.multi_hand_landmarks:
                # Extract key points
                landmarks = []
                for landmark in hand_landmarks.landmark:
                    landmarks.append([landmark.x, landmark.y, landmark.z])
                
                self.gesture_data.append({
                    'type': 'hand',
                    'landmarks': landmarks,
                    'timestamp': datetime.now()
                })
        
        # Process face detections
        if face_results.detections:
            for detection in face_results.detections:
                bbox = detection.location_data.relative_bounding_box
                
                self.face_locations.append({
                    'bbox': [bbox.xmin, bbox.ymin, bbox.width, bbox.height],
                    'confidence': detection.score[0],
                    'timestamp': datetime.now()
                })
    
    async def _visual_learning_loop(self):
        """Learn from visual patterns and user interactions"""
        while self.is_active:
            try:
                # Analyze visual patterns
                await self._analyze_visual_patterns()
                
                # Update learned patterns
                await self._update_learned_patterns()
                
                await asyncio.sleep(5.0)  # Learn every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Visual learning error: {e}")
                await asyncio.sleep(2)
    
    async def _analyze_visual_patterns(self):
        """Analyze patterns in visual data"""
        if len(self.visual_memory) < 10:
            return
        
        # Analyze UI element patterns
        recent_ui_elements = []
        for memory in self.visual_memory[-10:]:
            recent_ui_elements.extend(memory['ui_elements'])
        
        # Find frequently appearing UI elements
        ui_element_counts = {}
        for element in recent_ui_elements:
            key = f"{element['type']}_{element.get('center', [0, 0])}"
            ui_element_counts[key] = ui_element_counts.get(key, 0) + 1
        
        # Store frequently used elements
        for key, count in ui_element_counts.items():
            if count >= 3:  # Appeared at least 3 times
                self.learned_patterns[key] = {
                    'count': count,
                    'last_seen': datetime.now(),
                    'type': 'ui_element'
                }
    
    async def _update_learned_patterns(self):
        """Update and save learned patterns"""
        try:
            # Save learned patterns to file
            with open('zara_visual_patterns.json', 'w') as f:
                json.dump(self.learned_patterns, f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Failed to save visual patterns: {e}")
    
    def find_element_by_text(self, text: str) -> Optional[Dict]:
        """Find UI element containing specific text"""
        for region in self.text_regions:
            if text.lower() in region['text'].lower():
                return region
        return None
    
    def find_clickable_near_text(self, text: str, max_distance: int = 100) -> Optional[Dict]:
        """Find clickable element near specific text"""
        text_region = self.find_element_by_text(text)
        if not text_region:
            return None
        
        text_center = text_region['center']
        
        # Find nearest clickable element
        nearest_element = None
        min_distance = float('inf')
        
        for element in self.ui_elements:
            if element['type'] in ['button', 'clickable_area']:
                element_center = element['center']
                distance = np.sqrt((text_center[0] - element_center[0])**2 + 
                                 (text_center[1] - element_center[1])**2)
                
                if distance < max_distance and distance < min_distance:
                    min_distance = distance
                    nearest_element = element
        
        return nearest_element
    
    def get_visual_summary(self) -> Dict[str, Any]:
        """Get summary of current visual state"""
        return {
            'detected_objects': len(self.detected_objects),
            'ui_elements': len(self.ui_elements),
            'text_regions': len(self.text_regions),
            'faces_detected': len(self.face_locations),
            'gestures_detected': len(self.gesture_data),
            'visual_memory_size': len(self.visual_memory),
            'learned_patterns': len(self.learned_patterns),
            'is_active': self.is_active
        }
    
    async def smart_click(self, target_description: str) -> bool:
        """Intelligently click on described target"""
        try:
            # Try to find by text first
            element = self.find_element_by_text(target_description)
            if element:
                pyautogui.click(element['center'][0], element['center'][1])
                self.logger.info(f"Clicked on text: {target_description}")
                return True
            
            # Try to find clickable near text
            element = self.find_clickable_near_text(target_description)
            if element:
                pyautogui.click(element['center'][0], element['center'][1])
                self.logger.info(f"Clicked near text: {target_description}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Smart click error: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown the vision system"""
        self.is_active = False
        await self._update_learned_patterns()
        self.logger.info("👁️ Advanced Vision System shutdown")
