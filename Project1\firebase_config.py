# Firebase Configuration for Zara AI Assistant
# Your Firebase project configuration

import firebase_admin
from firebase_admin import credentials, firestore, db
import os
import json

# Firebase configuration (converted from JavaScript config)
FIREBASE_CONFIG = {
    "apiKey": "AIzaSyDyOJ0X2aJfq8wzqz490GiY06QstFAKr24",
    "authDomain": "my-chat-62345.firebaseapp.com",
    "databaseURL": "https://my-chat-62345-default-rtdb.firebaseio.com",
    "projectId": "my-chat-62345",
    "storageBucket": "my-chat-62345.firebasestorage.app",
    "messagingSenderId": "************",
    "appId": "1:************:web:47872f4881cc370682d319",
    "measurementId": "G-FXR5JFM1CS"
}

class ZaraFirebaseManager:
    """Firebase manager for Zara AI Assistant"""
    
    def __init__(self):
        self.app = None
        self.db_client = None
        self.realtime_db = None
        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase with service account or default credentials"""
        try:
            # Try to initialize with service account key if available
            service_account_path = "zara_service_account.json"
            
            if os.path.exists(service_account_path):
                cred = credentials.Certificate(service_account_path)
                self.app = firebase_admin.initialize_app(cred, {
                    'databaseURL': FIREBASE_CONFIG['databaseURL'],
                    'projectId': FIREBASE_CONFIG['projectId']
                }, name='zara_app')
            else:
                # Initialize with default credentials (for development)
                # You'll need to set up service account credentials for production
                print("⚠️ Service account key not found. Using default initialization.")
                print("📝 To set up Firebase properly:")
                print("   1. Go to Firebase Console > Project Settings > Service Accounts")
                print("   2. Generate new private key")
                print("   3. Save as 'zara_service_account.json' in the project folder")
                
                # For now, we'll create a mock configuration
                self._create_mock_service_account()
                return
            
            # Initialize Firestore and Realtime Database
            self.db_client = firestore.client(app=self.app)
            self.realtime_db = db.reference('/', app=self.app)
            
            print("✅ Zara Firebase initialized successfully!")
            
        except Exception as e:
            print(f"⚠️ Firebase initialization failed: {str(e)}")
            print("📝 Make sure you have the correct service account key file.")
            self._create_mock_service_account()
    
    def _create_mock_service_account(self):
        """Create a template service account file for user setup"""
        template = {
            "type": "service_account",
            "project_id": FIREBASE_CONFIG['projectId'],
            "private_key_id": "YOUR_PRIVATE_KEY_ID",
            "private_key": "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n",
            "client_email": f"firebase-adminsdk-xxxxx@{FIREBASE_CONFIG['projectId']}.iam.gserviceaccount.com",
            "client_id": "YOUR_CLIENT_ID",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": f"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40{FIREBASE_CONFIG['projectId']}.iam.gserviceaccount.com"
        }
        
        with open("zara_service_account_template.json", "w") as f:
            json.dump(template, f, indent=2)
        
        print("📄 Created 'zara_service_account_template.json' - please fill in your actual credentials")
    
    async def save_conversation(self, user_message: str, assistant_response: str):
        """Save conversation to Firestore"""
        if not self.db_client:
            return False
        
        try:
            doc_ref = self.db_client.collection('zara_conversations').document()
            doc_ref.set({
                'user_message': user_message,
                'assistant_response': assistant_response,
                'timestamp': firestore.SERVER_TIMESTAMP,
                'assistant_name': 'Zara'
            })
            return True
        except Exception as e:
            print(f"⚠️ Failed to save conversation: {str(e)}")
            return False
    
    async def get_user_preferences(self, user_id: str = "default"):
        """Get user preferences from Firestore"""
        if not self.db_client:
            return {}
        
        try:
            doc_ref = self.db_client.collection('zara_users').document(user_id)
            doc = doc_ref.get()
            if doc.exists:
                return doc.to_dict()
            else:
                # Create default preferences
                default_prefs = {
                    'language': 'hindi',
                    'voice_speed': 'normal',
                    'assistant_name': 'Zara',
                    'created_at': firestore.SERVER_TIMESTAMP
                }
                doc_ref.set(default_prefs)
                return default_prefs
        except Exception as e:
            print(f"⚠️ Failed to get user preferences: {str(e)}")
            return {}
    
    async def save_reminder(self, reminder_text: str, reminder_date: str):
        """Save reminder to Realtime Database"""
        if not self.realtime_db:
            return False
        
        try:
            reminders_ref = self.realtime_db.child('zara_reminders')
            reminders_ref.push({
                'text': reminder_text,
                'date': reminder_date,
                'created_at': db.ServerValue.TIMESTAMP,
                'completed': False
            })
            return True
        except Exception as e:
            print(f"⚠️ Failed to save reminder: {str(e)}")
            return False
    
    async def get_today_reminders(self, date_str: str):
        """Get today's reminders from Realtime Database"""
        if not self.realtime_db:
            return []
        
        try:
            reminders_ref = self.realtime_db.child('zara_reminders')
            reminders = reminders_ref.order_by_child('date').equal_to(date_str).get()
            
            if reminders:
                return [reminder for reminder in reminders.values() if not reminder.get('completed', False)]
            return []
        except Exception as e:
            print(f"⚠️ Failed to get reminders: {str(e)}")
            return []

# Global Firebase manager instance
firebase_manager = ZaraFirebaseManager()

# Helper functions for backward compatibility
async def save_conversation_to_firebase(user_msg: str, assistant_msg: str):
    """Save conversation to Firebase"""
    return await firebase_manager.save_conversation(user_msg, assistant_msg)

async def get_today_reminder_message_from_firebase(date_str: str = None):
    """Get today's reminders from Firebase"""
    if not date_str:
        from datetime import datetime
        date_str = datetime.now().strftime('%Y-%m-%d')
    
    reminders = await firebase_manager.get_today_reminders(date_str)
    if reminders:
        reminder_texts = [r['text'] for r in reminders]
        return " | ".join(reminder_texts)
    return None
