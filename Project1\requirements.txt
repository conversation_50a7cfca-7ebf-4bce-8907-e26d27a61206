# Zara AI Assistant - Python Dependencies

# Core LiveKit and AI
livekit
livekit-agents
livekit-plugins-google

# Firebase
firebase-admin
google-cloud-firestore
google-cloud-storage

# Web and HTTP
aiohttp
requests
urllib3

# Data Processing
pandas
numpy
matplotlib
seaborn
plotly

# Computer Vision and Advanced AI
opencv-python
Pillow
pytesseract
easyocr
mediapipe
ultralytics
torch
torchvision
transformers
openai

# GUI and Automation
PyQt5
pyautogui
pynput
uiautomation
selenium
keyboard
mouse

# Audio/Media and Speech
pygame
pydub
speechrecognition
pyttsx3

# System and OS
psutil
pywin32; sys_platform == "win32"

# Search and Web
wikipedia
duckduckgo-search
beautifulsoup4

# Communication
pywhatkit


# Utilities
python-dotenv


# Security
python-nmap
scapy

# Additional utilities
colorama
tqdm
rich
