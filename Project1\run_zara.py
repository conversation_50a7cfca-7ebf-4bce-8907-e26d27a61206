#!/usr/bin/env python3
"""
Zara AI Assistant - Main Launcher
Created by <PERSON><PERSON> ratnam

This script launches Zara AI Assistant with proper environment setup.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def setup_environment():
    """Setup environment variables and paths"""
    # Set up logging with UTF-8 encoding to handle emojis
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('zara.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # Create necessary directories
    os.makedirs('zara_memory', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    print("🤖 Zara AI Assistant - Environment Setup Complete")
    print("=" * 50)
    print("Created by: <PERSON><PERSON> ratnam")
    print("Version: 1.0")
    print("Based on: Nova AI Architecture")
    print("=" * 50)

def check_dependencies():
    """Check if all required dependencies are installed"""
    # Package name mapping: pip_name -> import_name
    required_packages = {
        'livekit': 'livekit',
        'livekit-agents': 'livekit.agents',
        'livekit-plugins-google': 'livekit.plugins.google',
        'PyQt5': 'PyQt5',
        'firebase-admin': 'firebase_admin',
        'aiohttp': 'aiohttp',
        'pandas': 'pandas',
        'numpy': 'numpy'
    }

    missing_packages = []

    for pip_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {pip_name}")
        except ImportError:
            missing_packages.append(pip_name)
            print(f"❌ {pip_name} - Missing")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        print("\nAlternatively, install all dependencies:")
        print("pip install -r requirements.txt")
        return False
    
    print("\n✅ All dependencies are installed!")
    return True

def launch_gui():
    """Launch Zara GUI application"""
    try:
        from zara_gui import main as gui_main
        print("🚀 Launching Zara GUI...")
        gui_main()
    except Exception as e:
        print(f"❌ Failed to launch GUI: {e}")
        return False
    return True

def launch_voice_only():
    """Launch Zara in voice-only mode"""
    try:
        from zara_agent_runner import main as agent_main
        print("🎤 Launching Zara Voice Assistant...")

        # Check environment variables
        required_vars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET']
        missing_vars = [var for var in required_vars if not os.getenv(var)]

        if missing_vars:
            print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
            print("Please check your .env file")
            return False

        print("✅ Zara Voice Assistant is ready!")
        print("🗣️ Connect to the LiveKit room to interact with Zara...")
        print(f"🔗 LiveKit URL: {os.getenv('LIVEKIT_URL')}")
        print("📝 Use Ctrl+C to stop the agent")

        # Run the agent using the main function (which uses LiveKit CLI)
        agent_main()

    except KeyboardInterrupt:
        print("\n👋 Zara shutdown requested by user")
        return True
    except Exception as e:
        print(f"❌ Failed to launch voice assistant: {e}")
        return False
    return True

def show_help():
    """Show help information"""
    help_text = """
🤖 Zara AI Assistant - Help

Usage:
    python run_zara.py [option]

Options:
    gui         Launch with GUI interface (default)
    voice       Launch voice-only mode
    console     Check dependencies and exit
    check       Check dependencies only
    help        Show this help message

Examples:
    python run_zara.py          # Launch GUI
    python run_zara.py gui      # Launch GUI
    python run_zara.py voice    # Voice-only mode
    python run_zara.py console  # Check dependencies
    python run_zara.py check    # Check dependencies

Features:
    ✅ Multilingual support (Hindi/English)
    ✅ Voice interaction with Google Realtime Model
    ✅ Visual analysis capabilities
    ✅ System control and automation
    ✅ Firebase integration for data storage
    ✅ 50+ built-in tools and functions
    ✅ Web search and information retrieval
    ✅ Media playback and entertainment
    ✅ Security scanning and monitoring

Created by: Sanjay Singh
Based on: Nova AI Architecture
"""
    print(help_text)

def main():
    """Main entry point"""
    # Parse command line arguments
    args = sys.argv[1:] if len(sys.argv) > 1 else ['gui']
    command = args[0].lower() if args else 'gui'
    
    # Setup environment
    setup_environment()
    
    if command == 'help':
        show_help()
        return
    
    if command == 'check':
        check_dependencies()
        return
    
    # Check dependencies before launching
    if not check_dependencies():
        print("\n❌ Cannot launch Zara due to missing dependencies.")
        print("Please install the required packages and try again.")
        return
    
    # Launch based on command
    if command == 'gui':
        success = launch_gui()
    elif command == 'voice':
        success = launch_voice_only()
    elif command == 'console':
        # Console mode - just run dependency check and exit
        print("✅ Console mode - dependency check completed!")
        return
    else:
        print(f"❌ Unknown command: {command}")
        print("Use 'python run_zara.py help' for available options.")
        print("Available commands: gui, voice, console, check, help")
        return
    
    if success:
        print("✅ Zara launched successfully!")
    else:
        print("❌ Failed to launch Zara.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Zara shutdown requested by user.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        logging.exception("Unexpected error in main")
