import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: []
    Component {
        file: "private/qqmlengine_p.h"
        name: "QObject"
        exports: ["QML/QtObject 1.0", "QtQml/QtObject 2.0"]
        exportMetaObjectRevisions: [0, 0]
        Property { name: "objectName"; type: "string" }
        Signal {
            name: "objectNameChanged"
            Parameter { name: "objectName"; type: "string" }
        }
        Method {
            name: "_q_reregisterTimers"
            Parameter { type: "void"; isPointer: true }
        }
        Method { name: "toString" }
        Method { name: "destroy" }
        Method {
            name: "destroy"
            Parameter { name: "delay"; type: "int" }
        }
    }
    Component {
        file: "private/qqmlbind_p.h"
        name: "QQmlBind"
        prototype: "QObject"
        exports: [
            "QtQml/Binding 2.0",
            "QtQml/Binding 2.14",
            "QtQml/Binding 2.8"
        ]
        exportMetaObjectRevisions: [0, 14, 8]
        Enum {
            name: "RestorationMode"
            values: [
                "RestoreNone",
                "RestoreBinding",
                "RestoreValue",
                "RestoreBindingOrValue"
            ]
        }
        Property { name: "target"; type: "QObject"; isPointer: true }
        Property { name: "property"; type: "string" }
        Property { name: "value"; type: "QJSValue" }
        Property { name: "when"; type: "bool" }
        Property { name: "delayed"; revision: 8; type: "bool" }
        Property { name: "restoreMode"; revision: 14; type: "RestorationMode" }
        Method { name: "targetValueChanged" }
    }
    Component {
        file: "qqmlcomponent.h"
        name: "QQmlComponent"
        prototype: "QObject"
        exports: ["QML/Component 1.0", "QtQml/Component 2.0"]
        exportMetaObjectRevisions: [0, 0]
        attachedType: "QQmlComponentAttached"
        Enum {
            name: "CompilationMode"
            values: ["PreferSynchronous", "Asynchronous"]
        }
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property { name: "progress"; type: "double"; isReadonly: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "url"; type: "QUrl"; isReadonly: true }
        Signal {
            name: "statusChanged"
            Parameter { type: "QQmlComponent::Status" }
        }
        Signal {
            name: "progressChanged"
            Parameter { type: "double" }
        }
        Method {
            name: "loadUrl"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "loadUrl"
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "mode"; type: "CompilationMode" }
        }
        Method {
            name: "setData"
            Parameter { type: "QByteArray" }
            Parameter { name: "baseUrl"; type: "QUrl" }
        }
        Method { name: "errorString"; type: "string" }
        Method {
            name: "createObject"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "incubateObject"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
    }
    Component {
        file: "private/qqmlcomponentattached_p.h"
        name: "QQmlComponentAttached"
        prototype: "QObject"
        Signal { name: "completed" }
        Signal { name: "destruction" }
    }
    Component {
        file: "private/qqmlconnections_p.h"
        name: "QQmlConnections"
        prototype: "QObject"
        exports: ["QtQml/Connections 2.0", "QtQml/Connections 2.3"]
        exportMetaObjectRevisions: [0, 3]
        Property { name: "target"; type: "QObject"; isPointer: true }
        Property { name: "enabled"; revision: 3; type: "bool" }
        Property { name: "ignoreUnknownSignals"; type: "bool" }
        Signal { name: "enabledChanged"; revision: 3 }
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlEasingValueType"
        exports: ["QtQml/Easing 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Type"
            values: [
                "Linear",
                "InQuad",
                "OutQuad",
                "InOutQuad",
                "OutInQuad",
                "InCubic",
                "OutCubic",
                "InOutCubic",
                "OutInCubic",
                "InQuart",
                "OutQuart",
                "InOutQuart",
                "OutInQuart",
                "InQuint",
                "OutQuint",
                "InOutQuint",
                "OutInQuint",
                "InSine",
                "OutSine",
                "InOutSine",
                "OutInSine",
                "InExpo",
                "OutExpo",
                "InOutExpo",
                "OutInExpo",
                "InCirc",
                "OutCirc",
                "InOutCirc",
                "OutInCirc",
                "InElastic",
                "OutElastic",
                "InOutElastic",
                "OutInElastic",
                "InBack",
                "OutBack",
                "InOutBack",
                "OutInBack",
                "InBounce",
                "OutBounce",
                "InOutBounce",
                "OutInBounce",
                "InCurve",
                "OutCurve",
                "SineCurve",
                "CosineCurve",
                "Bezier"
            ]
        }
        Property { name: "type"; type: "QQmlEasingValueType::Type" }
        Property { name: "amplitude"; type: "double" }
        Property { name: "overshoot"; type: "double" }
        Property { name: "period"; type: "double" }
        Property { name: "bezierCurve"; type: "QVariantList" }
    }
    Component {
        file: "private/qqmllocale_p.h"
        name: "QQmlLocale"
        exports: ["QtQml/Locale 2.2"]
        isCreatable: false
        exportMetaObjectRevisions: [2]
        Enum {
            name: "MeasurementSystem"
            values: [
                "MetricSystem",
                "ImperialSystem",
                "ImperialUSSystem",
                "ImperialUKSystem"
            ]
        }
        Enum {
            name: "FormatType"
            values: ["LongFormat", "ShortFormat", "NarrowFormat"]
        }
        Enum {
            name: "CurrencySymbolFormat"
            values: [
                "CurrencyIsoCode",
                "CurrencySymbol",
                "CurrencyDisplayName"
            ]
        }
        Enum {
            name: "DayOfWeek"
            values: [
                "Sunday",
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday"
            ]
        }
        Enum {
            name: "NumberOptions"
            values: [
                "DefaultNumberOptions",
                "OmitGroupSeparator",
                "RejectGroupSeparator",
                "OmitLeadingZeroInExponent",
                "RejectLeadingZeroInExponent",
                "IncludeTrailingZeroesAfterDot",
                "RejectTrailingZeroesAfterDot"
            ]
        }
    }
    Component {
        file: "private/qqmlloggingcategory_p.h"
        name: "QQmlLoggingCategory"
        prototype: "QObject"
        exports: ["QtQml/LoggingCategory 2.12", "QtQml/LoggingCategory 2.8"]
        exportMetaObjectRevisions: [12, 8]
        Enum {
            name: "DefaultLogLevel"
            values: ["Debug", "Info", "Warning", "Critical", "Fatal"]
        }
        Property { name: "name"; type: "string" }
        Property { name: "defaultLogLevel"; revision: 12; type: "DefaultLogLevel" }
    }
    Component {
        file: "private/qqmltimer_p.h"
        name: "QQmlTimer"
        prototype: "QObject"
        exports: ["QtQml/Timer 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "interval"; type: "int" }
        Property { name: "running"; type: "bool" }
        Property { name: "repeat"; type: "bool" }
        Property { name: "triggeredOnStart"; type: "bool" }
        Property { name: "parent"; type: "QObject"; isReadonly: true; isPointer: true }
        Signal { name: "triggered" }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "restart" }
        Method { name: "ticked" }
    }
    Component {
        file: "private/qqmltypenotavailable_p.h"
        name: "QQmlTypeNotAvailable"
        prototype: "QObject"
        exports: ["QtQml/TypeNotAvailable 2.15"]
        isCreatable: false
        exportMetaObjectRevisions: [15]
    }
}
