import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: []
    Component {
        file: "qquickboundaryrule_p.h"
        name: "QQuickBoundaryRule"
        prototype: "QObject"
        exports: ["Qt.labs.animation/BoundaryRule 1.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "OvershootFilter"
            values: ["None", "Peak"]
        }
        Property { name: "enabled"; type: "bool" }
        Property { name: "minimum"; type: "double" }
        Property { name: "minimumOvershoot"; type: "double" }
        Property { name: "maximum"; type: "double" }
        Property { name: "maximumOvershoot"; type: "double" }
        Property { name: "overshootScale"; type: "double" }
        Property { name: "currentOvershoot"; type: "double"; isReadonly: true }
        Property { name: "peakOvershoot"; type: "double"; isReadonly: true }
        Property { name: "overshootFilter"; type: "OvershootFilter" }
        Property { name: "easing"; type: "QEasingCurve" }
        Property { name: "returnDuration"; type: "int" }
        Method { name: "componentFinalized" }
        Method { name: "returnToBounds"; type: "bool" }
    }
}
