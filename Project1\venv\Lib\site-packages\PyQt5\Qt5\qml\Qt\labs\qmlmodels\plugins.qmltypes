import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: []
    Component {
        name: "QAbstractItemModel"
        prototype: "QObject"
        Enum {
            name: "LayoutChangeHint"
            values: [
                "NoLayoutChangeHint",
                "VerticalSortHint",
                "HorizontalSortHint"
            ]
        }
        Enum {
            name: "CheckIndexOption"
            values: [
                "NoOption",
                "IndexIsValid",
                "DoNotUseParent",
                "ParentIsInvalid"
            ]
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "QVector<int>" }
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutChanged" }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QList<QPersistentModelIndex>" }
        }
        Signal { name: "layoutAboutToBeChanged" }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "row"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
            Parameter { name: "destination"; type: "QModelIndex" }
            Parameter { name: "column"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method { name: "resetInternalData" }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int" }
        Method {
            name: "columnCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int" }
        Method {
            name: "hasChildren"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool" }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component { name: "QAbstractTableModel"; prototype: "QAbstractItemModel" }
    Component {
        file: "qqmldelegatecomponent_p.h"
        name: "QQmlDelegateChoice"
        defaultProperty: "delegate"
        prototype: "QObject"
        exports: ["Qt.labs.qmlmodels/DelegateChoice 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "roleValue"; type: "QVariant" }
        Property { name: "row"; type: "int" }
        Property { name: "index"; type: "int" }
        Property { name: "column"; type: "int" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Signal { name: "changed" }
    }
    Component {
        file: "qqmldelegatecomponent_p.h"
        name: "QQmlDelegateChooser"
        defaultProperty: "choices"
        prototype: "QQmlAbstractDelegateComponent"
        exports: ["Qt.labs.qmlmodels/DelegateChooser 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "role"; type: "string" }
        Property { name: "choices"; type: "QQmlDelegateChoice"; isList: true; isReadonly: true }
    }
    Component {
        file: "qqmltablemodel_p.h"
        name: "QQmlTableModel"
        defaultProperty: "columns"
        prototype: "QAbstractTableModel"
        exports: ["Qt.labs.qmlmodels/TableModel 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "columnCount"; type: "int"; isReadonly: true }
        Property { name: "rowCount"; type: "int"; isReadonly: true }
        Property { name: "rows"; type: "QVariant" }
        Property { name: "columns"; type: "QQmlTableModelColumn"; isList: true; isReadonly: true }
        Method {
            name: "appendRow"
            Parameter { name: "row"; type: "QVariant" }
        }
        Method { name: "clear" }
        Method {
            name: "getRow"
            type: "QVariant"
            Parameter { name: "rowIndex"; type: "int" }
        }
        Method {
            name: "insertRow"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "row"; type: "QVariant" }
        }
        Method {
            name: "moveRow"
            Parameter { name: "fromRowIndex"; type: "int" }
            Parameter { name: "toRowIndex"; type: "int" }
            Parameter { name: "rows"; type: "int" }
        }
        Method {
            name: "moveRow"
            Parameter { name: "fromRowIndex"; type: "int" }
            Parameter { name: "toRowIndex"; type: "int" }
        }
        Method {
            name: "removeRow"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "rows"; type: "int" }
        }
        Method {
            name: "removeRow"
            Parameter { name: "rowIndex"; type: "int" }
        }
        Method {
            name: "setRow"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "row"; type: "QVariant" }
        }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "string" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "string" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "qqmltablemodelcolumn_p.h"
        name: "QQmlTableModelColumn"
        prototype: "QObject"
        exports: ["Qt.labs.qmlmodels/TableModelColumn 1.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "display"; type: "QJSValue" }
        Property { name: "setDisplay"; type: "QJSValue" }
        Property { name: "decoration"; type: "QJSValue" }
        Property { name: "setDecoration"; type: "QJSValue" }
        Property { name: "edit"; type: "QJSValue" }
        Property { name: "setEdit"; type: "QJSValue" }
        Property { name: "toolTip"; type: "QJSValue" }
        Property { name: "setToolTip"; type: "QJSValue" }
        Property { name: "statusTip"; type: "QJSValue" }
        Property { name: "setStatusTip"; type: "QJSValue" }
        Property { name: "whatsThis"; type: "QJSValue" }
        Property { name: "setWhatsThis"; type: "QJSValue" }
        Property { name: "font"; type: "QJSValue" }
        Property { name: "setFont"; type: "QJSValue" }
        Property { name: "textAlignment"; type: "QJSValue" }
        Property { name: "setTextAlignment"; type: "QJSValue" }
        Property { name: "background"; type: "QJSValue" }
        Property { name: "setBackground"; type: "QJSValue" }
        Property { name: "foreground"; type: "QJSValue" }
        Property { name: "setForeground"; type: "QJSValue" }
        Property { name: "checkState"; type: "QJSValue" }
        Property { name: "setCheckState"; type: "QJSValue" }
        Property { name: "accessibleText"; type: "QJSValue" }
        Property { name: "setAccessibleText"; type: "QJSValue" }
        Property { name: "accessibleDescription"; type: "QJSValue" }
        Property { name: "setAccessibleDescription"; type: "QJSValue" }
        Property { name: "sizeHint"; type: "QJSValue" }
        Property { name: "setSizeHint"; type: "QJSValue" }
        Signal { name: "indexChanged" }
    }
}
