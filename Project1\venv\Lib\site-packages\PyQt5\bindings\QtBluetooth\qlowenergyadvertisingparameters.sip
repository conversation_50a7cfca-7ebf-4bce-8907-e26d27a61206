// qlowenergyadvertisingparameters.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_7_0 -)

class QLowEnergyAdvertisingParameters
{
%TypeHeaderCode
#include <qlowenergyadvertisingparameters.h>
%End

public:
    QLowEnergyAdvertisingParameters();
    QLowEnergyAdvertisingParameters(const QLowEnergyAdvertisingParameters &other);
    ~QLowEnergyAdvertisingParameters();

    enum Mode
    {
        AdvInd,
        AdvScanInd,
        AdvNonConnInd,
    };

    void setMode(QLowEnergyAdvertisingParameters::Mode mode);
    QLowEnergyAdvertisingParameters::Mode mode() const;

    struct AddressInfo
    {
%TypeHeaderCode
#include <qlowenergyadvertisingparameters.h>
%End

        AddressInfo(const QBluetoothAddress &addr, QLowEnergyController::RemoteAddressType t);
        AddressInfo();
        QBluetoothAddress address;
        QLowEnergyController::RemoteAddressType type;
    };

    enum FilterPolicy
    {
        IgnoreWhiteList,
        UseWhiteListForScanning,
        UseWhiteListForConnecting,
        UseWhiteListForScanningAndConnecting,
    };

    void setWhiteList(const QList<QLowEnergyAdvertisingParameters::AddressInfo> &whiteList, QLowEnergyAdvertisingParameters::FilterPolicy policy);
    QList<QLowEnergyAdvertisingParameters::AddressInfo> whiteList() const;
    QLowEnergyAdvertisingParameters::FilterPolicy filterPolicy() const;
    void setInterval(quint16 minimum, quint16 maximum);
    int minimumInterval() const;
    int maximumInterval() const;
    void swap(QLowEnergyAdvertisingParameters &other);
};

%End
%If (Qt_5_7_0 -)
bool operator==(const QLowEnergyAdvertisingParameters &p1, const QLowEnergyAdvertisingParameters &p2);
%End
%If (Qt_5_7_0 -)
bool operator==(const QLowEnergyAdvertisingParameters::AddressInfo &ai1, const QLowEnergyAdvertisingParameters::AddressInfo &ai2);
%End
%If (Qt_5_7_0 -)
bool operator!=(const QLowEnergyAdvertisingParameters &p1, const QLowEnergyAdvertisingParameters &p2);
%End
