// qcborcommon.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_12_0 -)
%ModuleCode
#include <qcborcommon.h>
%End
%End

%If (Qt_5_12_0 -)

enum class QCborSimpleType
{
    False /PyName=False_/,
    True /PyName=True_/,
    Null,
    Undefined,
};

%End
%If (Qt_5_12_0 -)

struct QCborError
{
%TypeHeaderCode
#include <qcborcommon.h>
%End

    enum Code
    {
        UnknownError,
        AdvancePastEnd,
        InputOutputError,
        GarbageAtEnd,
        EndOfFile,
        UnexpectedBreak,
        UnknownType,
        IllegalType,
        IllegalNumber,
        IllegalSimpleType,
        InvalidUtf8String,
        DataTooLarge,
        NestingTooDeep,
        UnsupportedType,
        NoError,
    };

// Error code access
// This class is currently undocumented.  Access to the error code is via a
// cast (which SIP doesn't support) or a badly named instance variable.  To be
// safe we implement a more Qt-typical solution.
QCborError::Code code() const;
%MethodCode
    sipRes = sipCpp->c;
%End
    QString toString() const;
};

%End
%If (Qt_5_12_0 -)

enum class QCborKnownTags
{
    DateTimeString,
    UnixTime_t,
    PositiveBignum,
    NegativeBignum,
    Decimal,
    Bigfloat,
    COSE_Encrypt0,
    COSE_Mac0,
    COSE_Sign1,
    ExpectedBase64url,
    ExpectedBase64,
    ExpectedBase16,
    EncodedCbor,
    Url,
    Base64url,
    Base64,
    RegularExpression,
    MimeMessage,
    Uuid,
    COSE_Encrypt,
    COSE_Mac,
    COSE_Sign,
    Signature,
};

%End
