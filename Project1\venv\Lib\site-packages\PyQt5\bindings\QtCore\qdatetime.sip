// qdatetime.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDate /TypeHintIn="Union[QDate, datetime.date]"/
{
%TypeHeaderCode
#include <qdatetime.h>
%End

%TypeCode
#include <QHash>
%End

%ConvertToTypeCode
// Allow a Python date object whenever a QDate is expected.

if (sipIsErr == NULL)
    return (sipGetDate(sipPy, 0) ||
            sipCanConvertToType(sipPy, sipType_QDate, SIP_NO_CONVERTORS));

sipDateDef py_date;

if (sipGetDate(sipPy, &py_date))
{
    *sipCppPtr = new QDate(py_date.pd_year,
                           py_date.pd_month,
                           py_date.pd_day);

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QDate *>(sipConvertToType(sipPy, sipType_QDate, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"iii", sipCpp->year(), sipCpp->month(), sipCpp->day());
%End

public:
    QDate();
    QDate(int y, int m, int d);
%If (Qt_5_14_0 -)
    QDate(int y, int m, int d, QCalendar cal);
%End
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QDate()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QDate()");
        #endif
        }
        else
        {
            sipRes =
        #if PY_MAJOR_VERSION >= 3
                PyUnicode_FromFormat
        #else
                PyString_FromFormat
        #endif
                    ("PyQt5.QtCore.QDate(%i, %i, %i)", sipCpp->year(),
                    sipCpp->month(), sipCpp->day());
        }
%End

    long __hash__() const;
%MethodCode
        sipRes = qHash(sipCpp->toString(Qt::ISODate));
%End

    SIP_PYOBJECT toPyDate() const /TypeHint="datetime.date"/;
%MethodCode
        // Convert to a Python date object.
        sipDateDef py_date;
        
        py_date.pd_year = sipCpp->year();
        py_date.pd_month = sipCpp->month();
        py_date.pd_day = sipCpp->day();
        
        sipRes = sipFromDate(&py_date);
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    bool isValid() const;
    int year() const;
%If (Qt_5_14_0 -)
    int year(QCalendar cal) const;
%End
    int month() const;
%If (Qt_5_14_0 -)
    int month(QCalendar cal) const;
%End
    int day() const;
%If (Qt_5_14_0 -)
    int day(QCalendar cal) const;
%End
    int dayOfWeek() const;
%If (Qt_5_14_0 -)
    int dayOfWeek(QCalendar cal) const;
%End
    int dayOfYear() const;
%If (Qt_5_14_0 -)
    int dayOfYear(QCalendar cal) const;
%End
    int daysInMonth() const;
%If (Qt_5_14_0 -)
    int daysInMonth(QCalendar cal) const;
%End
    int daysInYear() const;
%If (Qt_5_14_0 -)
    int daysInYear(QCalendar cal) const;
%End
    int weekNumber(int *yearNumber = 0) const;
    static QString shortMonthName(int month, QDate::MonthNameType type = QDate::DateFormat);
    static QString shortDayName(int weekday, QDate::MonthNameType type = QDate::DateFormat);
    static QString longMonthName(int month, QDate::MonthNameType type = QDate::DateFormat);
    static QString longDayName(int weekday, QDate::MonthNameType type = QDate::DateFormat);
    QString toString(Qt::DateFormat format = Qt::TextDate) const;
%If (Qt_5_14_0 -)
    QString toString(Qt::DateFormat f, QCalendar cal) const;
%End
    QString toString(const QString &format) const;
%If (Qt_5_14_0 -)
    QString toString(const QString &format, QCalendar cal) const;
%End
    QDate addDays(qint64 days) const;
    QDate addMonths(int months) const;
%If (Qt_5_14_0 -)
    QDate addMonths(int months, QCalendar cal) const;
%End
    QDate addYears(int years) const;
%If (Qt_5_14_0 -)
    QDate addYears(int years, QCalendar cal) const;
%End
    qint64 daysTo(const QDate &) const;
    bool operator==(const QDate &other) const;
    bool operator!=(const QDate &other) const;
    bool operator<(const QDate &other) const;
    bool operator<=(const QDate &other) const;
    bool operator>(const QDate &other) const;
    bool operator>=(const QDate &other) const;
    static QDate currentDate();
    static QDate fromString(const QString &string, Qt::DateFormat format = Qt::TextDate);
    static QDate fromString(const QString &s, const QString &format);
%If (Qt_5_14_0 -)
    static QDate fromString(const QString &s, const QString &format, QCalendar cal);
%End
    static bool isValid(int y, int m, int d);
    static bool isLeapYear(int year);
    static QDate fromJulianDay(qint64 jd);
    qint64 toJulianDay() const;
    bool setDate(int year, int month, int date);
%If (- Qt_5_7_0)
%If (Qt_5_15_0 -)
    void getDate(int *year, int *month, int *day);
%End
%End
%If (Qt_5_7_0 -)
    void getDate(int *year, int *month, int *day) const;
%End

    enum MonthNameType
    {
        DateFormat,
        StandaloneFormat,
    };

%If (Qt_5_14_0 -)
    QDateTime startOfDay(Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0) const;
%End
%If (Qt_5_14_0 -)
    QDateTime endOfDay(Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0) const;
%End
%If (Qt_5_14_0 -)
    QDateTime startOfDay(const QTimeZone &zone) const;
%End
%If (Qt_5_14_0 -)
    QDateTime endOfDay(const QTimeZone &zone) const;
%End
%If (Qt_5_14_0 -)
    bool setDate(int year, int month, int day, QCalendar cal);
%End
};

class QTime /TypeHintIn="Union[QTime, datetime.time]"/
{
%TypeHeaderCode
#include <qdatetime.h>
%End

%TypeCode
#include <QHash>
%End

%ConvertToTypeCode
// Allow a Python time object whenever a QTime is expected.

if (sipIsErr == NULL)
    return (sipGetTime(sipPy, 0) ||
            sipCanConvertToType(sipPy, sipType_QTime, SIP_NO_CONVERTORS));

sipTimeDef py_time;

if (sipGetTime(sipPy, &py_time))
{
    *sipCppPtr = new QTime(py_time.pt_hour,
                           py_time.pt_minute,
                           py_time.pt_second,
                           py_time.pt_microsecond / 1000);

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QTime *>(sipConvertToType(sipPy, sipType_QTime, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"iiii", sipCpp->hour(), sipCpp->minute(), sipCpp->second(), sipCpp->msec());
%End

public:
    QTime();
    QTime(int h, int m, int second = 0, int msec = 0);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QTime()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QTime()");
        #endif
        }
        else
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QTime(%i, %i", sipCpp->hour(),
                    sipCpp->minute());
        
            if (sipCpp->second() || sipCpp->msec())
            {
                qpycore_Unicode_ConcatAndDel(&sipRes,
                        PyUnicode_FromFormat(", %i", sipCpp->second()));
        
                if (sipCpp->msec())
                    qpycore_Unicode_ConcatAndDel(&sipRes,
                            PyUnicode_FromFormat(", %i", sipCpp->msec()));
            }
        
            qpycore_Unicode_ConcatAndDel(&sipRes, PyUnicode_FromString(")"));
        #else
            sipRes = PyString_FromFormat("PyQt5.QtCore.QTime(%i, %i", sipCpp->hour(),
                    sipCpp->minute());
        
            if (sipCpp->second() || sipCpp->msec())
            {
                PyString_ConcatAndDel(&sipRes,
                        PyString_FromFormat(", %i", sipCpp->second()));
        
                if (sipCpp->msec())
                    PyString_ConcatAndDel(&sipRes,
                            PyString_FromFormat(", %i", sipCpp->msec()));
            }
        
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        }
%End

    long __hash__() const;
%MethodCode
        sipRes = qHash(sipCpp->toString(Qt::ISODate));
%End

    SIP_PYOBJECT toPyTime() const /TypeHint="datetime.time"/;
%MethodCode
        // Convert to a Python time object.
        sipTimeDef py_time;
        
        py_time.pt_hour = sipCpp->hour();
        py_time.pt_minute = sipCpp->minute();
        py_time.pt_second = sipCpp->second();
        py_time.pt_microsecond = sipCpp->msec() * 1000;
        
        sipRes = sipFromTime(&py_time);
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    bool isValid() const;
    int hour() const;
    int minute() const;
    int second() const;
    int msec() const;
    QString toString(Qt::DateFormat format = Qt::TextDate) const;
    QString toString(const QString &format) const;
    bool setHMS(int h, int m, int s, int msec = 0);
    QTime addSecs(int secs) const;
    int secsTo(const QTime &) const;
    QTime addMSecs(int ms) const;
    int msecsTo(const QTime &) const;
    bool operator==(const QTime &other) const;
    bool operator!=(const QTime &other) const;
    bool operator<(const QTime &other) const;
    bool operator<=(const QTime &other) const;
    bool operator>(const QTime &other) const;
    bool operator>=(const QTime &other) const;
    static QTime currentTime();
    static QTime fromString(const QString &string, Qt::DateFormat format = Qt::TextDate);
    static QTime fromString(const QString &s, const QString &format);
    static bool isValid(int h, int m, int s, int msec = 0);
    void start();
    int restart();
    int elapsed() const;
%If (Qt_5_2_0 -)
    static QTime fromMSecsSinceStartOfDay(int msecs);
%End
%If (Qt_5_2_0 -)
    int msecsSinceStartOfDay() const;
%End
};

class QDateTime /TypeHintIn="Union[QDateTime, datetime.datetime]"/
{
%TypeHeaderCode
#include <qdatetime.h>
%End

%TypeCode
#include <QHash>
%End

%ConvertToTypeCode
// Allow a Python datetime object whenever a QDateTime is expected.

if (sipIsErr == NULL)
    return (sipGetDateTime(sipPy, 0, 0) ||
            sipCanConvertToType(sipPy, sipType_QDateTime, SIP_NO_CONVERTORS));

sipDateDef py_date;
sipTimeDef py_time;

if (sipGetDateTime(sipPy, &py_date, &py_time))
{
    QDate qdate(py_date.pd_year,
                py_date.pd_month,
                py_date.pd_day);

    QTime qtime(py_time.pt_hour,
                py_time.pt_minute,
                py_time.pt_second,
                py_time.pt_microsecond / 1000);

    QDateTime *qdt = new QDateTime(qdate, qtime);

    *sipCppPtr = qdt;

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QDateTime *>(sipConvertToType(sipPy, sipType_QDateTime, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

%PickleCode
    QDate qd = sipCpp->date();
    QTime qt = sipCpp->time();
    
    sipRes = Py_BuildValue((char *)"iiiiiiii", qd.year(), qd.month(), qd.day(),
            qt.hour(), qt.minute(), qt.second(), qt.msec(),
            (int)sipCpp->timeSpec());
%End

public:
    QDateTime();
    QDateTime(const QDateTime &other);
    explicit QDateTime(const QDate &);
    QDateTime(const QDate &date, const QTime &time, Qt::TimeSpec timeSpec = Qt::LocalTime);
    QDateTime(int year, int month, int day, int hour, int minute, int second = 0, int msec = 0, int timeSpec = 0) /NoDerived/;
%MethodCode
        // This ctor is mainly supplied to allow pickling.
        QDate qd(a0, a1, a2);
        QTime qt(a3, a4, a5, a6);
        
        sipCpp = new QDateTime(qd, qt, (Qt::TimeSpec)a7);
%End

    ~QDateTime();
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QDateTime()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QDateTime()");
        #endif
        }
        else
        {
            QDate qd = sipCpp->date();
            QTime qt = sipCpp->time();
        
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QDateTime(%i, %i, %i, %i, %i",
                    qd.year(), qd.month(), qd.day(), qt.hour(), qt.minute());
        
            if (qt.second() || qt.msec() || sipCpp->timeSpec() != Qt::LocalTime)
            {
                qpycore_Unicode_ConcatAndDel(&sipRes,
                        PyUnicode_FromFormat(", %i", qt.second()));
        
                if (qt.msec() || sipCpp->timeSpec() != Qt::LocalTime)
                {
                    qpycore_Unicode_ConcatAndDel(&sipRes,
                            PyUnicode_FromFormat(", %i", qt.msec()));
        
                    if (sipCpp->timeSpec() != Qt::LocalTime)
                        qpycore_Unicode_ConcatAndDel(&sipRes,
                                PyUnicode_FromFormat(", PyQt5.QtCore.Qt.TimeSpec(%i)",
                                        (int)sipCpp->timeSpec()));
                }
            }
        
            qpycore_Unicode_ConcatAndDel(&sipRes, PyUnicode_FromString(")"));
        #else
            sipRes = PyString_FromFormat("PyQt5.QtCore.QDateTime(%i, %i, %i, %i, %i",
                    qd.year(), qd.month(), qd.day(), qt.hour(), qt.minute());
        
            if (qt.second() || qt.msec() || sipCpp->timeSpec() != Qt::LocalTime)
            {
                PyString_ConcatAndDel(&sipRes,
                        PyString_FromFormat(", %i", qt.second()));
        
                if (qt.msec() || sipCpp->timeSpec() != Qt::LocalTime)
                {
                    PyString_ConcatAndDel(&sipRes,
                            PyString_FromFormat(", %i", qt.msec()));
        
                    if (sipCpp->timeSpec() != Qt::LocalTime)
                        PyString_ConcatAndDel(&sipRes,
                                PyString_FromFormat(", PyQt5.QtCore.Qt.TimeSpec(%i)",
                                        (int)sipCpp->timeSpec()));
                }
            }
        
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        }
%End

    long __hash__() const;
%MethodCode
        sipRes = qHash(sipCpp->toString(Qt::ISODate));
%End

    SIP_PYOBJECT toPyDateTime() const /TypeHint="datetime.datetime"/;
%MethodCode
        // Convert to a Python datetime object.
        sipDateDef py_date;
        QDate qd = sipCpp->date();
        
        py_date.pd_year = qd.year();
        py_date.pd_month = qd.month();
        py_date.pd_day = qd.day();
        
        sipTimeDef py_time;
        QTime qt = sipCpp->time();
        
        py_time.pt_hour = qt.hour();
        py_time.pt_minute = qt.minute();
        py_time.pt_second = qt.second();
        py_time.pt_microsecond = qt.msec() * 1000;
        
        sipRes = sipFromDateTime(&py_date, &py_time);
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    bool isValid() const;
    QDate date() const;
    QTime time() const;
    Qt::TimeSpec timeSpec() const;
    uint toTime_t() const;
    void setDate(const QDate &date);
    void setTime(const QTime &time);
    void setTimeSpec(Qt::TimeSpec spec);
    void setTime_t(uint secsSince1Jan1970UTC);
    QString toString(Qt::DateFormat format = Qt::TextDate) const;
    QString toString(const QString &format) const;
    QDateTime addDays(qint64 days) const;
    QDateTime addMonths(int months) const;
    QDateTime addYears(int years) const;
    QDateTime addSecs(qint64 secs) const;
    QDateTime addMSecs(qint64 msecs) const;
    QDateTime toTimeSpec(Qt::TimeSpec spec) const;
    QDateTime toLocalTime() const;
    QDateTime toUTC() const;
    qint64 daysTo(const QDateTime &) const;
    qint64 secsTo(const QDateTime &) const;
    bool operator==(const QDateTime &other) const;
    bool operator!=(const QDateTime &other) const;
    bool operator<(const QDateTime &other) const;
    bool operator<=(const QDateTime &other) const;
    bool operator>(const QDateTime &other) const;
    bool operator>=(const QDateTime &other) const;
    static QDateTime currentDateTime();
    static QDateTime fromString(const QString &string, Qt::DateFormat format = Qt::TextDate);
    static QDateTime fromString(const QString &s, const QString &format);
    static QDateTime fromTime_t(uint secsSince1Jan1970UTC);
    qint64 toMSecsSinceEpoch() const;
    void setMSecsSinceEpoch(qint64 msecs);
    qint64 msecsTo(const QDateTime &) const;
    static QDateTime currentDateTimeUtc();
    static QDateTime fromMSecsSinceEpoch(qint64 msecs);
    static qint64 currentMSecsSinceEpoch();
    void swap(QDateTime &other /Constrained/);
%If (Qt_5_2_0 -)
    QDateTime(const QDate &date, const QTime &time, Qt::TimeSpec spec, int offsetSeconds);
%End
%If (Qt_5_2_0 -)
    QDateTime(const QDate &date, const QTime &time, const QTimeZone &timeZone);
%End
%If (Qt_5_2_0 -)
    int offsetFromUtc() const;
%End
%If (Qt_5_2_0 -)
    QTimeZone timeZone() const;
%End
%If (Qt_5_2_0 -)
    QString timeZoneAbbreviation() const;
%End
%If (Qt_5_2_0 -)
    bool isDaylightTime() const;
%End
%If (Qt_5_2_0 -)
    void setOffsetFromUtc(int offsetSeconds);
%End
%If (Qt_5_2_0 -)
    void setTimeZone(const QTimeZone &toZone);
%End
%If (Qt_5_2_0 -)
    QDateTime toOffsetFromUtc(int offsetSeconds) const;
%End
%If (Qt_5_2_0 -)
    QDateTime toTimeZone(const QTimeZone &toZone) const;
%End
%If (Qt_5_2_0 -)
    static QDateTime fromTime_t(uint secsSince1Jan1970UTC, Qt::TimeSpec spec, int offsetSeconds = 0);
%End
%If (Qt_5_2_0 -)
    static QDateTime fromTime_t(uint secsSince1Jan1970UTC, const QTimeZone &timeZone);
%End
%If (Qt_5_2_0 -)
    static QDateTime fromMSecsSinceEpoch(qint64 msecs, Qt::TimeSpec spec, int offsetSeconds = 0);
%End
%If (Qt_5_2_0 -)
    static QDateTime fromMSecsSinceEpoch(qint64 msecs, const QTimeZone &timeZone);
%End
%If (Qt_5_8_0 -)
    qint64 toSecsSinceEpoch() const;
%End
%If (Qt_5_8_0 -)
    void setSecsSinceEpoch(qint64 secs);
%End
%If (Qt_5_8_0 -)
    static QDateTime fromSecsSinceEpoch(qint64 secs, Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0);
%End
%If (Qt_5_8_0 -)
    static QDateTime fromSecsSinceEpoch(qint64 secs, const QTimeZone &timeZone);
%End
%If (Qt_5_8_0 -)
    static qint64 currentSecsSinceEpoch();
%End
%If (Qt_5_14_0 -)
    static QDateTime fromString(const QString &s, const QString &format, QCalendar cal);
%End
%If (Qt_5_14_0 -)

    enum class YearRange
    {
        First,
        Last,
    };

%End
%If (Qt_5_15_0 -)
    QString toString(const QString &format, QCalendar cal) const;
%End
};

QDataStream &operator<<(QDataStream &, const QDate & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QDate & /Constrained/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &, const QTime & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QTime & /Constrained/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &, const QDateTime & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QDateTime & /Constrained/) /ReleaseGIL/;
