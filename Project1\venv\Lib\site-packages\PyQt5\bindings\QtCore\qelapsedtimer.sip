// qelapsedtimer.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QElapsedTimer
{
%TypeHeaderCode
#include <qelapsedtimer.h>
%End

public:
%If (Qt_5_4_0 -)
    QElapsedTimer();
%End

    enum ClockType
    {
        SystemTime,
        MonotonicClock,
        TickCounter,
        MachAbsoluteTime,
        PerformanceCounter,
    };

    static QElapsedTimer::ClockType clockType();
    static bool isMonotonic();
    void start();
    qint64 restart();
    void invalidate();
    bool isValid() const;
    qint64 elapsed() const;
    bool hasExpired(qint64 timeout) const;
    qint64 msecsSinceReference() const;
    qint64 msecsTo(const QElapsedTimer &other) const;
    qint64 secsTo(const QElapsedTimer &other) const;
    bool operator==(const QElapsedTimer &other) const;
    bool operator!=(const QElapsedTimer &other) const;
    qint64 nsecsElapsed() const;
};

bool operator<(const QElapsedTimer &v1, const QElapsedTimer &v2);
