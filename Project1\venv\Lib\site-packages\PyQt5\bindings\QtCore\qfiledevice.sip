// qfiledevice.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFileDevice : public QIODevice /NoDefaultCtors/
{
%TypeHeaderCode
#include <qfiledevice.h>
%End

public:
    enum FileError
    {
        NoE<PERSON>r,
        <PERSON><PERSON><PERSON>r,
        <PERSON><PERSON><PERSON>rror,
        FatalError,
        ResourceError,
        OpenError,
        AbortError,
        TimeOutError,
        UnspecifiedError,
        RemoveError,
        RenameError,
        PositionError,
        ResizeError,
        PermissionsError,
        CopyError,
    };

    enum Permission
    {
        ReadOwner,
        WriteOwner,
        ExeOwner,
        ReadUser,
        WriteUser,
        ExeUser,
        ReadGroup,
        WriteGroup,
        ExeGroup,
        ReadOther,
        WriteOther,
        ExeOther,
    };

    typedef QFlags<QFileDevice::Permission> Permissions;

    enum FileHandleFlag
    {
        AutoCloseHandle,
        DontCloseHandle,
    };

    typedef QFlags<QFileDevice::FileHandleFlag> FileHandleFlags;
    virtual ~QFileDevice();
    QFileDevice::FileError error() const;
    void unsetError();
    virtual void close() /ReleaseGIL/;
    virtual bool isSequential() const;
    int handle() const;
    virtual QString fileName() const;
    virtual qint64 pos() const;
    virtual bool seek(qint64 offset) /ReleaseGIL/;
    virtual bool atEnd() const;
    bool flush() /ReleaseGIL/;
    virtual qint64 size() const;
    virtual bool resize(qint64 sz);
    virtual QFileDevice::Permissions permissions() const;
    virtual bool setPermissions(QFileDevice::Permissions permissionSpec);

    enum MemoryMapFlags
    {
        NoOptions,
%If (Qt_5_4_0 -)
        MapPrivateOption,
%End
    };

    void *map(qint64 offset, qint64 size /ResultSize/, QFileDevice::MemoryMapFlags flags = QFileDevice::NoOptions) [uchar * (qint64 offset, qint64 size, QFileDevice::MemoryMapFlags flags = QFileDevice::NoOptions)];
    bool unmap(void *address) [bool (uchar *address)];

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QFileDevice::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 len /ArraySize/) /ReleaseGIL/;
    virtual SIP_PYOBJECT readLineData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QFileDevice::readLineData(s, a0) : sipCpp->readLineData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readLineData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

public:
%If (Qt_5_10_0 -)

    enum FileTime
    {
        FileAccessTime,
        FileBirthTime,
        FileMetadataChangeTime,
        FileModificationTime,
    };

%End
%If (Qt_5_10_0 -)
    QDateTime fileTime(QFileDevice::FileTime time) const;
%End
%If (Qt_5_10_0 -)
    bool setFileTime(const QDateTime &newDate, QFileDevice::FileTime fileTime);
%End
};

QFlags<QFileDevice::Permission> operator|(QFileDevice::Permission f1, QFlags<QFileDevice::Permission> f2);
