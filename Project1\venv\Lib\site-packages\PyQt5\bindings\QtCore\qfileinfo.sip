// qfileinfo.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFileInfo
{
%TypeHeaderCode
#include <qfileinfo.h>
%End

public:
    QFileInfo();
    QFileInfo(const QString &file);
    QFileInfo(const QFile &file);
    QFileInfo(const QDir &dir, const QString &file);
    QFileInfo(const QFileInfo &fileinfo);
    ~QFileInfo();
    bool operator==(const QFileInfo &fileinfo) const;
    bool operator!=(const QFileInfo &fileinfo) const;
    void setFile(const QString &file);
    void setFile(const QFile &file);
    void setFile(const QDir &dir, const QString &file);
    bool exists() const;
    void refresh();
    QString filePath() const;
    SIP_PYOBJECT __fspath__();
%MethodCode
        sipRes = qpycore_PyObject_FromQString(QDir::toNativeSeparators(sipCpp->filePath()));
%End

    QString absoluteFilePath() const;
    QString canonicalFilePath() const;
    QString fileName() const;
    QString baseName() const;
    QString completeBaseName() const;
    QString suffix() const;
    QString completeSuffix() const;
    QString path() const;
    QString absolutePath() const;
    QString canonicalPath() const;
    QDir dir() const;
    QDir absoluteDir() const;
    bool isReadable() const;
    bool isWritable() const;
    bool isExecutable() const;
    bool isHidden() const;
    bool isRelative() const;
    bool isAbsolute() const;
    bool makeAbsolute();
    bool isFile() const;
    bool isDir() const;
    bool isSymLink() const;
    bool isRoot() const;
    QString owner() const;
    uint ownerId() const;
    QString group() const;
    uint groupId() const;
    bool permission(QFileDevice::Permissions permissions) const;
    QFileDevice::Permissions permissions() const;
    qint64 size() const;
    QDateTime created() const;
    QDateTime lastModified() const;
    QDateTime lastRead() const;
    bool caching() const;
    void setCaching(bool on);
    QString symLinkTarget() const;
    QString bundleName() const;
    bool isBundle() const;
    bool isNativePath() const;
    void swap(QFileInfo &other /Constrained/);
%If (Qt_5_2_0 -)
    static bool exists(const QString &file);
%End
%If (Qt_5_10_0 -)
    QDateTime birthTime() const;
%End
%If (Qt_5_10_0 -)
    QDateTime metadataChangeTime() const;
%End
%If (Qt_5_10_0 -)
    QDateTime fileTime(QFileDevice::FileTime time) const;
%End
%If (Qt_5_14_0 -)
    bool isSymbolicLink() const;
%End
%If (Qt_5_14_0 -)
    bool isShortcut() const;
%End
%If (Qt_5_15_0 -)
    bool isJunction() const;
%End
};

typedef QList<QFileInfo> QFileInfoList;
