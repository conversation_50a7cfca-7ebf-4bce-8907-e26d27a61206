// qnamespace.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace Qt
{
%TypeHeaderCode
#include <qnamespace.h>
%End

    enum GlobalColor
    {
        color0,
        color1,
        black,
        white,
        darkGray,
        gray,
        lightGray,
        red,
        green,
        blue,
        cyan,
        magenta,
        yellow,
        darkRed,
        darkGreen,
        darkBlue,
        darkCyan,
        darkMagenta,
        darkYellow,
        transparent,
    };

    enum KeyboardModifier
    {
        NoModifier,
        ShiftModifier,
        ControlModifier,
        AltModifier,
        MetaModifier,
        KeypadModifier,
        GroupSwitchModifier,
        KeyboardModifierMask,
    };

    typedef QFlags<Qt::KeyboardModifier> KeyboardModifiers;

    enum Modifier
    {
        META,
        SHIFT,
        CTRL,
        ALT,
        MODIFIER_MASK,
        UNICODE_ACCEL,
    };

    enum MouseButton
    {
        NoButton,
        AllButtons,
        LeftButton,
        RightButton,
        MidButton,
        MiddleButton,
        XButton1,
        XButton2,
        BackButton,
        ExtraButton1,
        ForwardButton,
        ExtraButton2,
        TaskButton,
        ExtraButton3,
        ExtraButton4,
        ExtraButton5,
        ExtraButton6,
        ExtraButton7,
        ExtraButton8,
        ExtraButton9,
        ExtraButton10,
        ExtraButton11,
        ExtraButton12,
        ExtraButton13,
        ExtraButton14,
        ExtraButton15,
        ExtraButton16,
        ExtraButton17,
        ExtraButton18,
        ExtraButton19,
        ExtraButton20,
        ExtraButton21,
        ExtraButton22,
        ExtraButton23,
        ExtraButton24,
    };

    typedef QFlags<Qt::MouseButton> MouseButtons;

    enum Orientation
    {
        Horizontal,
        Vertical,
    };

    typedef QFlags<Qt::Orientation> Orientations;

    enum FocusPolicy
    {
        NoFocus,
        TabFocus,
        ClickFocus,
        StrongFocus,
        WheelFocus,
    };

    enum SortOrder
    {
        AscendingOrder,
        DescendingOrder,
    };

    enum AlignmentFlag
    {
        AlignLeft,
        AlignLeading,
        AlignRight,
        AlignTrailing,
        AlignHCenter,
        AlignJustify,
        AlignAbsolute,
        AlignHorizontal_Mask,
        AlignTop,
        AlignBottom,
        AlignVCenter,
        AlignVertical_Mask,
        AlignCenter,
%If (Qt_5_2_0 -)
        AlignBaseline,
%End
    };

    typedef QFlags<Qt::AlignmentFlag> Alignment;

    enum TextFlag
    {
        TextSingleLine,
        TextDontClip,
        TextExpandTabs,
        TextShowMnemonic,
        TextWordWrap,
        TextWrapAnywhere,
        TextDontPrint,
        TextIncludeTrailingSpaces,
        TextHideMnemonic,
        TextJustificationForced,
    };

    enum TextElideMode
    {
        ElideLeft,
        ElideRight,
        ElideMiddle,
        ElideNone,
    };

    enum WindowType
    {
        Widget,
        Window,
        Dialog,
        Sheet,
        Drawer,
        Popup,
        Tool,
        ToolTip,
        SplashScreen,
        Desktop,
        SubWindow,
        WindowType_Mask,
        MSWindowsFixedSizeDialogHint,
        MSWindowsOwnDC,
        X11BypassWindowManagerHint,
        FramelessWindowHint,
        CustomizeWindowHint,
        WindowTitleHint,
        WindowSystemMenuHint,
        WindowMinimizeButtonHint,
        WindowMaximizeButtonHint,
        WindowMinMaxButtonsHint,
        WindowContextHelpButtonHint,
        WindowShadeButtonHint,
        WindowStaysOnTopHint,
%If (- Qt_5_8_0)
        WindowOkButtonHint,
%End
%If (- Qt_5_8_0)
        WindowCancelButtonHint,
%End
        WindowStaysOnBottomHint,
        WindowCloseButtonHint,
        MacWindowToolBarButtonHint,
        BypassGraphicsProxyWidget,
        WindowTransparentForInput,
        WindowOverridesSystemGestures,
        WindowDoesNotAcceptFocus,
        NoDropShadowWindowHint,
        WindowFullscreenButtonHint,
%If (Qt_5_1_0 -)
        ForeignWindow,
%End
%If (Qt_5_1_0 -)
        BypassWindowManagerHint,
%End
%If (Qt_5_2_0 -)
        CoverWindow,
%End
%If (Qt_5_5_0 -)
        MaximizeUsingFullscreenGeometryHint,
%End
    };

    typedef QFlags<Qt::WindowType> WindowFlags;

    enum WindowState
    {
        WindowNoState,
        WindowMinimized,
        WindowMaximized,
        WindowFullScreen,
        WindowActive,
    };

    typedef QFlags<Qt::WindowState> WindowStates;

    enum WidgetAttribute
    {
        WA_Disabled,
        WA_UnderMouse,
        WA_MouseTracking,
        WA_OpaquePaintEvent,
        WA_StaticContents,
        WA_LaidOut,
        WA_PaintOnScreen,
        WA_NoSystemBackground,
        WA_UpdatesDisabled,
        WA_Mapped,
        WA_MacNoClickThrough,
        WA_InputMethodEnabled,
        WA_WState_Visible,
        WA_WState_Hidden,
        WA_ForceDisabled,
        WA_KeyCompression,
        WA_PendingMoveEvent,
        WA_PendingResizeEvent,
        WA_SetPalette,
        WA_SetFont,
        WA_SetCursor,
        WA_NoChildEventsFromChildren,
        WA_WindowModified,
        WA_Resized,
        WA_Moved,
        WA_PendingUpdate,
        WA_InvalidSize,
        WA_MacMetalStyle,
        WA_CustomWhatsThis,
        WA_LayoutOnEntireRect,
        WA_OutsideWSRange,
        WA_GrabbedShortcut,
        WA_TransparentForMouseEvents,
        WA_PaintUnclipped,
        WA_SetWindowIcon,
        WA_NoMouseReplay,
        WA_DeleteOnClose,
        WA_RightToLeft,
        WA_SetLayoutDirection,
        WA_NoChildEventsForParent,
        WA_ForceUpdatesDisabled,
        WA_WState_Created,
        WA_WState_CompressKeys,
        WA_WState_InPaintEvent,
        WA_WState_Reparented,
        WA_WState_ConfigPending,
        WA_WState_Polished,
        WA_WState_OwnSizePolicy,
        WA_WState_ExplicitShowHide,
        WA_MouseNoMask,
        WA_GroupLeader,
        WA_NoMousePropagation,
        WA_Hover,
        WA_InputMethodTransparent,
        WA_QuitOnClose,
        WA_KeyboardFocusChange,
        WA_AcceptDrops,
        WA_WindowPropagation,
        WA_NoX11EventCompression,
        WA_TintedBackground,
        WA_X11OpenGLOverlay,
        WA_AttributeCount,
        WA_AlwaysShowToolTips,
        WA_MacOpaqueSizeGrip,
        WA_SetStyle,
        WA_MacBrushedMetal,
        WA_SetLocale,
        WA_MacShowFocusRect,
        WA_MacNormalSize,
        WA_MacSmallSize,
        WA_MacMiniSize,
        WA_LayoutUsesWidgetRect,
        WA_StyledBackground,
        WA_MSWindowsUseDirect3D,
        WA_MacAlwaysShowToolWindow,
        WA_StyleSheet,
        WA_ShowWithoutActivating,
        WA_NativeWindow,
        WA_DontCreateNativeAncestors,
        WA_MacVariableSize,
        WA_DontShowOnScreen,
        WA_X11NetWmWindowTypeDesktop,
        WA_X11NetWmWindowTypeDock,
        WA_X11NetWmWindowTypeToolBar,
        WA_X11NetWmWindowTypeMenu,
        WA_X11NetWmWindowTypeUtility,
        WA_X11NetWmWindowTypeSplash,
        WA_X11NetWmWindowTypeDialog,
        WA_X11NetWmWindowTypeDropDownMenu,
        WA_X11NetWmWindowTypePopupMenu,
        WA_X11NetWmWindowTypeToolTip,
        WA_X11NetWmWindowTypeNotification,
        WA_X11NetWmWindowTypeCombo,
        WA_X11NetWmWindowTypeDND,
        WA_MacFrameworkScaled,
        WA_TranslucentBackground,
        WA_AcceptTouchEvents,
        WA_TouchPadAcceptSingleTouchEvents,
        WA_X11DoNotAcceptFocus,
        WA_MacNoShadow,
%If (Qt_5_4_0 -)
        WA_AlwaysStackOnTop,
%End
%If (Qt_5_9_0 -)
        WA_TabletTracking,
%End
%If (Qt_5_11_0 -)
        WA_ContentsMarginsRespectsSafeArea,
%End
%If (Qt_5_12_0 -)
        WA_StyleSheetTarget,
%End
    };

    enum ImageConversionFlag
    {
        AutoColor,
        ColorOnly,
        MonoOnly,
        ThresholdAlphaDither,
        OrderedAlphaDither,
        DiffuseAlphaDither,
        DiffuseDither,
        OrderedDither,
        ThresholdDither,
        AutoDither,
        PreferDither,
        AvoidDither,
        NoOpaqueDetection,
        NoFormatConversion,
    };

    typedef QFlags<Qt::ImageConversionFlag> ImageConversionFlags;

    enum BGMode
    {
        TransparentMode,
        OpaqueMode,
    };

    enum Key
    {
        Key_Escape,
        Key_Tab,
        Key_Backtab,
        Key_Backspace,
        Key_Return,
        Key_Enter,
        Key_Insert,
        Key_Delete,
        Key_Pause,
        Key_Print,
        Key_SysReq,
        Key_Clear,
        Key_Home,
        Key_End,
        Key_Left,
        Key_Up,
        Key_Right,
        Key_Down,
        Key_PageUp,
        Key_PageDown,
        Key_Shift,
        Key_Control,
        Key_Meta,
        Key_Alt,
        Key_CapsLock,
        Key_NumLock,
        Key_ScrollLock,
        Key_F1,
        Key_F2,
        Key_F3,
        Key_F4,
        Key_F5,
        Key_F6,
        Key_F7,
        Key_F8,
        Key_F9,
        Key_F10,
        Key_F11,
        Key_F12,
        Key_F13,
        Key_F14,
        Key_F15,
        Key_F16,
        Key_F17,
        Key_F18,
        Key_F19,
        Key_F20,
        Key_F21,
        Key_F22,
        Key_F23,
        Key_F24,
        Key_F25,
        Key_F26,
        Key_F27,
        Key_F28,
        Key_F29,
        Key_F30,
        Key_F31,
        Key_F32,
        Key_F33,
        Key_F34,
        Key_F35,
        Key_Super_L,
        Key_Super_R,
        Key_Menu,
        Key_Hyper_L,
        Key_Hyper_R,
        Key_Help,
        Key_Direction_L,
        Key_Direction_R,
        Key_Space,
        Key_Any,
        Key_Exclam,
        Key_QuoteDbl,
        Key_NumberSign,
        Key_Dollar,
        Key_Percent,
        Key_Ampersand,
        Key_Apostrophe,
        Key_ParenLeft,
        Key_ParenRight,
        Key_Asterisk,
        Key_Plus,
        Key_Comma,
        Key_Minus,
        Key_Period,
        Key_Slash,
        Key_0,
        Key_1,
        Key_2,
        Key_3,
        Key_4,
        Key_5,
        Key_6,
        Key_7,
        Key_8,
        Key_9,
        Key_Colon,
        Key_Semicolon,
        Key_Less,
        Key_Equal,
        Key_Greater,
        Key_Question,
        Key_At,
        Key_A,
        Key_B,
        Key_C,
        Key_D,
        Key_E,
        Key_F,
        Key_G,
        Key_H,
        Key_I,
        Key_J,
        Key_K,
        Key_L,
        Key_M,
        Key_N,
        Key_O,
        Key_P,
        Key_Q,
        Key_R,
        Key_S,
        Key_T,
        Key_U,
        Key_V,
        Key_W,
        Key_X,
        Key_Y,
        Key_Z,
        Key_BracketLeft,
        Key_Backslash,
        Key_BracketRight,
        Key_AsciiCircum,
        Key_Underscore,
        Key_QuoteLeft,
        Key_BraceLeft,
        Key_Bar,
        Key_BraceRight,
        Key_AsciiTilde,
        Key_nobreakspace,
        Key_exclamdown,
        Key_cent,
        Key_sterling,
        Key_currency,
        Key_yen,
        Key_brokenbar,
        Key_section,
        Key_diaeresis,
        Key_copyright,
        Key_ordfeminine,
        Key_guillemotleft,
        Key_notsign,
        Key_hyphen,
        Key_registered,
        Key_macron,
        Key_degree,
        Key_plusminus,
        Key_twosuperior,
        Key_threesuperior,
        Key_acute,
        Key_mu,
        Key_paragraph,
        Key_periodcentered,
        Key_cedilla,
        Key_onesuperior,
        Key_masculine,
        Key_guillemotright,
        Key_onequarter,
        Key_onehalf,
        Key_threequarters,
        Key_questiondown,
        Key_Agrave,
        Key_Aacute,
        Key_Acircumflex,
        Key_Atilde,
        Key_Adiaeresis,
        Key_Aring,
        Key_AE,
        Key_Ccedilla,
        Key_Egrave,
        Key_Eacute,
        Key_Ecircumflex,
        Key_Ediaeresis,
        Key_Igrave,
        Key_Iacute,
        Key_Icircumflex,
        Key_Idiaeresis,
        Key_ETH,
        Key_Ntilde,
        Key_Ograve,
        Key_Oacute,
        Key_Ocircumflex,
        Key_Otilde,
        Key_Odiaeresis,
        Key_multiply,
        Key_Ooblique,
        Key_Ugrave,
        Key_Uacute,
        Key_Ucircumflex,
        Key_Udiaeresis,
        Key_Yacute,
        Key_THORN,
        Key_ssharp,
        Key_division,
        Key_ydiaeresis,
        Key_AltGr,
        Key_Multi_key,
        Key_Codeinput,
        Key_SingleCandidate,
        Key_MultipleCandidate,
        Key_PreviousCandidate,
        Key_Mode_switch,
        Key_Kanji,
        Key_Muhenkan,
        Key_Henkan,
        Key_Romaji,
        Key_Hiragana,
        Key_Katakana,
        Key_Hiragana_Katakana,
        Key_Zenkaku,
        Key_Hankaku,
        Key_Zenkaku_Hankaku,
        Key_Touroku,
        Key_Massyo,
        Key_Kana_Lock,
        Key_Kana_Shift,
        Key_Eisu_Shift,
        Key_Eisu_toggle,
        Key_Hangul,
        Key_Hangul_Start,
        Key_Hangul_End,
        Key_Hangul_Hanja,
        Key_Hangul_Jamo,
        Key_Hangul_Romaja,
        Key_Hangul_Jeonja,
        Key_Hangul_Banja,
        Key_Hangul_PreHanja,
        Key_Hangul_PostHanja,
        Key_Hangul_Special,
        Key_Dead_Grave,
        Key_Dead_Acute,
        Key_Dead_Circumflex,
        Key_Dead_Tilde,
        Key_Dead_Macron,
        Key_Dead_Breve,
        Key_Dead_Abovedot,
        Key_Dead_Diaeresis,
        Key_Dead_Abovering,
        Key_Dead_Doubleacute,
        Key_Dead_Caron,
        Key_Dead_Cedilla,
        Key_Dead_Ogonek,
        Key_Dead_Iota,
        Key_Dead_Voiced_Sound,
        Key_Dead_Semivoiced_Sound,
        Key_Dead_Belowdot,
        Key_Dead_Hook,
        Key_Dead_Horn,
        Key_Back,
        Key_Forward,
        Key_Stop,
        Key_Refresh,
        Key_VolumeDown,
        Key_VolumeMute,
        Key_VolumeUp,
        Key_BassBoost,
        Key_BassUp,
        Key_BassDown,
        Key_TrebleUp,
        Key_TrebleDown,
        Key_MediaPlay,
        Key_MediaStop,
        Key_MediaPrevious,
        Key_MediaNext,
        Key_MediaRecord,
        Key_HomePage,
        Key_Favorites,
        Key_Search,
        Key_Standby,
        Key_OpenUrl,
        Key_LaunchMail,
        Key_LaunchMedia,
        Key_Launch0,
        Key_Launch1,
        Key_Launch2,
        Key_Launch3,
        Key_Launch4,
        Key_Launch5,
        Key_Launch6,
        Key_Launch7,
        Key_Launch8,
        Key_Launch9,
        Key_LaunchA,
        Key_LaunchB,
        Key_LaunchC,
        Key_LaunchD,
        Key_LaunchE,
        Key_LaunchF,
        Key_MediaLast,
        Key_Select,
        Key_Yes,
        Key_No,
        Key_Context1,
        Key_Context2,
        Key_Context3,
        Key_Context4,
        Key_Call,
        Key_Hangup,
        Key_Flip,
        Key_unknown,
        Key_Execute,
        Key_Printer,
        Key_Play,
        Key_Sleep,
        Key_Zoom,
        Key_Cancel,
        Key_MonBrightnessUp,
        Key_MonBrightnessDown,
        Key_KeyboardLightOnOff,
        Key_KeyboardBrightnessUp,
        Key_KeyboardBrightnessDown,
        Key_PowerOff,
        Key_WakeUp,
        Key_Eject,
        Key_ScreenSaver,
        Key_WWW,
        Key_Memo,
        Key_LightBulb,
        Key_Shop,
        Key_History,
        Key_AddFavorite,
        Key_HotLinks,
        Key_BrightnessAdjust,
        Key_Finance,
        Key_Community,
        Key_AudioRewind,
        Key_BackForward,
        Key_ApplicationLeft,
        Key_ApplicationRight,
        Key_Book,
        Key_CD,
        Key_Calculator,
        Key_ToDoList,
        Key_ClearGrab,
        Key_Close,
        Key_Copy,
        Key_Cut,
        Key_Display,
        Key_DOS,
        Key_Documents,
        Key_Excel,
        Key_Explorer,
        Key_Game,
        Key_Go,
        Key_iTouch,
        Key_LogOff,
        Key_Market,
        Key_Meeting,
        Key_MenuKB,
        Key_MenuPB,
        Key_MySites,
        Key_News,
        Key_OfficeHome,
        Key_Option,
        Key_Paste,
        Key_Phone,
        Key_Calendar,
        Key_Reply,
        Key_Reload,
        Key_RotateWindows,
        Key_RotationPB,
        Key_RotationKB,
        Key_Save,
        Key_Send,
        Key_Spell,
        Key_SplitScreen,
        Key_Support,
        Key_TaskPane,
        Key_Terminal,
        Key_Tools,
        Key_Travel,
        Key_Video,
        Key_Word,
        Key_Xfer,
        Key_ZoomIn,
        Key_ZoomOut,
        Key_Away,
        Key_Messenger,
        Key_WebCam,
        Key_MailForward,
        Key_Pictures,
        Key_Music,
        Key_Battery,
        Key_Bluetooth,
        Key_WLAN,
        Key_UWB,
        Key_AudioForward,
        Key_AudioRepeat,
        Key_AudioRandomPlay,
        Key_Subtitle,
        Key_AudioCycleTrack,
        Key_Time,
        Key_Hibernate,
        Key_View,
        Key_TopMenu,
        Key_PowerDown,
        Key_Suspend,
        Key_ContrastAdjust,
        Key_MediaPause,
        Key_MediaTogglePlayPause,
        Key_LaunchG,
        Key_LaunchH,
        Key_ToggleCallHangup,
        Key_VoiceDial,
        Key_LastNumberRedial,
        Key_Camera,
        Key_CameraFocus,
        Key_TouchpadToggle,
        Key_TouchpadOn,
        Key_TouchpadOff,
%If (Qt_5_1_0 -)
        Key_MicMute,
%End
%If (Qt_5_2_0 -)
        Key_Red,
%End
%If (Qt_5_2_0 -)
        Key_Green,
%End
%If (Qt_5_2_0 -)
        Key_Yellow,
%End
%If (Qt_5_2_0 -)
        Key_Blue,
%End
%If (Qt_5_2_0 -)
        Key_ChannelUp,
%End
%If (Qt_5_2_0 -)
        Key_ChannelDown,
%End
%If (Qt_5_3_0 -)
        Key_Guide,
%End
%If (Qt_5_3_0 -)
        Key_Info,
%End
%If (Qt_5_3_0 -)
        Key_Settings,
%End
%If (Qt_5_3_0 -)
        Key_Exit,
%End
%If (Qt_5_4_0 -)
        Key_MicVolumeUp,
%End
%If (Qt_5_4_0 -)
        Key_MicVolumeDown,
%End
%If (Qt_5_4_0 -)
        Key_New,
%End
%If (Qt_5_4_0 -)
        Key_Open,
%End
%If (Qt_5_4_0 -)
        Key_Find,
%End
%If (Qt_5_4_0 -)
        Key_Undo,
%End
%If (Qt_5_4_0 -)
        Key_Redo,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Stroke,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Abovecomma,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Abovereversedcomma,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Doublegrave,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Belowring,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Belowmacron,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Belowcircumflex,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Belowtilde,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Belowbreve,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Belowdiaeresis,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Invertedbreve,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Belowcomma,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Currency,
%End
%If (Qt_5_11_0 -)
        Key_Dead_a,
%End
%If (Qt_5_11_0 -)
        Key_Dead_A,
%End
%If (Qt_5_11_0 -)
        Key_Dead_e,
%End
%If (Qt_5_11_0 -)
        Key_Dead_E,
%End
%If (Qt_5_11_0 -)
        Key_Dead_i,
%End
%If (Qt_5_11_0 -)
        Key_Dead_I,
%End
%If (Qt_5_11_0 -)
        Key_Dead_o,
%End
%If (Qt_5_11_0 -)
        Key_Dead_O,
%End
%If (Qt_5_11_0 -)
        Key_Dead_u,
%End
%If (Qt_5_11_0 -)
        Key_Dead_U,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Small_Schwa,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Capital_Schwa,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Greek,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Lowline,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Aboveverticalline,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Belowverticalline,
%End
%If (Qt_5_11_0 -)
        Key_Dead_Longsolidusoverlay,
%End
    };

    enum ArrowType
    {
        NoArrow,
        UpArrow,
        DownArrow,
        LeftArrow,
        RightArrow,
    };

    enum PenStyle
    {
        NoPen,
        SolidLine,
        DashLine,
        DotLine,
        DashDotLine,
        DashDotDotLine,
        CustomDashLine,
        MPenStyle,
    };

    enum PenCapStyle
    {
        FlatCap,
        SquareCap,
        RoundCap,
        MPenCapStyle,
    };

    enum PenJoinStyle
    {
        MiterJoin,
        BevelJoin,
        RoundJoin,
        MPenJoinStyle,
        SvgMiterJoin,
    };

    enum BrushStyle
    {
        NoBrush,
        SolidPattern,
        Dense1Pattern,
        Dense2Pattern,
        Dense3Pattern,
        Dense4Pattern,
        Dense5Pattern,
        Dense6Pattern,
        Dense7Pattern,
        HorPattern,
        VerPattern,
        CrossPattern,
        BDiagPattern,
        FDiagPattern,
        DiagCrossPattern,
        LinearGradientPattern,
        RadialGradientPattern,
        ConicalGradientPattern,
        TexturePattern,
    };

    enum UIEffect
    {
        UI_General,
        UI_AnimateMenu,
        UI_FadeMenu,
        UI_AnimateCombo,
        UI_AnimateTooltip,
        UI_FadeTooltip,
        UI_AnimateToolBox,
    };

    enum CursorShape
    {
        ArrowCursor,
        UpArrowCursor,
        CrossCursor,
        WaitCursor,
        IBeamCursor,
        SizeVerCursor,
        SizeHorCursor,
        SizeBDiagCursor,
        SizeFDiagCursor,
        SizeAllCursor,
        BlankCursor,
        SplitVCursor,
        SplitHCursor,
        PointingHandCursor,
        ForbiddenCursor,
        OpenHandCursor,
        ClosedHandCursor,
        WhatsThisCursor,
        BusyCursor,
        LastCursor,
        BitmapCursor,
        CustomCursor,
        DragCopyCursor,
        DragMoveCursor,
        DragLinkCursor,
    };

    enum TextFormat
    {
        PlainText,
        RichText,
        AutoText,
%If (Qt_5_14_0 -)
        MarkdownText,
%End
    };

    enum AspectRatioMode
    {
        IgnoreAspectRatio,
        KeepAspectRatio,
        KeepAspectRatioByExpanding,
    };

    enum DockWidgetArea
    {
        LeftDockWidgetArea,
        RightDockWidgetArea,
        TopDockWidgetArea,
        BottomDockWidgetArea,
        DockWidgetArea_Mask,
        AllDockWidgetAreas,
        NoDockWidgetArea,
    };

    typedef QFlags<Qt::DockWidgetArea> DockWidgetAreas;

    enum TimerType
    {
        PreciseTimer,
        CoarseTimer,
        VeryCoarseTimer,
    };

    enum ToolBarArea
    {
        LeftToolBarArea,
        RightToolBarArea,
        TopToolBarArea,
        BottomToolBarArea,
        ToolBarArea_Mask,
        AllToolBarAreas,
        NoToolBarArea,
    };

    typedef QFlags<Qt::ToolBarArea> ToolBarAreas;

    enum DateFormat
    {
        TextDate,
        ISODate,
%If (Qt_5_8_0 -)
        ISODateWithMs,
%End
        LocalDate,
        SystemLocaleDate,
        LocaleDate,
        SystemLocaleShortDate,
        SystemLocaleLongDate,
        DefaultLocaleShortDate,
        DefaultLocaleLongDate,
%If (Qt_5_2_0 -)
        RFC2822Date,
%End
    };

    enum TimeSpec
    {
        LocalTime,
        UTC,
        OffsetFromUTC,
%If (Qt_5_2_0 -)
        TimeZone,
%End
    };

    enum DayOfWeek
    {
        Monday,
        Tuesday,
        Wednesday,
        Thursday,
        Friday,
        Saturday,
        Sunday,
    };

    enum ScrollBarPolicy
    {
        ScrollBarAsNeeded,
        ScrollBarAlwaysOff,
        ScrollBarAlwaysOn,
    };

    enum CaseSensitivity
    {
        CaseInsensitive,
        CaseSensitive,
    };

    enum Corner
    {
        TopLeftCorner,
        TopRightCorner,
        BottomLeftCorner,
        BottomRightCorner,
    };

    enum ConnectionType
    {
        AutoConnection,
        DirectConnection,
        QueuedConnection,
        BlockingQueuedConnection,
        UniqueConnection,
    };

    enum ShortcutContext
    {
        WidgetShortcut,
        WindowShortcut,
        ApplicationShortcut,
        WidgetWithChildrenShortcut,
    };

    enum FillRule
    {
        OddEvenFill,
        WindingFill,
    };

    enum ClipOperation
    {
        NoClip,
        ReplaceClip,
        IntersectClip,
    };

    enum TransformationMode
    {
        FastTransformation,
        SmoothTransformation,
    };

    enum FocusReason
    {
        MouseFocusReason,
        TabFocusReason,
        BacktabFocusReason,
        ActiveWindowFocusReason,
        PopupFocusReason,
        ShortcutFocusReason,
        MenuBarFocusReason,
        OtherFocusReason,
        NoFocusReason,
    };

    enum ContextMenuPolicy
    {
        NoContextMenu,
        PreventContextMenu,
        DefaultContextMenu,
        ActionsContextMenu,
        CustomContextMenu,
    };

    enum InputMethodQuery
    {
        ImMicroFocus,
        ImFont,
        ImCursorPosition,
        ImSurroundingText,
        ImCurrentSelection,
        ImMaximumTextLength,
        ImAnchorPosition,
        ImEnabled,
        ImCursorRectangle,
        ImHints,
        ImPreferredLanguage,
        ImPlatformData,
        ImQueryInput,
        ImQueryAll,
%If (Qt_5_3_0 -)
        ImAbsolutePosition,
%End
%If (Qt_5_3_0 -)
        ImTextBeforeCursor,
%End
%If (Qt_5_3_0 -)
        ImTextAfterCursor,
%End
%If (Qt_5_6_0 -)
        ImEnterKeyType,
%End
%If (Qt_5_7_0 -)
        ImAnchorRectangle,
%End
%If (Qt_5_7_0 -)
        ImInputItemClipRectangle,
%End
    };

    typedef QFlags<Qt::InputMethodQuery> InputMethodQueries;

    enum ToolButtonStyle
    {
        ToolButtonIconOnly,
        ToolButtonTextOnly,
        ToolButtonTextBesideIcon,
        ToolButtonTextUnderIcon,
        ToolButtonFollowStyle,
    };

    enum LayoutDirection
    {
        LeftToRight,
        RightToLeft,
        LayoutDirectionAuto,
    };

    enum DropAction
    {
        CopyAction,
        MoveAction,
        LinkAction,
        ActionMask,
        TargetMoveAction,
        IgnoreAction,
    };

    typedef QFlags<Qt::DropAction> DropActions;

    enum CheckState
    {
        Unchecked,
        PartiallyChecked,
        Checked,
    };

    enum ItemDataRole
    {
        DisplayRole,
        DecorationRole,
        EditRole,
        ToolTipRole,
        StatusTipRole,
        WhatsThisRole,
        FontRole,
        TextAlignmentRole,
        BackgroundRole,
        BackgroundColorRole,
        ForegroundRole,
        TextColorRole,
        CheckStateRole,
        AccessibleTextRole,
        AccessibleDescriptionRole,
        SizeHintRole,
        InitialSortOrderRole,
        UserRole,
    };

    enum ItemFlag
    {
        NoItemFlags,
        ItemIsSelectable,
        ItemIsEditable,
        ItemIsDragEnabled,
        ItemIsDropEnabled,
        ItemIsUserCheckable,
        ItemIsEnabled,
        ItemIsTristate,
%If (Qt_5_1_0 -)
        ItemNeverHasChildren,
%End
%If (Qt_5_5_0 -)
        ItemIsUserTristate,
%End
%If (Qt_5_6_0 -)
        ItemIsAutoTristate,
%End
    };

    typedef QFlags<Qt::ItemFlag> ItemFlags;

    enum MatchFlag
    {
        MatchExactly,
        MatchFixedString,
        MatchContains,
        MatchStartsWith,
        MatchEndsWith,
        MatchRegExp,
        MatchWildcard,
        MatchCaseSensitive,
        MatchWrap,
        MatchRecursive,
%If (Qt_5_15_0 -)
        MatchRegularExpression,
%End
    };

    typedef QFlags<Qt::MatchFlag> MatchFlags;
    typedef void *HANDLE;

    enum WindowModality
    {
        NonModal,
        WindowModal,
        ApplicationModal,
    };

    enum ApplicationAttribute
    {
        AA_ImmediateWidgetCreation,
        AA_MSWindowsUseDirect3DByDefault,
        AA_DontShowIconsInMenus,
        AA_NativeWindows,
        AA_DontCreateNativeWidgetSiblings,
        AA_MacPluginApplication,
        AA_DontUseNativeMenuBar,
        AA_MacDontSwapCtrlAndMeta,
        AA_X11InitThreads,
        AA_Use96Dpi,
        AA_SynthesizeTouchForUnhandledMouseEvents,
        AA_SynthesizeMouseForUnhandledTouchEvents,
%If (Qt_5_1_0 -)
        AA_UseHighDpiPixmaps,
%End
%If (Qt_5_3_0 -)
        AA_ForceRasterWidgets,
%End
%If (Qt_5_3_0 -)
        AA_UseDesktopOpenGL,
%End
%If (Qt_5_3_0 -)
        AA_UseOpenGLES,
%End
%If (Qt_5_4_0 -)
        AA_UseSoftwareOpenGL,
%End
%If (Qt_5_4_0 -)
        AA_ShareOpenGLContexts,
%End
%If (Qt_5_5_0 -)
        AA_SetPalette,
%End
%If (Qt_5_6_0 -)
        AA_EnableHighDpiScaling,
%End
%If (Qt_5_6_0 -)
        AA_DisableHighDpiScaling,
%End
%If (Qt_5_7_0 -)
        AA_PluginApplication,
%End
%If (Qt_5_7_0 -)
        AA_UseStyleSheetPropagationInWidgetStyles,
%End
%If (Qt_5_7_0 -)
        AA_DontUseNativeDialogs,
%End
%If (Qt_5_7_0 -)
        AA_SynthesizeMouseForUnhandledTabletEvents,
%End
%If (Qt_5_7_0 -)
        AA_CompressHighFrequencyEvents,
%End
%If (Qt_5_8_0 -)
        AA_DontCheckOpenGLContextThreadAffinity,
%End
%If (Qt_5_9_0 -)
        AA_DisableShaderDiskCache,
%End
%If (Qt_5_10_0 -)
        AA_DontShowShortcutsInContextMenus,
%End
%If (Qt_5_10_0 -)
        AA_CompressTabletEvents,
%End
%If (Qt_5_10_0 -)
        AA_DisableWindowContextHelpButton,
%End
%If (Qt_5_14_0 -)
        AA_DisableSessionManager,
%End
%If (Qt_5_15_0 -)
        AA_DisableNativeVirtualKeyboard,
%End
    };

    enum ItemSelectionMode
    {
        ContainsItemShape,
        IntersectsItemShape,
        ContainsItemBoundingRect,
        IntersectsItemBoundingRect,
    };

    enum TextInteractionFlag
    {
        NoTextInteraction,
        TextSelectableByMouse,
        TextSelectableByKeyboard,
        LinksAccessibleByMouse,
        LinksAccessibleByKeyboard,
        TextEditable,
        TextEditorInteraction,
        TextBrowserInteraction,
    };

    typedef QFlags<Qt::TextInteractionFlag> TextInteractionFlags;

    enum MaskMode
    {
        MaskInColor,
        MaskOutColor,
    };

    enum Axis
    {
        XAxis,
        YAxis,
        ZAxis,
    };

    enum EventPriority
    {
        HighEventPriority,
        NormalEventPriority,
        LowEventPriority,
    };

    enum SizeMode
    {
        AbsoluteSize,
        RelativeSize,
    };

    enum SizeHint
    {
        MinimumSize,
        PreferredSize,
        MaximumSize,
        MinimumDescent,
    };

    enum WindowFrameSection
    {
        NoSection,
        LeftSection,
        TopLeftSection,
        TopSection,
        TopRightSection,
        RightSection,
        BottomRightSection,
        BottomSection,
        BottomLeftSection,
        TitleBarArea,
    };

    enum TileRule
    {
        StretchTile,
        RepeatTile,
        RoundTile,
    };

    enum InputMethodHint
    {
        ImhNone,
        ImhHiddenText,
        ImhNoAutoUppercase,
        ImhPreferNumbers,
        ImhPreferUppercase,
        ImhPreferLowercase,
        ImhNoPredictiveText,
        ImhDigitsOnly,
        ImhFormattedNumbersOnly,
        ImhUppercaseOnly,
        ImhLowercaseOnly,
        ImhDialableCharactersOnly,
        ImhEmailCharactersOnly,
        ImhUrlCharactersOnly,
        ImhExclusiveInputMask,
        ImhSensitiveData,
        ImhDate,
        ImhTime,
        ImhPreferLatin,
        ImhLatinOnly,
%If (Qt_5_1_0 -)
        ImhMultiLine,
%End
%If (Qt_5_11_0 -)
        ImhNoEditMenu,
%End
%If (Qt_5_11_0 -)
        ImhNoTextHandles,
%End
    };

    typedef QFlags<Qt::InputMethodHint> InputMethodHints;

    enum AnchorPoint
    {
        AnchorLeft,
        AnchorHorizontalCenter,
        AnchorRight,
        AnchorTop,
        AnchorVerticalCenter,
        AnchorBottom,
    };

    enum CoordinateSystem
    {
        DeviceCoordinates,
        LogicalCoordinates,
    };

    enum TouchPointState
    {
        TouchPointPressed,
        TouchPointMoved,
        TouchPointStationary,
        TouchPointReleased,
    };

    typedef QFlags<Qt::TouchPointState> TouchPointStates;

    enum GestureState
    {
        GestureStarted,
        GestureUpdated,
        GestureFinished,
        GestureCanceled,
    };

    enum GestureType
    {
        TapGesture,
        TapAndHoldGesture,
        PanGesture,
        PinchGesture,
        SwipeGesture,
        CustomGesture,
    };

    enum GestureFlag
    {
        DontStartGestureOnChildren,
        ReceivePartialGestures,
        IgnoredGesturesPropagateToParent,
    };

    typedef QFlags<Qt::GestureFlag> GestureFlags;

    enum NavigationMode
    {
        NavigationModeNone,
        NavigationModeKeypadTabOrder,
        NavigationModeKeypadDirectional,
        NavigationModeCursorAuto,
        NavigationModeCursorForceVisible,
    };

    enum CursorMoveStyle
    {
        LogicalMoveStyle,
        VisualMoveStyle,
    };

    enum ScreenOrientation
    {
        PrimaryOrientation,
        PortraitOrientation,
        LandscapeOrientation,
        InvertedPortraitOrientation,
        InvertedLandscapeOrientation,
    };

    typedef QFlags<Qt::ScreenOrientation> ScreenOrientations;

    enum FindChildOption
    {
        FindDirectChildrenOnly,
        FindChildrenRecursively,
    };

    typedef QFlags<Qt::FindChildOption> FindChildOptions;

    enum WhiteSpaceMode
    {
        WhiteSpaceNormal,
        WhiteSpacePre,
        WhiteSpaceNoWrap,
        WhiteSpaceModeUndefined,
    };

    enum HitTestAccuracy
    {
        ExactHit,
        FuzzyHit,
    };

%If (Qt_5_1_0 -)

    enum ApplicationState
    {
        ApplicationSuspended,
        ApplicationHidden,
        ApplicationInactive,
        ApplicationActive,
    };

%End
%If (Qt_5_1_0 -)
    typedef QFlags<Qt::ApplicationState> ApplicationStates;
%End
%If (Qt_5_1_0 -)

    enum Edge
    {
        TopEdge,
        LeftEdge,
        RightEdge,
        BottomEdge,
    };

%End
%If (Qt_5_2_0 -)

    enum NativeGestureType
    {
        BeginNativeGesture,
        EndNativeGesture,
        PanNativeGesture,
        ZoomNativeGesture,
        SmartZoomNativeGesture,
        RotateNativeGesture,
        SwipeNativeGesture,
    };

%End
%If (Qt_5_2_0 -)

    enum ScrollPhase
    {
        ScrollBegin,
        ScrollUpdate,
        ScrollEnd,
%If (Qt_5_7_0 -)
        NoScrollPhase,
%End
%If (Qt_5_12_0 -)
        ScrollMomentum,
%End
    };

%End
%If (Qt_5_3_0 -)
    typedef QFlags<Qt::Edge> Edges;
%End
%If (Qt_5_3_0 -)

    enum MouseEventSource
    {
        MouseEventNotSynthesized,
        MouseEventSynthesizedBySystem,
        MouseEventSynthesizedByQt,
%If (Qt_5_6_0 -)
        MouseEventSynthesizedByApplication,
%End
    };

%End
%If (Qt_5_3_0 -)

    enum MouseEventFlag
    {
        MouseEventCreatedDoubleClick,
    };

%End
%If (Qt_5_3_0 -)
    typedef QFlags<Qt::MouseEventFlag> MouseEventFlags;
%End
%If (Qt_5_5_0 -)

    enum TabFocusBehavior
    {
        NoTabFocus,
        TabFocusTextControls,
        TabFocusListControls,
        TabFocusAllControls,
    };

%End
%If (Qt_5_5_0 -)

    enum ItemSelectionOperation
    {
        ReplaceSelection,
        AddToSelection,
    };

%End
%If (Qt_5_6_0 -)

    enum EnterKeyType
    {
        EnterKeyDefault,
        EnterKeyReturn,
        EnterKeyDone,
        EnterKeyGo,
        EnterKeySend,
        EnterKeySearch,
        EnterKeyNext,
        EnterKeyPrevious,
    };

%End
%If (Qt_5_9_0 -)

    enum ChecksumType
    {
        ChecksumIso3309,
        ChecksumItuV41,
    };

%End
%If (Qt_5_14_0 -)

    enum class HighDpiScaleFactorRoundingPolicy
    {
        Round,
        Ceil,
        Floor,
        RoundPreferFloor,
        PassThrough,
    };

%End
};

QFlags<Qt::MouseButton> operator|(Qt::MouseButton f1, QFlags<Qt::MouseButton> f2);
QFlags<Qt::Orientation> operator|(Qt::Orientation f1, QFlags<Qt::Orientation> f2);
QFlags<Qt::KeyboardModifier> operator|(Qt::KeyboardModifier f1, QFlags<Qt::KeyboardModifier> f2);
QFlags<Qt::WindowType> operator|(Qt::WindowType f1, QFlags<Qt::WindowType> f2);
QFlags<Qt::AlignmentFlag> operator|(Qt::AlignmentFlag f1, QFlags<Qt::AlignmentFlag> f2);
QFlags<Qt::ImageConversionFlag> operator|(Qt::ImageConversionFlag f1, QFlags<Qt::ImageConversionFlag> f2);
QFlags<Qt::DockWidgetArea> operator|(Qt::DockWidgetArea f1, QFlags<Qt::DockWidgetArea> f2);
QFlags<Qt::ToolBarArea> operator|(Qt::ToolBarArea f1, QFlags<Qt::ToolBarArea> f2);
QFlags<Qt::WindowState> operator|(Qt::WindowState f1, QFlags<Qt::WindowState> f2);
QFlags<Qt::DropAction> operator|(Qt::DropAction f1, QFlags<Qt::DropAction> f2);
QFlags<Qt::ItemFlag> operator|(Qt::ItemFlag f1, QFlags<Qt::ItemFlag> f2);
QFlags<Qt::MatchFlag> operator|(Qt::MatchFlag f1, QFlags<Qt::MatchFlag> f2);
QFlags<Qt::TextInteractionFlag> operator|(Qt::TextInteractionFlag f1, QFlags<Qt::TextInteractionFlag> f2);
QFlags<Qt::InputMethodHint> operator|(Qt::InputMethodHint f1, QFlags<Qt::InputMethodHint> f2);
QFlags<Qt::TouchPointState> operator|(Qt::TouchPointState f1, QFlags<Qt::TouchPointState> f2);
QFlags<Qt::GestureFlag> operator|(Qt::GestureFlag f1, QFlags<Qt::GestureFlag> f2);
QFlags<Qt::ScreenOrientation> operator|(Qt::ScreenOrientation f1, QFlags<Qt::ScreenOrientation> f2);
QFlags<Qt::InputMethodQuery> operator|(Qt::InputMethodQuery f1, QFlags<Qt::InputMethodQuery> f2);
%If (Qt_5_3_0 -)
QFlags<Qt::Edge> operator|(Qt::Edge f1, QFlags<Qt::Edge> f2);
%End
%If (Qt_5_3_0 -)
QFlags<Qt::MouseEventFlag> operator|(Qt::MouseEventFlag f1, QFlags<Qt::MouseEventFlag> f2);
%End
