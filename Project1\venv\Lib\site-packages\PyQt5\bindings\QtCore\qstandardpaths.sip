// qstandardpaths.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStandardPaths
{
%TypeHeaderCode
#include <qstandardpaths.h>
%End

public:
    enum StandardLocation
    {
        DesktopLocation,
        DocumentsLocation,
        FontsLocation,
        ApplicationsLocation,
        MusicLocation,
        MoviesLocation,
        PicturesLocation,
        TempLocation,
        HomeLocation,
        DataLocation,
        CacheLocation,
        GenericDataLocation,
        RuntimeLocation,
        ConfigLocation,
        DownloadLocation,
        GenericCacheLocation,
%If (Qt_5_2_0 -)
        GenericConfigLocation,
%End
%If (Qt_5_4_0 -)
        AppDataLocation,
%End
%If (Qt_5_4_0 -)
        AppLocalDataLocation,
%End
%If (Qt_5_5_0 -)
        AppConfigLocation,
%End
    };

    static QString writableLocation(QStandardPaths::StandardLocation type);
    static QStringList standardLocations(QStandardPaths::StandardLocation type);

    enum LocateOption
    {
        LocateFile,
        LocateDirectory,
    };

    typedef QFlags<QStandardPaths::LocateOption> LocateOptions;
    static QString locate(QStandardPaths::StandardLocation type, const QString &fileName, QFlags<QStandardPaths::LocateOption> options = QStandardPaths::LocateFile);
    static QStringList locateAll(QStandardPaths::StandardLocation type, const QString &fileName, QFlags<QStandardPaths::LocateOption> options = QStandardPaths::LocateFile);
%If (PyQt_NotBootstrapped)
    static QString displayName(QStandardPaths::StandardLocation type);
%End
    static QString findExecutable(const QString &executableName, const QStringList &paths = QStringList());
    static void enableTestMode(bool testMode);
%If (Qt_5_2_0 -)
    static void setTestModeEnabled(bool testMode);
%End

private:
    QStandardPaths();
    ~QStandardPaths();
};

%If (Qt_5_8_0 -)
QFlags<QStandardPaths::LocateOption> operator|(QStandardPaths::LocateOption f1, QFlags<QStandardPaths::LocateOption> f2);
%End
