// qstate.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QState : public QAbstractState
{
%TypeHeaderCode
#include <qstate.h>
%End

public:
    enum ChildMode
    {
        ExclusiveStates,
        ParallelStates,
    };

    enum RestorePolicy
    {
        DontRestoreProperties,
        RestoreProperties,
    };

    QState(QState *parent /TransferThis/ = 0);
    QState(QState::ChildMode childMode, QState *parent /TransferThis/ = 0);
    virtual ~QState();
    QAbstractState *errorState() const;
    void setErrorState(QAbstractState *state /KeepReference/);
    void addTransition(QAbstractTransition *transition /Transfer/);
    QSignalTransition *addTransition(SIP_PYOBJECT signal /TypeHint="pyqtBoundSignal"/, QAbstractState *target);
%MethodCode
        QObject *sender;
        QByteArray signal_signature;
        
        if ((sipError = pyqt5_get_pyqtsignal_parts(a0, &sender, signal_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addTransition(sender, signal_signature.constData(), a1);
        }
        else
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    QAbstractTransition *addTransition(QAbstractState *target /Transfer/);
    void removeTransition(QAbstractTransition *transition /TransferBack/);
    QList<QAbstractTransition *> transitions() const;
    QAbstractState *initialState() const;
    void setInitialState(QAbstractState *state /KeepReference/);
    QState::ChildMode childMode() const;
    void setChildMode(QState::ChildMode mode);
    void assignProperty(QObject *object, const char *name, const QVariant &value);

signals:
    void finished();
    void propertiesAssigned();

protected:
    virtual void onEntry(QEvent *event);
    virtual void onExit(QEvent *event);
    virtual bool event(QEvent *e);

signals:
%If (Qt_5_4_0 -)
    void childModeChanged();
%End
%If (Qt_5_4_0 -)
    void initialStateChanged();
%End
%If (Qt_5_4_0 -)
    void errorStateChanged();
%End
};
