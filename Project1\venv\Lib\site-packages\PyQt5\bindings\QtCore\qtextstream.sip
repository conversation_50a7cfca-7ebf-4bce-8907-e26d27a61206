// qtextstream.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qtextstream.h>
%End

class QTextStream
{
%TypeHeaderCode
#include <qtextstream.h>
%End

public:
    enum RealNumberNotation
    {
        SmartNotation,
        FixedNotation,
        ScientificNotation,
    };

    enum FieldAlignment
    {
        Align<PERSON>eft,
        AlignRight,
        AlignCenter,
        AlignAccountingStyle,
    };

    enum NumberFlag
    {
        ShowBase,
        ForcePoint,
        ForceSign,
        UppercaseBase,
        UppercaseDigits,
    };

    enum Status
    {
        Ok,
        ReadPastEnd,
        ReadCorruptData,
        WriteFailed,
    };

    typedef QFlags<QTextStream::NumberFlag> NumberFlags;
    QTextStream();
    explicit QTextStream(QIODevice *device);
    QTextStream(QByteArray *array /Constrained/, QIODevice::OpenMode mode = QIODevice::ReadWrite);
    virtual ~QTextStream();
    void setCodec(QTextCodec *codec /KeepReference/);
    void setCodec(const char *codecName);
    QTextCodec *codec() const;
    void setAutoDetectUnicode(bool enabled);
    bool autoDetectUnicode() const;
    void setGenerateByteOrderMark(bool generate);
    bool generateByteOrderMark() const;
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    bool atEnd() const;
    void reset();
    void flush() /ReleaseGIL/;
    bool seek(qint64 pos);
    void skipWhiteSpace();
    QString read(qint64 maxlen) /ReleaseGIL/;
    QString readLine(qint64 maxLength = 0) /ReleaseGIL/;
    QString readAll() /ReleaseGIL/;
    void setFieldAlignment(QTextStream::FieldAlignment alignment);
    QTextStream::FieldAlignment fieldAlignment() const;
    void setPadChar(QChar ch);
    QChar padChar() const;
    void setFieldWidth(int width);
    int fieldWidth() const;
    void setNumberFlags(QTextStream::NumberFlags flags);
    QTextStream::NumberFlags numberFlags() const;
    void setIntegerBase(int base);
    int integerBase() const;
    void setRealNumberNotation(QTextStream::RealNumberNotation notation);
    QTextStream::RealNumberNotation realNumberNotation() const;
    void setRealNumberPrecision(int precision);
    int realNumberPrecision() const;
    QTextStream::Status status() const;
    void setStatus(QTextStream::Status status);
    void resetStatus();
    qint64 pos() const;
    QTextStream &operator>>(QByteArray &array /Constrained/);
    QTextStream &operator<<(const QString &s);
    QTextStream &operator<<(const QByteArray &array);
    QTextStream &operator<<(double f /Constrained/);
    QTextStream &operator<<(SIP_PYOBJECT i /TypeHint="int"/);
%MethodCode
        #if PY_MAJOR_VERSION < 3
        if (PyInt_Check(a1))
        {
            qlonglong val = PyInt_AsLong(a1);
        
            sipRes = &(*a0 << val);
        }
        else
        #endif
        {
            qlonglong val = sipLong_AsLongLong(a1);
        
            if (!PyErr_Occurred())
            {
                sipRes = &(*a0 << val);
            }
            else
            {
                // If it is positive then it might fit an unsigned long long.
        
                qulonglong uval = sipLong_AsUnsignedLongLong(a1);
        
                if (!PyErr_Occurred())
                {
                    sipRes = &(*a0 << uval);
                }
                else
                {
                    sipError = (PyErr_ExceptionMatches(PyExc_OverflowError)
                            ? sipErrorFail : sipErrorContinue);
                }
            }
        }
%End

    void setLocale(const QLocale &locale);
    QLocale locale() const;

private:
    QTextStream(const QTextStream &);
};

QFlags<QTextStream::NumberFlag> operator|(QTextStream::NumberFlag f1, QFlags<QTextStream::NumberFlag> f2);
class QTextStreamManipulator;
QTextStream &operator<<(QTextStream &s, QTextStreamManipulator m);
QTextStream &bin(QTextStream &s) /PyName=bin_/;
QTextStream &oct(QTextStream &s) /PyName=oct_/;
QTextStream &dec(QTextStream &s);
QTextStream &hex(QTextStream &s) /PyName=hex_/;
QTextStream &showbase(QTextStream &s);
QTextStream &forcesign(QTextStream &s);
QTextStream &forcepoint(QTextStream &s);
QTextStream &noshowbase(QTextStream &s);
QTextStream &noforcesign(QTextStream &s);
QTextStream &noforcepoint(QTextStream &s);
QTextStream &uppercasebase(QTextStream &s);
QTextStream &uppercasedigits(QTextStream &s);
QTextStream &lowercasebase(QTextStream &s);
QTextStream &lowercasedigits(QTextStream &s);
QTextStream &fixed(QTextStream &s);
QTextStream &scientific(QTextStream &s);
QTextStream &left(QTextStream &s);
QTextStream &right(QTextStream &s);
QTextStream &center(QTextStream &s);
QTextStream &endl(QTextStream &s);
QTextStream &flush(QTextStream &s);
QTextStream &reset(QTextStream &s);
QTextStream &bom(QTextStream &s);
QTextStream &ws(QTextStream &s);
QTextStreamManipulator qSetFieldWidth(int width);
QTextStreamManipulator qSetPadChar(QChar ch);
QTextStreamManipulator qSetRealNumberPrecision(int precision);
