// qdbuserror.sip generated by MetaSIP
//
// This file is part of the QtDBus Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDBusError
{
%TypeHeaderCode
#include <qdbuserror.h>
%End

public:
    enum ErrorType
    {
        NoError,
        Other,
        Failed,
        NoMemory,
        ServiceUnknown,
        NoReply,
        Bad<PERSON><PERSON>ress,
        NotSupported,
        LimitsExceeded,
        AccessDenied,
        NoServer,
        Timeout,
        NoNetwork,
        AddressInUse,
        Disconnected,
        InvalidArgs,
        UnknownMethod,
        TimedOut,
        InvalidSignature,
        UnknownInterface,
        InternalError,
        UnknownObject,
        InvalidService,
        InvalidObjectPath,
        InvalidInterface,
        InvalidMember,
        UnknownProperty,
        PropertyReadOnly,
    };

    QDBusError(const QDBusError &other);
    QDBusError::ErrorType type() const;
    QString name() const;
    QString message() const;
    bool isValid() const;
    static QString errorString(QDBusError::ErrorType error);
%If (Qt_5_6_0 -)
    void swap(QDBusError &other /Constrained/);
%End
};
