// abstractformeditor.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesignerFormEditorInterface : public QObject
{
%TypeHeaderCode
#include <abstractformeditor.h>
%End

public:
%If (Qt_5_6_1 -)
    explicit QDesignerFormEditorInterface(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QDesignerFormEditorInterface(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QDesignerFormEditorInterface();
    QExtensionManager *extensionManager() const;
    QWidget *topLevel() const;
    QDesignerWidgetBoxInterface *widgetBox() const;
    QDesignerPropertyEditorInterface *propertyEditor() const;
    QDesignerObjectInspectorInterface *objectInspector() const;
    QDesignerFormWindowManagerInterface *formWindowManager() const;
    QDesignerActionEditorInterface *actionEditor() const;
    void setWidgetBox(QDesignerWidgetBoxInterface *widgetBox /KeepReference/);
    void setPropertyEditor(QDesignerPropertyEditorInterface *propertyEditor /KeepReference/);
    void setObjectInspector(QDesignerObjectInspectorInterface *objectInspector /KeepReference/);
    void setActionEditor(QDesignerActionEditorInterface *actionEditor /KeepReference/);

private:
    QDesignerFormEditorInterface(const QDesignerFormEditorInterface &other);
};
