// abstractformwindowcursor.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesignerFormWindowCursorInterface
{
%TypeHeaderCode
#include <abstractformwindowcursor.h>
%End

public:
    enum MoveOperation
    {
        NoMove,
        Start,
        End,
        Next,
        Prev,
        Left,
        Right,
        Up,
        Down,
    };

    enum MoveMode
    {
        MoveAnchor,
        KeepAnchor,
    };

    virtual ~QDesignerFormWindowCursorInterface();
    virtual QDesignerFormWindowInterface *formWindow() const = 0;
    virtual bool movePosition(QDesignerFormWindowCursorInterface::MoveOperation op, QDesignerFormWindowCursorInterface::MoveMode mode = QDesignerFormWindowCursorInterface::MoveAnchor) = 0;
    virtual int position() const = 0;
    virtual void setPosition(int pos, QDesignerFormWindowCursorInterface::MoveMode mode = QDesignerFormWindowCursorInterface::MoveAnchor) = 0;
    virtual QWidget *current() const = 0;
    virtual int widgetCount() const = 0;
    virtual QWidget *widget(int index) const = 0;
    virtual bool hasSelection() const = 0;
    virtual int selectedWidgetCount() const = 0;
    virtual QWidget *selectedWidget(int index) const = 0;
    virtual void setProperty(const QString &name, const QVariant &value) = 0;
    virtual void setWidgetProperty(QWidget *widget, const QString &name, const QVariant &value) = 0;
    virtual void resetWidgetProperty(QWidget *widget, const QString &name) = 0;
    bool isWidgetSelected(QWidget *widget) const;
};
