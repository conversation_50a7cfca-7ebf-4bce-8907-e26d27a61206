// qextensionmanager.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QExtensionManager : public QObject, public QAbstractExtensionManager
{
%TypeHeaderCode
#include <qextensionmanager.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QPyDesignerCustomWidgetPlugin, &sipType_QPyDesignerCustomWidgetPlugin, -1, 1},
        {sipName_QExtensionFactory, &sipType_QExtensionFactory, -1, 2},
        {sipName_QPyDesignerMemberSheetExtension, &sipType_QPyDesignerMemberSheetExtension, -1, 3},
        {sipName_QDesignerFormEditorInterface, &sipType_QDesignerFormEditorInterface, -1, 4},
        {sipName_QDesignerWidgetBoxInterface, &sipType_QDesignerWidgetBoxInterface, -1, 5},
        {sipName_QDesignerFormWindowInterface, &sipType_QDesignerFormWindowInterface, -1, 6},
        {sipName_QDesignerActionEditorInterface, &sipType_QDesignerActionEditorInterface, -1, 7},
        {sipName_QPyDesignerContainerExtension, &sipType_QPyDesignerContainerExtension, -1, 8},
        {sipName_QDesignerPropertyEditorInterface, &sipType_QDesignerPropertyEditorInterface, -1, 9},
        {sipName_QDesignerFormWindowManagerInterface, &sipType_QDesignerFormWindowManagerInterface, -1, 10},
        {sipName_QPyDesignerTaskMenuExtension, &sipType_QPyDesignerTaskMenuExtension, -1, 11},
        {sipName_QPyDesignerPropertySheetExtension, &sipType_QPyDesignerPropertySheetExtension, -1, 12},
        {sipName_QDesignerObjectInspectorInterface, &sipType_QDesignerObjectInspectorInterface, -1, 13},
        {sipName_QPyDesignerCustomWidgetCollectionPlugin, &sipType_QPyDesignerCustomWidgetCollectionPlugin, -1, 14},
        {sipName_QExtensionManager, &sipType_QExtensionManager, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
%If (Qt_5_6_1 -)
    explicit QExtensionManager(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QExtensionManager(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QExtensionManager();
    virtual void registerExtensions(QAbstractExtensionFactory *factory, const QString &iid = QString());
    virtual void unregisterExtensions(QAbstractExtensionFactory *factory, const QString &iid = QString());
    virtual QObject *extension(QObject *object, const QString &iid) const;
};
