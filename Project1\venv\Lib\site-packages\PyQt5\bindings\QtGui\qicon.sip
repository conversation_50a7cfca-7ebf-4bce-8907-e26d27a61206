// qicon.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QIcon /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qicon.h>
%End

public:
    enum Mode
    {
        Normal,
        Disabled,
        Active,
        Selected,
    };

    enum State
    {
        On,
        Off,
    };

    QIcon();
    QIcon(const QPixmap &pixmap);
    QIcon(const QIcon &other);
    explicit QIcon(const QString &fileName);
    explicit QIcon(QIconEngine *engine /GetWrapper/);
%MethodCode
        sipCpp = new QIcon(a0);
        
        // The QIconEngine is implicitly shared by copies of the QIcon and is destroyed
        // by C++ when the last copy is destroyed.  Therefore we need to transfer
        // ownership but not to associate it with this QIcon.  The Python object will
        // get tidied up when the virtual dtor gets called.
        sipTransferTo(a0Wrapper, Py_None);
%End

    QIcon(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QIcon>())
            sipCpp = new QIcon(a0->value<QIcon>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QIcon();
    QPixmap pixmap(const QSize &size, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
    QPixmap pixmap(int w, int h, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
    QPixmap pixmap(int extent, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
%If (Qt_5_1_0 -)
    QPixmap pixmap(QWindow *window, const QSize &size, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
%End
    QSize actualSize(const QSize &size, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
%If (Qt_5_1_0 -)
    QSize actualSize(QWindow *window, const QSize &size, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
%End
    QList<QSize> availableSizes(QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
    void paint(QPainter *painter, const QRect &rect, Qt::Alignment alignment = Qt::AlignCenter, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
    void paint(QPainter *painter, int x, int y, int w, int h, Qt::Alignment alignment = Qt::AlignCenter, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
    bool isNull() const;
    bool isDetached() const;
    void addPixmap(const QPixmap &pixmap, QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off);
    void addFile(const QString &fileName, const QSize &size = QSize(), QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off);
    qint64 cacheKey() const;
%If (Qt_5_7_0 -)
    static QIcon fromTheme(const QString &name);
%End
%If (Qt_5_7_0 -)
    static QIcon fromTheme(const QString &name, const QIcon &fallback);
%End
%If (- Qt_5_7_0)
    static QIcon fromTheme(const QString &name, const QIcon &fallback = QIcon());
%End
    static bool hasThemeIcon(const QString &name);
    static QStringList themeSearchPaths();
    static void setThemeSearchPaths(const QStringList &searchpath);
    static QString themeName();
    static void setThemeName(const QString &path);
    QString name() const;
    void swap(QIcon &other /Constrained/);
%If (Qt_5_6_0 -)
    void setIsMask(bool isMask);
%End
%If (Qt_5_6_0 -)
    bool isMask() const;
%End
%If (Qt_5_11_0 -)
    static QStringList fallbackSearchPaths();
%End
%If (Qt_5_11_0 -)
    static void setFallbackSearchPaths(const QStringList &paths);
%End
%If (Qt_5_12_0 -)
    static QString fallbackThemeName();
%End
%If (Qt_5_12_0 -)
    static void setFallbackThemeName(const QString &name);
%End
};

QDataStream &operator<<(QDataStream &, const QIcon & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QIcon & /Constrained/) /ReleaseGIL/;
