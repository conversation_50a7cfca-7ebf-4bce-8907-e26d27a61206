// qimage.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QImage : public QPaintDevice
{
%TypeHeaderCode
#include <qimage.h>
%End

public:
    enum InvertMode
    {
        InvertRgb,
        InvertRgba,
    };

    enum Format
    {
        Format_Invalid,
        Format_Mono,
        Format_MonoLSB,
        Format_Indexed8,
        Format_RGB32,
        Format_ARGB32,
        Format_ARGB32_Premultiplied,
        Format_RGB16,
        Format_ARGB8565_Premultiplied,
        Format_RGB666,
        Format_ARGB6666_Premultiplied,
        Format_RGB555,
        Format_ARGB8555_Premultiplied,
        Format_RGB888,
        Format_RGB444,
        Format_ARGB4444_Premultiplied,
%If (Qt_5_2_0 -)
        Format_RGBX8888,
%End
%If (Qt_5_2_0 -)
        Format_RGBA8888,
%End
%If (Qt_5_2_0 -)
        Format_RGBA8888_Premultiplied,
%End
%If (Qt_5_4_0 -)
        Format_BGR30,
%End
%If (Qt_5_4_0 -)
        Format_A2BGR30_Premultiplied,
%End
%If (Qt_5_4_0 -)
        Format_RGB30,
%End
%If (Qt_5_4_0 -)
        Format_A2RGB30_Premultiplied,
%End
%If (Qt_5_5_0 -)
        Format_Alpha8,
%End
%If (Qt_5_5_0 -)
        Format_Grayscale8,
%End
%If (Qt_5_12_0 -)
        Format_RGBX64,
%End
%If (Qt_5_12_0 -)
        Format_RGBA64,
%End
%If (Qt_5_12_0 -)
        Format_RGBA64_Premultiplied,
%End
%If (Qt_5_13_0 -)
        Format_Grayscale16,
%End
%If (Qt_5_14_0 -)
        Format_BGR888,
%End
    };

    QImage();
    QImage(const QSize &size, QImage::Format format);
    QImage(int width, int height, QImage::Format format);
    QImage(const uchar *data /KeepReference/, int width, int height, QImage::Format format);
    QImage(void *data, int width, int height, QImage::Format format) [(uchar *data, int width, int height, QImage::Format format)];
    QImage(const uchar *data /KeepReference/, int width, int height, int bytesPerLine, QImage::Format format);
    QImage(void *data, int width, int height, int bytesPerLine, QImage::Format format) [(uchar *data, int width, int height, int bytesPerLine, QImage::Format format)];
    explicit QImage(SIP_PYLIST xpm /TypeHint="List[str]"/) [(const char **xpm)];
%MethodCode
        // The Python interface is a list of strings that make up the image.
        
        const char **str = QtGui_ListToArray(a0);
        
        if (str)
        {
            sipCpp = new sipQImage(str);
            QtGui_DeleteArray(str);
        }
        else
            sipIsErr = 1;
%End

    QImage(const QString &fileName, const char *format = 0) /ReleaseGIL/;
    QImage(const QImage &);
    QImage(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QImage>())
            sipCpp = new sipQImage(a0->value<QImage>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    virtual ~QImage();
    bool isNull() const;
    virtual int devType() const;
    bool operator==(const QImage &) const;
    bool operator!=(const QImage &) const;
    void detach();
    bool isDetached() const;
    QImage copy(const QRect &rect = QRect()) const;
    QImage copy(int x, int y, int w, int h) const;
    QImage::Format format() const;
    QImage convertToFormat(QImage::Format f, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor) const;
    QImage convertToFormat(QImage::Format f, const QVector<unsigned int> &colorTable, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor) const;
    int width() const;
    int height() const;
    QSize size() const;
    QRect rect() const;
    int depth() const;
    QRgb color(int i) const;
    void setColor(int i, QRgb c);
    bool allGray() const;
    bool isGrayscale() const;
    void *bits() [uchar * ()];
    const void *constBits() const [const uchar * ()];
    void *scanLine(int) [uchar * (int)];
    const void *constScanLine(int) const [const uchar * (int)];
    int bytesPerLine() const;
    bool valid(const QPoint &pt) const;
    bool valid(int x, int y) const;
    int pixelIndex(const QPoint &pt) const;
    int pixelIndex(int x, int y) const;
    QRgb pixel(const QPoint &pt) const;
    QRgb pixel(int x, int y) const;
    void setPixel(const QPoint &pt, uint index_or_rgb);
    void setPixel(int x, int y, uint index_or_rgb);
    QVector<unsigned int> colorTable() const;
    void setColorTable(const QVector<unsigned int> colors);
    void fill(Qt::GlobalColor color /Constrained/);
    void fill(const QColor &color);
    void fill(uint pixel);
    bool hasAlphaChannel() const;
    void setAlphaChannel(const QImage &alphaChannel);
    QImage createAlphaMask(Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor) const;
    QImage createHeuristicMask(bool clipTight = true) const;
    QImage scaled(int width, int height, Qt::AspectRatioMode aspectRatioMode = Qt::IgnoreAspectRatio, Qt::TransformationMode transformMode = Qt::FastTransformation) const;
    QImage scaled(const QSize &size, Qt::AspectRatioMode aspectRatioMode = Qt::IgnoreAspectRatio, Qt::TransformationMode transformMode = Qt::FastTransformation) const;
    QImage scaledToWidth(int width, Qt::TransformationMode mode = Qt::FastTransformation) const;
    QImage scaledToHeight(int height, Qt::TransformationMode mode = Qt::FastTransformation) const;
    QImage mirrored(bool horizontal = false, bool vertical = true) const;
    QImage rgbSwapped() const;
    void invertPixels(QImage::InvertMode mode = QImage::InvertRgb);
    bool load(QIODevice *device, const char *format) /ReleaseGIL/;
    bool load(const QString &fileName, const char *format = 0) /ReleaseGIL/;
    bool loadFromData(const uchar *data /Array/, int len /ArraySize/, const char *format = 0);
    bool loadFromData(const QByteArray &data, const char *format = 0);
    bool save(const QString &fileName, const char *format = 0, int quality = -1) const /ReleaseGIL/;
    bool save(QIODevice *device, const char *format = 0, int quality = -1) const /ReleaseGIL/;
    static QImage fromData(const uchar *data /Array/, int size /ArraySize/, const char *format = 0);
    static QImage fromData(const QByteArray &data, const char *format = 0);
    virtual QPaintEngine *paintEngine() const;
    int dotsPerMeterX() const;
    int dotsPerMeterY() const;
    void setDotsPerMeterX(int);
    void setDotsPerMeterY(int);
    QPoint offset() const;
    void setOffset(const QPoint &);
    QStringList textKeys() const;
    QString text(const QString &key = QString()) const;
    void setText(const QString &key, const QString &value);

protected:
    virtual int metric(QPaintDevice::PaintDeviceMetric metric) const;
%If (Qt_5_5_0 -)
    QImage smoothScaled(int w, int h) const;
%End

public:
    QImage createMaskFromColor(QRgb color, Qt::MaskMode mode = Qt::MaskInColor) const;
    QImage transformed(const QTransform &matrix, Qt::TransformationMode mode = Qt::FastTransformation) const;
    static QTransform trueMatrix(const QTransform &, int w, int h);
    qint64 cacheKey() const;
    int colorCount() const;
    void setColorCount(int);
    int byteCount() const;
    int bitPlaneCount() const;
    void swap(QImage &other /Constrained/);
    qreal devicePixelRatio() const;
    void setDevicePixelRatio(qreal scaleFactor);
%If (Qt_5_4_0 -)
    QPixelFormat pixelFormat() const;
%End
%If (Qt_5_4_0 -)
    static QPixelFormat toPixelFormat(QImage::Format format);
%End
%If (Qt_5_4_0 -)
    static QImage::Format toImageFormat(QPixelFormat format);
%End
%If (Qt_5_6_0 -)
    QColor pixelColor(int x, int y) const;
%End
%If (Qt_5_6_0 -)
    QColor pixelColor(const QPoint &pt) const;
%End
%If (Qt_5_6_0 -)
    void setPixelColor(int x, int y, const QColor &c);
%End
%If (Qt_5_6_0 -)
    void setPixelColor(const QPoint &pt, const QColor &c);
%End
%If (Qt_5_9_0 -)
    bool reinterpretAsFormat(QImage::Format f);
%End
%If (Qt_5_10_0 -)
    Py_ssize_t sizeInBytes() const [qsizetype ()];
%End
%If (Qt_5_13_0 -)
    void convertTo(QImage::Format f, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor);
%End
%If (Qt_5_14_0 -)
    QColorSpace colorSpace() const;
%End
%If (Qt_5_14_0 -)
    QImage convertedToColorSpace(const QColorSpace &) const;
%End
%If (Qt_5_14_0 -)
    void convertToColorSpace(const QColorSpace &);
%End
%If (Qt_5_14_0 -)
    void setColorSpace(const QColorSpace &);
%End
%If (Qt_5_14_0 -)
    void applyColorTransform(const QColorTransform &transform);
%End
};

QDataStream &operator<<(QDataStream &, const QImage & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QImage & /Constrained/) /ReleaseGIL/;

%ModuleHeaderCode
const char **QtGui_ListToArray(PyObject *lst);
void QtGui_DeleteArray(const char **arr);
%End

%ModuleCode
// Convert a list of strings to an array of ASCII strings on the heap.  Used by
// QImage and QPixmap.
const char **QtGui_ListToArray(PyObject *lst)
{
    Py_ssize_t nstr = PyList_Size(lst);
    const char **arr = new const char *[nstr + 1];

    for (Py_ssize_t i = 0; i < nstr; ++i)
    {
        PyObject *ascii_obj = PyList_GetItem(lst, i);
        const char *ascii = sipString_AsASCIIString(&ascii_obj);

        if (!ascii)
        {
            while (i-- > 0)
                delete[] arr[i];

            delete[] arr;

            return 0;
        }

        // Copy the string.
        arr[i] = qstrdup(ascii);

        Py_DECREF(ascii_obj);
    }

    // The sentinal.
    arr[nstr] = 0;

    return arr;
}


// Return a string array created by QtGui_ListToArray() to the heap.
void QtGui_DeleteArray(const char **arr)
{
    for (Py_ssize_t i = 0; arr[i]; ++i)
        delete[] arr[i];

    delete[] arr;
}
%End
