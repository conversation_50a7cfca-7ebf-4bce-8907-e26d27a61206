// qinputmethod.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QInputMethod : public QObject
{
%TypeHeaderCode
#include <qinputmethod.h>
%End

public:
    QTransform inputItemTransform() const;
    void setInputItemTransform(const QTransform &transform);
    QRectF cursorRectangle() const;
    QRectF keyboardRectangle() const;

    enum Action
    {
        Click,
        ContextMenu,
    };

    bool isVisible() const;
    void setVisible(bool visible);
    bool isAnimating() const;
    QLocale locale() const;
    Qt::LayoutDirection inputDirection() const;
%If (Qt_5_1_0 -)
    QRectF inputItemRectangle() const;
%End
%If (Qt_5_1_0 -)
    void setInputItemRectangle(const QRectF &rect);
%End
%If (Qt_5_3_0 -)
    static QVariant queryFocusObject(Qt::InputMethodQuery query, QVariant argument);
%End

public slots:
    void show();
    void hide();
    void update(Qt::InputMethodQueries queries);
    void reset();
    void commit();
    void invokeAction(QInputMethod::Action a, int cursorPosition);

signals:
    void cursorRectangleChanged();
    void keyboardRectangleChanged();
    void visibleChanged();
    void animatingChanged();
    void localeChanged();
    void inputDirectionChanged(Qt::LayoutDirection newDirection);

public:
%If (Qt_5_7_0 -)
    QRectF anchorRectangle() const;
%End
%If (Qt_5_7_0 -)
    QRectF inputItemClipRectangle() const;
%End

signals:
%If (Qt_5_7_0 -)
    void anchorRectangleChanged();
%End
%If (Qt_5_7_0 -)
    void inputItemClipRectangleChanged();
%End

private:
    QInputMethod();
    virtual ~QInputMethod();
};
