// qpagedpaintdevice.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPagedPaintDevice : public QPaintDevice
{
%TypeHeaderCode
#include <qpagedpaintdevice.h>
%End

public:
    QPagedPaintDevice();
    virtual ~QPagedPaintDevice();
    virtual bool newPage() = 0;

    enum PageSize
    {
        A4,
        B5,
        Letter,
        Legal,
        Executive,
        A0,
        A1,
        A2,
        A3,
        A5,
        A6,
        A7,
        A8,
        A9,
        B0,
        B1,
        B10,
        B2,
        B3,
        B4,
        B6,
        B7,
        B8,
        B9,
        C5E,
        Comm10E,
        DLE,
        Folio,
        Ledger,
        Tabloid,
        Custom,
%If (Qt_5_3_0 -)
        A10,
%End
%If (Qt_5_3_0 -)
        A3Extra,
%End
%If (Qt_5_3_0 -)
        A4Extra,
%End
%If (Qt_5_3_0 -)
        A4Plus,
%End
%If (Qt_5_3_0 -)
        A4Small,
%End
%If (Qt_5_3_0 -)
        A5Extra,
%End
%If (Qt_5_3_0 -)
        B5Extra,
%End
%If (Qt_5_3_0 -)
        JisB0,
%End
%If (Qt_5_3_0 -)
        JisB1,
%End
%If (Qt_5_3_0 -)
        JisB2,
%End
%If (Qt_5_3_0 -)
        JisB3,
%End
%If (Qt_5_3_0 -)
        JisB4,
%End
%If (Qt_5_3_0 -)
        JisB5,
%End
%If (Qt_5_3_0 -)
        JisB6,
%End
%If (Qt_5_3_0 -)
        JisB7,
%End
%If (Qt_5_3_0 -)
        JisB8,
%End
%If (Qt_5_3_0 -)
        JisB9,
%End
%If (Qt_5_3_0 -)
        JisB10,
%End
%If (Qt_5_3_0 -)
        AnsiC,
%End
%If (Qt_5_3_0 -)
        AnsiD,
%End
%If (Qt_5_3_0 -)
        AnsiE,
%End
%If (Qt_5_3_0 -)
        LegalExtra,
%End
%If (Qt_5_3_0 -)
        LetterExtra,
%End
%If (Qt_5_3_0 -)
        LetterPlus,
%End
%If (Qt_5_3_0 -)
        LetterSmall,
%End
%If (Qt_5_3_0 -)
        TabloidExtra,
%End
%If (Qt_5_3_0 -)
        ArchA,
%End
%If (Qt_5_3_0 -)
        ArchB,
%End
%If (Qt_5_3_0 -)
        ArchC,
%End
%If (Qt_5_3_0 -)
        ArchD,
%End
%If (Qt_5_3_0 -)
        ArchE,
%End
%If (Qt_5_3_0 -)
        Imperial7x9,
%End
%If (Qt_5_3_0 -)
        Imperial8x10,
%End
%If (Qt_5_3_0 -)
        Imperial9x11,
%End
%If (Qt_5_3_0 -)
        Imperial9x12,
%End
%If (Qt_5_3_0 -)
        Imperial10x11,
%End
%If (Qt_5_3_0 -)
        Imperial10x13,
%End
%If (Qt_5_3_0 -)
        Imperial10x14,
%End
%If (Qt_5_3_0 -)
        Imperial12x11,
%End
%If (Qt_5_3_0 -)
        Imperial15x11,
%End
%If (Qt_5_3_0 -)
        ExecutiveStandard,
%End
%If (Qt_5_3_0 -)
        Note,
%End
%If (Qt_5_3_0 -)
        Quarto,
%End
%If (Qt_5_3_0 -)
        Statement,
%End
%If (Qt_5_3_0 -)
        SuperA,
%End
%If (Qt_5_3_0 -)
        SuperB,
%End
%If (Qt_5_3_0 -)
        Postcard,
%End
%If (Qt_5_3_0 -)
        DoublePostcard,
%End
%If (Qt_5_3_0 -)
        Prc16K,
%End
%If (Qt_5_3_0 -)
        Prc32K,
%End
%If (Qt_5_3_0 -)
        Prc32KBig,
%End
%If (Qt_5_3_0 -)
        FanFoldUS,
%End
%If (Qt_5_3_0 -)
        FanFoldGerman,
%End
%If (Qt_5_3_0 -)
        FanFoldGermanLegal,
%End
%If (Qt_5_3_0 -)
        EnvelopeB4,
%End
%If (Qt_5_3_0 -)
        EnvelopeB5,
%End
%If (Qt_5_3_0 -)
        EnvelopeB6,
%End
%If (Qt_5_3_0 -)
        EnvelopeC0,
%End
%If (Qt_5_3_0 -)
        EnvelopeC1,
%End
%If (Qt_5_3_0 -)
        EnvelopeC2,
%End
%If (Qt_5_3_0 -)
        EnvelopeC3,
%End
%If (Qt_5_3_0 -)
        EnvelopeC4,
%End
%If (Qt_5_3_0 -)
        EnvelopeC6,
%End
%If (Qt_5_3_0 -)
        EnvelopeC65,
%End
%If (Qt_5_3_0 -)
        EnvelopeC7,
%End
%If (Qt_5_3_0 -)
        Envelope9,
%End
%If (Qt_5_3_0 -)
        Envelope11,
%End
%If (Qt_5_3_0 -)
        Envelope12,
%End
%If (Qt_5_3_0 -)
        Envelope14,
%End
%If (Qt_5_3_0 -)
        EnvelopeMonarch,
%End
%If (Qt_5_3_0 -)
        EnvelopePersonal,
%End
%If (Qt_5_3_0 -)
        EnvelopeChou3,
%End
%If (Qt_5_3_0 -)
        EnvelopeChou4,
%End
%If (Qt_5_3_0 -)
        EnvelopeInvite,
%End
%If (Qt_5_3_0 -)
        EnvelopeItalian,
%End
%If (Qt_5_3_0 -)
        EnvelopeKaku2,
%End
%If (Qt_5_3_0 -)
        EnvelopeKaku3,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc1,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc2,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc3,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc4,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc5,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc6,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc7,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc8,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc9,
%End
%If (Qt_5_3_0 -)
        EnvelopePrc10,
%End
%If (Qt_5_3_0 -)
        EnvelopeYou4,
%End
%If (Qt_5_3_0 -)
        NPaperSize,
%End
%If (Qt_5_3_0 -)
        AnsiA,
%End
%If (Qt_5_3_0 -)
        AnsiB,
%End
%If (Qt_5_3_0 -)
        EnvelopeC5,
%End
%If (Qt_5_3_0 -)
        EnvelopeDL,
%End
%If (Qt_5_3_0 -)
        Envelope10,
%End
%If (Qt_5_3_0 -)
        LastPageSize,
%End
    };

%If (Qt_5_10_0 -)

    enum PdfVersion
    {
        PdfVersion_1_4,
        PdfVersion_A1b,
%If (Qt_5_12_0 -)
        PdfVersion_1_6,
%End
    };

%End
    virtual void setPageSize(QPagedPaintDevice::PageSize size);
    QPagedPaintDevice::PageSize pageSize() const;
    virtual void setPageSizeMM(const QSizeF &size);
    QSizeF pageSizeMM() const;

    struct Margins
    {
%TypeHeaderCode
#include <qpagedpaintdevice.h>
%End

        qreal left;
        qreal right;
        qreal top;
        qreal bottom;
    };

    virtual void setMargins(const QPagedPaintDevice::Margins &margins);
    QPagedPaintDevice::Margins margins() const;
%If (Qt_5_3_0 -)
    bool setPageLayout(const QPageLayout &pageLayout);
%End
%If (Qt_5_3_0 -)
    bool setPageSize(const QPageSize &pageSize);
%End
%If (Qt_5_3_0 -)
    bool setPageOrientation(QPageLayout::Orientation orientation);
%End
%If (Qt_5_3_0 -)
    bool setPageMargins(const QMarginsF &margins);
%End
%If (Qt_5_3_0 -)
    bool setPageMargins(const QMarginsF &margins, QPageLayout::Unit units);
%End
%If (Qt_5_3_0 -)
    QPageLayout pageLayout() const;
%End
};
