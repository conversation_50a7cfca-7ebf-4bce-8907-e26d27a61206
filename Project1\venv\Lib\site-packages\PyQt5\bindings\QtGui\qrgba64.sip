// qrgba64.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_6_0 -)
%ModuleCode
#include <qrgba64.h>
%End
%End

%If (Qt_5_6_0 -)

class QRgba64
{
%TypeHeaderCode
#include <qrgba64.h>
%End

public:
%If (Qt_5_7_0 -)
    QRgba64();
%End
    static QRgba64 fromRgba64(quint64 c);
    static QRgba64 fromRgba64(quint16 red, quint16 green, quint16 blue, quint16 alpha);
    static QRgba64 fromRgba(quint8 red, quint8 green, quint8 blue, quint8 alpha);
    static QRgba64 fromArgb32(uint rgb);
    bool isOpaque() const;
    bool isTransparent() const;
    quint16 red() const;
    quint16 green() const;
    quint16 blue() const;
    quint16 alpha() const;
    void setRed(quint16 _red);
    void setGreen(quint16 _green);
    void setBlue(quint16 _blue);
    void setAlpha(quint16 _alpha);
    quint8 red8() const;
    quint8 green8() const;
    quint8 blue8() const;
    quint8 alpha8() const;
    uint toArgb32() const;
    ushort toRgb16() const;
    QRgba64 premultiplied() const;
    QRgba64 unpremultiplied() const;
    operator quint64() const;
};

%End
%If (Qt_5_6_0 -)
QRgba64 qRgba64(quint16 r, quint16 g, quint16 b, quint16 a);
%End
%If (Qt_5_6_0 -)
QRgba64 qRgba64(quint64 c);
%End
%If (Qt_5_6_0 -)
QRgba64 qPremultiply(QRgba64 c);
%End
%If (Qt_5_6_0 -)
QRgba64 qUnpremultiply(QRgba64 c);
%End
%If (Qt_5_6_0 -)
uint qRed(QRgba64 rgb);
%End
%If (Qt_5_6_0 -)
uint qGreen(QRgba64 rgb);
%End
%If (Qt_5_6_0 -)
uint qBlue(QRgba64 rgb);
%End
%If (Qt_5_6_0 -)
uint qAlpha(QRgba64 rgb);
%End
