// qsurface.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSurface /Abstract/
{
%TypeHeaderCode
#include <qsurface.h>
%End

public:
    enum SurfaceClass
    {
        Window,
%If (Qt_5_1_0 -)
        Offscreen,
%End
    };

    enum SurfaceType
    {
        RasterSurface,
        OpenGLSurface,
%If (Qt_5_3_0 -)
        RasterGLSurface,
%End
%If (Qt_5_9_0 -)
        OpenVGSurface,
%End
%If (Qt_5_10_0 -)
        VulkanSurface,
%End
%If (Qt_5_12_0 -)
        MetalSurface,
%End
    };

    virtual ~QSurface();
    QSurface::SurfaceClass surfaceClass() const;
    virtual QSurfaceFormat format() const = 0;
    virtual QSurface::SurfaceType surfaceType() const = 0;
    virtual QSize size() const = 0;
%If (Qt_5_3_0 -)
    bool supportsOpenGL() const;
%End

protected:
    explicit QSurface(QSurface::SurfaceClass type);
};
