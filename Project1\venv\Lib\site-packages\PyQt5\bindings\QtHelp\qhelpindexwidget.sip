// qhelpindexwidget.sip generated by MetaSIP
//
// This file is part of the QtHelp Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHelpIndexModel : public QStringListModel /NoDefaultCtors/
{
%TypeHeaderCode
#include <qhelpindexwidget.h>
%End

public:
%If (Qt_5_15_0 -)
    QHelpEngineCore *helpEngine() const;
%End
    void createIndex(const QString &customFilterName);
    QModelIndex filter(const QString &filter, const QString &wildcard = QString());
    QMap<QString, QUrl> linksForKeyword(const QString &keyword) const;
    bool isCreatingIndex() const;

signals:
    void indexCreationStarted();
    void indexCreated();

private:
    virtual ~QHelpIndexModel();
};

class QHelpIndexWidget : public QListView
{
%TypeHeaderCode
#include <qhelpindexwidget.h>
%End

signals:
    void linkActivated(const QUrl &link, const QString &keyword);
    void linksActivated(const QMap<QString, QUrl> &links, const QString &keyword);

public slots:
    void filterIndices(const QString &filter, const QString &wildcard = QString());
    void activateCurrentItem();

signals:
%If (Qt_5_15_0 -)
    void documentActivated(const QHelpLink &document, const QString &keyword);
%End
%If (Qt_5_15_0 -)
    void documentsActivated(const QList<QHelpLink> &documents, const QString &keyword);
%End

private:
    QHelpIndexWidget();
};
