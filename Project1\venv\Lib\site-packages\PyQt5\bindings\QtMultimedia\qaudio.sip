// qaudio.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QAudio
{
%TypeHeaderCode
#include <qaudio.h>
%End

    enum Error
    {
        NoError,
        <PERSON>Error,
        <PERSON><PERSON><PERSON><PERSON>,
        Underrun<PERSON><PERSON><PERSON>,
        Fatal<PERSON><PERSON><PERSON>,
    };

    enum State
    {
        ActiveState,
        SuspendedState,
        StoppedState,
        IdleState,
%If (Qt_5_10_0 -)
        InterruptedState,
%End
    };

    enum Mode
    {
        AudioInput,
        AudioOutput,
    };

%If (Qt_5_6_0 -)

    enum Role
    {
        UnknownRole,
        MusicRole,
        VideoRole,
        VoiceCommunicationRole,
        AlarmRole,
        NotificationRole,
        RingtoneRole,
        AccessibilityRole,
        SonificationRole,
        GameRole,
%If (Qt_5_11_0 -)
        CustomRole,
%End
    };

%End
%If (Qt_5_8_0 -)

    enum VolumeScale
    {
        LinearVolumeScale,
        CubicVolumeScale,
        LogarithmicVolumeScale,
        DecibelVolumeScale,
    };

%End
%If (Qt_5_8_0 -)
    qreal convertVolume(qreal volume, QAudio::VolumeScale from, QAudio::VolumeScale to);
%End
};
