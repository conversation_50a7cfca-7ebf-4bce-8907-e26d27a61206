// qaudiodecodercontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAudioDecoderControl : public QMediaControl
{
%TypeHeaderCode
#include <qaudiodecodercontrol.h>
%End

public:
    virtual ~QAudioDecoderControl();
    virtual QAudioDecoder::State state() const = 0;
    virtual QString sourceFilename() const = 0;
    virtual void setSourceFilename(const QString &fileName) = 0;
    virtual QIODevice *sourceDevice() const = 0;
    virtual void setSourceDevice(QIODevice *device) = 0;
    virtual void start() = 0;
    virtual void stop() = 0;
    virtual QAudioFormat audioFormat() const = 0;
    virtual void setAudioFormat(const QAudioFormat &format) = 0;
    virtual QAudioBuffer read() = 0;
    virtual bool bufferAvailable() const = 0;
    virtual qint64 position() const = 0;
    virtual qint64 duration() const = 0;

signals:
    void stateChanged(QAudioDecoder::State newState);
    void formatChanged(const QAudioFormat &format);
    void sourceChanged();
    void error(int error, const QString &errorString);
    void bufferReady();
    void bufferAvailableChanged(bool available);
    void finished();
    void positionChanged(qint64 position);
    void durationChanged(qint64 duration);

protected:
    explicit QAudioDecoderControl(QObject *parent /TransferThis/ = 0);
};
