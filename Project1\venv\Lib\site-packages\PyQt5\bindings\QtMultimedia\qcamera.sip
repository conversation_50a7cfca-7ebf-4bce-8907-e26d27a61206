// qcamera.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCamera : public QMediaObject
{
%TypeHeaderCode
#include <qcamera.h>
%End

public:
    enum Status
    {
        UnavailableStatus,
        UnloadedStatus,
        LoadingStatus,
        UnloadingStatus,
        LoadedStatus,
        StandbyStatus,
        StartingStatus,
        Stopping<PERSON>tatus,
        ActiveStatus,
    };

    enum State
    {
        UnloadedState,
        LoadedState,
        ActiveState,
    };

    enum CaptureMode
    {
        CaptureViewfinder,
        CaptureStillImage,
        CaptureVideo,
    };

    typedef QFlags<QCamera::CaptureMode> CaptureModes;

    enum Error
    {
        NoError,
        CameraError,
        InvalidRequestError,
        ServiceMissingError,
        NotSupportedFeatureError,
    };

    enum LockStatus
    {
        Unlocked,
        Searching,
        Locked,
    };

    enum LockChangeReason
    {
        UserRequest,
        LockAcquired,
        LockFailed,
        LockLost,
        LockTemporaryLost,
    };

    enum LockType
    {
        NoLock,
        LockExposure,
        LockWhiteBalance,
        LockFocus,
    };

    typedef QFlags<QCamera::LockType> LockTypes;
%If (Qt_5_3_0 -)

    enum Position
    {
        UnspecifiedPosition,
        BackFace,
        FrontFace,
    };

%End
%If (Qt_5_6_1 -)
    explicit QCamera(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QCamera(QObject *parent /TransferThis/ = 0);
%End
    QCamera(const QByteArray &device, QObject *parent /TransferThis/ = 0);
%If (Qt_5_3_0 -)
    QCamera(const QCameraInfo &cameraInfo, QObject *parent /TransferThis/ = 0);
%End
%If (Qt_5_3_0 -)
    QCamera(QCamera::Position position, QObject *parent /TransferThis/ = 0);
%End
    virtual ~QCamera();
    static QList<QByteArray> availableDevices();
    static QString deviceDescription(const QByteArray &device);
    virtual QMultimedia::AvailabilityStatus availability() const;
    QCamera::State state() const;
    QCamera::Status status() const;
    QCamera::CaptureModes captureMode() const;
    bool isCaptureModeSupported(QCamera::CaptureModes mode) const;
    QCameraExposure *exposure() const;
    QCameraFocus *focus() const;
    QCameraImageProcessing *imageProcessing() const;
    void setViewfinder(QVideoWidget *viewfinder);
    void setViewfinder(QGraphicsVideoItem *viewfinder);
    void setViewfinder(QAbstractVideoSurface *surface);
    QCamera::Error error() const;
    QString errorString() const;
    QCamera::LockTypes supportedLocks() const;
    QCamera::LockTypes requestedLocks() const;
    QCamera::LockStatus lockStatus() const;
    QCamera::LockStatus lockStatus(QCamera::LockType lock) const;

public slots:
    void setCaptureMode(QCamera::CaptureModes mode);
    void load();
    void unload();
    void start();
    void stop();
    void searchAndLock();
    void unlock();
    void searchAndLock(QCamera::LockTypes locks);
    void unlock(QCamera::LockTypes locks);

signals:
    void stateChanged(QCamera::State);
    void captureModeChanged(QCamera::CaptureModes);
    void statusChanged(QCamera::Status);
    void locked();
    void lockFailed();
    void lockStatusChanged(QCamera::LockStatus, QCamera::LockChangeReason);
    void lockStatusChanged(QCamera::LockType, QCamera::LockStatus, QCamera::LockChangeReason);
    void error(QCamera::Error);
%If (Qt_5_15_0 -)
    void errorOccurred(QCamera::Error);
%End

public:
%If (Qt_5_5_0 -)

    struct FrameRateRange
    {
%TypeHeaderCode
#include <qcamera.h>
%End

        FrameRateRange(qreal minimum, qreal maximum);
        FrameRateRange();
        qreal minimumFrameRate;
        qreal maximumFrameRate;
    };

%End
%If (Qt_5_5_0 -)
    QCameraViewfinderSettings viewfinderSettings() const;
%End
%If (Qt_5_5_0 -)
    void setViewfinderSettings(const QCameraViewfinderSettings &settings);
%End
%If (Qt_5_5_0 -)
    QList<QCameraViewfinderSettings> supportedViewfinderSettings(const QCameraViewfinderSettings &settings = QCameraViewfinderSettings()) const;
%End
%If (Qt_5_5_0 -)
    QList<QSize> supportedViewfinderResolutions(const QCameraViewfinderSettings &settings = QCameraViewfinderSettings()) const;
%End
%If (Qt_5_5_0 -)
    QList<QCamera::FrameRateRange> supportedViewfinderFrameRateRanges(const QCameraViewfinderSettings &settings = QCameraViewfinderSettings()) const;
%End
%If (Qt_5_5_0 -)
    QList<QVideoFrame::PixelFormat> supportedViewfinderPixelFormats(const QCameraViewfinderSettings &settings = QCameraViewfinderSettings()) const;
%End
};

QFlags<QCamera::LockType> operator|(QCamera::LockType f1, QFlags<QCamera::LockType> f2);
%If (Qt_5_5_0 -)
bool operator==(const QCamera::FrameRateRange &r1, const QCamera::FrameRateRange &r2);
%End
%If (Qt_5_5_0 -)
bool operator!=(const QCamera::FrameRateRange &r1, const QCamera::FrameRateRange &r2);
%End
