// qsoundeffect.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSoundEffect : public QObject
{
%TypeHeaderCode
#include <qsoundeffect.h>
%End

public:
    enum Loop
    {
        Infinite,
    };

    enum Status
    {
        Null,
        Loading,
        Ready,
        Error,
    };

    explicit QSoundEffect(QObject *parent /TransferThis/ = 0);
%If (Qt_5_13_0 -)
    QSoundEffect(const QAudioDeviceInfo &audioDevice, QObject *parent /TransferThis/ = 0);
%End
    virtual ~QSoundEffect();
    static QStringList supportedMimeTypes();
    QUrl source() const;
    void setSource(const QUrl &url);
    int loopCount() const;
    int loopsRemaining() const;
    void setLoopCount(int loopCount);
    qreal volume() const;
    void setVolume(qreal volume);
    bool isMuted() const;
    void setMuted(bool muted);
    bool isLoaded() const;
    bool isPlaying() const;
    QSoundEffect::Status status() const;
    QString category() const;
    void setCategory(const QString &category);

signals:
    void sourceChanged();
    void loopCountChanged();
    void loopsRemainingChanged();
    void volumeChanged();
    void mutedChanged();
    void loadedChanged();
    void playingChanged();
    void statusChanged();
    void categoryChanged();

public slots:
    void play();
    void stop();
};
