// qgeopath.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_9_0 -)

class QGeoPath : public QGeoShape
{
%TypeHeaderCode
#include <qgeopath.h>
%End

public:
    QGeoPath();
    QGeoPath(const QList<QGeoCoordinate> &path, const qreal &width = 0.);
    QGeoPath(const QGeoPath &other);
    QGeoPath(const QGeoShape &other);
    ~QGeoPath();
    bool operator==(const QGeoPath &other) const;
    bool operator!=(const QGeoPath &other) const;
    void setPath(const QList<QGeoCoordinate> &path);
    const QList<QGeoCoordinate> &path() const;
    void setWidth(const qreal &width);
    qreal width() const;
    void translate(double degreesLatitude, double degreesLongitude);
    QGeoPath translated(double degreesLatitude, double degreesLongitude) const;
    double length(int indexFrom = 0, int indexTo = -1) const;
    void addCoordinate(const QGeoCoordinate &coordinate);
    void insertCoordinate(int index, const QGeoCoordinate &coordinate);
    void replaceCoordinate(int index, const QGeoCoordinate &coordinate);
    QGeoCoordinate coordinateAt(int index) const;
    bool containsCoordinate(const QGeoCoordinate &coordinate) const;
    void removeCoordinate(const QGeoCoordinate &coordinate);
    void removeCoordinate(int index);
    QString toString() const;
%If (Qt_5_10_0 -)
    int size() const;
%End
%If (Qt_5_12_0 -)
    void clearPath();
%End
};

%End
