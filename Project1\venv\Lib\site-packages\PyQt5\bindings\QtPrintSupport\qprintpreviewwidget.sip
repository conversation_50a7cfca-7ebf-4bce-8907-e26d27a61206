// qprintpreviewwidget.sip generated by MetaSIP
//
// This file is part of the QtPrintSupport Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_PrintPreviewWidget)

class QPrintPreviewWidget : public QWidget
{
%TypeHeaderCode
#include <qprintpreviewwidget.h>
%End

public:
    enum ViewMode
    {
        SinglePageView,
        FacingPagesView,
        AllPagesView,
    };

    enum ZoomMode
    {
        CustomZoom,
        FitToWidth,
        FitInView,
    };

    QPrintPreviewWidget(QPrinter *printer, QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    QPrintPreviewWidget(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QPrintPreviewWidget();
    qreal zoomFactor() const;
    QPrinter::Orientation orientation() const;
    QPrintPreviewWidget::ViewMode viewMode() const;
    QPrintPreviewWidget::ZoomMode zoomMode() const;
    int currentPage() const;

public slots:
    virtual void setVisible(bool visible);
    void print() /PyName=print_/;
%If (Py_v3)
    void print();
%End
    void zoomIn(qreal factor = 1.1);
    void zoomOut(qreal factor = 1.1);
    void setZoomFactor(qreal zoomFactor);
    void setOrientation(QPrinter::Orientation orientation);
    void setViewMode(QPrintPreviewWidget::ViewMode viewMode);
    void setZoomMode(QPrintPreviewWidget::ZoomMode zoomMode);
    void setCurrentPage(int pageNumber);
    void fitToWidth();
    void fitInView();
    void setLandscapeOrientation();
    void setPortraitOrientation();
    void setSinglePageViewMode();
    void setFacingPagesViewMode();
    void setAllPagesViewMode();
    void updatePreview();

signals:
    void paintRequested(QPrinter *printer);
    void previewChanged();

public:
    int pageCount() const;
};

%End
