// qqmlscriptstring.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlScriptString
{
%TypeHeaderCode
#include <qqmlscriptstring.h>
%End

public:
    QQmlScriptString();
    QQmlScriptString(const QQmlScriptString &);
    ~QQmlScriptString();
    bool isEmpty() const;
    bool isUndefinedLiteral() const;
    bool isNullLiteral() const;
    QString stringLiteral() const;
    qreal numberLiteral(bool *ok) const;
    bool booleanLiteral(bool *ok) const;
%If (Qt_5_4_0 -)
    bool operator==(const QQmlScriptString &) const;
%End
%If (Qt_5_4_0 -)
    bool operator!=(const QQmlScriptString &) const;
%End
};
