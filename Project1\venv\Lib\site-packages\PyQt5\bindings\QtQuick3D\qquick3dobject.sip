// qquick3dobject.sip generated by MetaSIP
//
// This file is part of the QtQuick3D Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_15_0 -)

class QQuick3DObject : public QObject, public QQmlParserStatus /Abstract/
{
%TypeHeaderCode
#include <qquick3dobject.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QQuick3DObject, &sipType_QQuick3DObject, 1, -1},
        {sipName_QQuick3DGeometry, &sipType_QQuick3DGeometry, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    explicit QQuick3DObject(QQuick3DObject *parent /TransferThis/ = 0);
    virtual ~QQuick3DObject();
    QString state() const;
    void setState(const QString &state);
    QQuick3DObject *parentItem() const;

public slots:
    void setParentItem(QQuick3DObject *parentItem);

signals:
    void stateChanged();

protected:
    virtual void classBegin();
    virtual void componentComplete();
};

%End
