// qserialport.sip generated by MetaSIP
//
// This file is part of the QtSerialPort Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)

class QSerialPort : public QIODevice
{
%TypeHeaderCode
#include <qserialport.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QSerialPort, &sipType_QSerialPort, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum Direction
    {
        Input,
        Output,
        AllDirections,
    };

    typedef QFlags<QSerialPort::Direction> Directions;

    enum BaudRate
    {
        Baud1200,
        Baud2400,
        Baud4800,
        Baud9600,
        Baud19200,
        Baud38400,
        Baud57600,
        Baud115200,
        UnknownBaud,
    };

    enum DataBits
    {
        Data5,
        Data6,
        Data7,
        Data8,
        UnknownDataBits,
    };

    enum Parity
    {
        NoParity,
        EvenParity,
        OddParity,
        SpaceParity,
        MarkParity,
        UnknownParity,
    };

    enum StopBits
    {
        OneStop,
        OneAndHalfStop,
        TwoStop,
        UnknownStopBits,
    };

    enum FlowControl
    {
        NoFlowControl,
        HardwareControl,
        SoftwareControl,
        UnknownFlowControl,
    };

    enum PinoutSignal
    {
        NoSignal,
        TransmittedDataSignal,
        ReceivedDataSignal,
        DataTerminalReadySignal,
        DataCarrierDetectSignal,
        DataSetReadySignal,
        RingIndicatorSignal,
        RequestToSendSignal,
        ClearToSendSignal,
        SecondaryTransmittedDataSignal,
        SecondaryReceivedDataSignal,
    };

    typedef QFlags<QSerialPort::PinoutSignal> PinoutSignals;

    enum DataErrorPolicy
    {
        SkipPolicy,
        PassZeroPolicy,
        IgnorePolicy,
        StopReceivingPolicy,
        UnknownPolicy,
    };

    enum SerialPortError
    {
        NoError,
        DeviceNotFoundError,
        PermissionError,
        OpenError,
        ParityError,
        FramingError,
        BreakConditionError,
        WriteError,
        ReadError,
        ResourceError,
        UnsupportedOperationError,
%If (Qt_5_2_0 -)
        TimeoutError,
%End
%If (Qt_5_2_0 -)
        NotOpenError,
%End
        UnknownError,
    };

    explicit QSerialPort(QObject *parent /TransferThis/ = 0);
    QSerialPort(const QString &name, QObject *parent /TransferThis/ = 0);
    QSerialPort(const QSerialPortInfo &info, QObject *parent /TransferThis/ = 0);
    virtual ~QSerialPort();
    void setPortName(const QString &name);
    QString portName() const;
    void setPort(const QSerialPortInfo &info);
    virtual bool open(QIODevice::OpenMode mode);
    virtual void close() /ReleaseGIL/;
    void setSettingsRestoredOnClose(bool restore);
    bool settingsRestoredOnClose() const;
    bool setBaudRate(qint32 baudRate, QSerialPort::Directions dir = QSerialPort::AllDirections);
    qint32 baudRate(QSerialPort::Directions dir = QSerialPort::AllDirections) const;
    bool setDataBits(QSerialPort::DataBits dataBits);
    QSerialPort::DataBits dataBits() const;
    bool setParity(QSerialPort::Parity parity);
    QSerialPort::Parity parity() const;
    bool setStopBits(QSerialPort::StopBits stopBits);
    QSerialPort::StopBits stopBits() const;
    bool setFlowControl(QSerialPort::FlowControl flow);
    QSerialPort::FlowControl flowControl() const;
    bool setDataTerminalReady(bool set);
    bool isDataTerminalReady();
    bool setRequestToSend(bool set);
    bool isRequestToSend();
    QSerialPort::PinoutSignals pinoutSignals();
    bool flush() /ReleaseGIL/;
    bool clear(QSerialPort::Directions dir = QSerialPort::AllDirections);
    virtual bool atEnd() const;
    bool setDataErrorPolicy(QSerialPort::DataErrorPolicy policy = QSerialPort::IgnorePolicy);
    QSerialPort::DataErrorPolicy dataErrorPolicy() const;
    QSerialPort::SerialPortError error() const;
    void clearError();
    qint64 readBufferSize() const;
    void setReadBufferSize(qint64 size);
    virtual bool isSequential() const;
    virtual qint64 bytesAvailable() const;
    virtual qint64 bytesToWrite() const;
    virtual bool canReadLine() const;
%If (- Qt_5_11_0)
    virtual bool waitForReadyRead(int msecs) /ReleaseGIL/;
%End
%If (Qt_5_11_0 -)
    virtual bool waitForReadyRead(int msecs = 30000) /ReleaseGIL/;
%End
%If (- Qt_5_11_0)
    virtual bool waitForBytesWritten(int msecs) /ReleaseGIL/;
%End
%If (Qt_5_11_0 -)
    virtual bool waitForBytesWritten(int msecs = 30000) /ReleaseGIL/;
%End
    bool sendBreak(int duration = 0) /ReleaseGIL/;
    bool setBreakEnabled(bool enabled = true);

signals:
    void baudRateChanged(qint32 baudRate, QSerialPort::Directions directions);
    void dataBitsChanged(QSerialPort::DataBits dataBits);
    void parityChanged(QSerialPort::Parity parity);
    void stopBitsChanged(QSerialPort::StopBits stopBits);
    void flowControlChanged(QSerialPort::FlowControl flow);
    void dataErrorPolicyChanged(QSerialPort::DataErrorPolicy policy);
    void dataTerminalReadyChanged(bool set);
    void requestToSendChanged(bool set);
    void error(QSerialPort::SerialPortError serialPortError);
    void settingsRestoredOnCloseChanged(bool restore);

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxSize)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QSerialPort::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual SIP_PYOBJECT readLineData(qint64 maxlen) /TypeHint="Py_v3:bytes;str",ReleaseGIL/ [qint64 (char *data, qint64 maxSize)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QSerialPort::readLineData(s, a0) : sipCpp->readLineData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readLineData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 maxSize /ArraySize/) /ReleaseGIL/;

public:
%If (Qt_5_2_0 -)
%If (WS_WIN)
    void *handle() const;
%End
%End
%If (Qt_5_2_0 -)
%If (WS_X11 || WS_MACX)
    int handle() const;
%End
%End
%If (Qt_5_5_0 -)
    bool isBreakEnabled() const;
%End

signals:
%If (Qt_5_5_0 -)
    void breakEnabledChanged(bool set);
%End
%If (Qt_5_8_0 -)
    void errorOccurred(QSerialPort::SerialPortError error);
%End
};

%End
%If (Qt_5_1_0 -)
QFlags<QSerialPort::Direction> operator|(QSerialPort::Direction f1, QFlags<QSerialPort::Direction> f2);
%End
%If (Qt_5_1_0 -)
QFlags<QSerialPort::PinoutSignal> operator|(QSerialPort::PinoutSignal f1, QFlags<QSerialPort::PinoutSignal> f2);
%End
