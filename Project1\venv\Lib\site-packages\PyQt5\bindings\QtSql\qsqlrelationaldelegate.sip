// qsqlrelationaldelegate.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlRelationalDelegate : public QItemDelegate
{
%TypeHeaderCode
#include <qsqlrelationaldelegate.h>
%End

public:
    explicit QSqlRelationalDelegate(QObject *parent /TransferThis/ = 0);
    virtual ~QSqlRelationalDelegate();
    virtual QWidget *createEditor(QWidget *parent /TransferThis/, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const /Factory/;
    virtual void setModelData(QWidget *editor, QAbstractItemModel *model /KeepReference/, const QModelIndex &index) const;
%If (Qt_5_12_0 -)
    virtual void setEditorData(QWidget *editor, const QModelIndex &index) const;
%End
};
