// qabstractitemmodeltester.sip generated by MetaSIP
//
// This file is part of the QtTest Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_11_0 -)

class QAbstractItemModelTester : public QObject
{
%TypeHeaderCode
// Qt v5.11.0 needs this.
#include <qabstractitemmodel.h>

#include <qabstractitemmodeltester.h>
%End

public:
    enum class FailureReportingMode
    {
        QtTest,
        Warning,
        Fatal,
    };

    QAbstractItemModelTester(QAbstractItemModel *model /KeepReference=1/, QObject *parent /TransferThis/ = 0);
    QAbstractItemModelTester(QAbstractItemModel *model /KeepReference=1/, QAbstractItemModelTester::FailureReportingMode mode, QObject *parent /TransferThis/ = 0);
    QAbstractItemModel *model() const;
    QAbstractItemModelTester::FailureReportingMode failureReportingMode() const;
};

%End
