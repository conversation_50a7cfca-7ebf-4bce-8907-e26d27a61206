// qgraphicsanchorlayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsAnchor : public QObject
{
%TypeHeaderCode
#include <qgraphicsanchorlayout.h>
%End

public:
    virtual ~QGraphicsAnchor();
    void setSpacing(qreal spacing);
    void unsetSpacing();
    qreal spacing() const;
    void setSizePolicy(QSizePolicy::Policy policy);
    QSizePolicy::Policy sizePolicy() const;

private:
    QGraphicsAnchor(QGraphicsAnchorLayout *parent);
};

class QGraphicsAnchorLayout : public QGraphicsLayout
{
%TypeHeaderCode
#include <qgraphicsanchorlayout.h>
%End

public:
    QGraphicsAnchorLayout(QGraphicsLayoutItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsAnchorLayout();
    QGraphicsAnchor *addAnchor(QGraphicsLayoutItem *firstItem /Transfer/, Qt::AnchorPoint firstEdge, QGraphicsLayoutItem *secondItem /Transfer/, Qt::AnchorPoint secondEdge);
    QGraphicsAnchor *anchor(QGraphicsLayoutItem *firstItem, Qt::AnchorPoint firstEdge, QGraphicsLayoutItem *secondItem, Qt::AnchorPoint secondEdge);
    void addCornerAnchors(QGraphicsLayoutItem *firstItem /Transfer/, Qt::Corner firstCorner, QGraphicsLayoutItem *secondItem /Transfer/, Qt::Corner secondCorner);
    void addAnchors(QGraphicsLayoutItem *firstItem /Transfer/, QGraphicsLayoutItem *secondItem /Transfer/, Qt::Orientations orientations = Qt::Horizontal | Qt::Vertical);
    void setHorizontalSpacing(qreal spacing);
    void setVerticalSpacing(qreal spacing);
    void setSpacing(qreal spacing);
    qreal horizontalSpacing() const;
    qreal verticalSpacing() const;
    virtual void removeAt(int index);
    virtual void setGeometry(const QRectF &rect);
    virtual int count() const;
    virtual QGraphicsLayoutItem *itemAt(int index) const;
    virtual void invalidate();

protected:
    virtual QSizeF sizeHint(Qt::SizeHint which, const QSizeF &constraint = QSizeF()) const;
};
