// qgraphicsview.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsView : public QAbstractScrollArea
{
%TypeHeaderCode
#include <qgraphicsview.h>
%End

public:
    enum CacheModeFlag
    {
        CacheNone,
        CacheBackground,
    };

    typedef QFlags<QGraphicsView::CacheModeFlag> CacheMode;

    enum DragMode
    {
        NoDrag,
        ScrollHandDrag,
        RubberBandDrag,
    };

    enum ViewportAnchor
    {
        NoAnchor,
        AnchorViewCenter,
        AnchorUnderMouse,
    };

    QGraphicsView(QWidget *parent /TransferThis/ = 0);
    QGraphicsView(QGraphicsScene *scene /KeepReference/, QWidget *parent /TransferThis/ = 0);
    virtual ~QGraphicsView();
    virtual QSize sizeHint() const;
    QPainter::RenderHints renderHints() const;
    void setRenderHint(QPainter::RenderHint hint, bool on = true);
    void setRenderHints(QPainter::RenderHints hints);
    Qt::Alignment alignment() const;
    void setAlignment(Qt::Alignment alignment);
    QGraphicsView::ViewportAnchor transformationAnchor() const;
    void setTransformationAnchor(QGraphicsView::ViewportAnchor anchor);
    QGraphicsView::ViewportAnchor resizeAnchor() const;
    void setResizeAnchor(QGraphicsView::ViewportAnchor anchor);
    QGraphicsView::DragMode dragMode() const;
    void setDragMode(QGraphicsView::DragMode mode);
    QGraphicsView::CacheMode cacheMode() const;
    void setCacheMode(QGraphicsView::CacheMode mode);
    void resetCachedContent();
    bool isInteractive() const;
    void setInteractive(bool allowed);
    QGraphicsScene *scene() const;
    void setScene(QGraphicsScene *scene /KeepReference/);
    QRectF sceneRect() const;
    void setSceneRect(const QRectF &rect);
    void rotate(qreal angle);
    void scale(qreal sx, qreal sy);
    void shear(qreal sh, qreal sv);
    void translate(qreal dx, qreal dy);
    void centerOn(const QPointF &pos);
    void centerOn(const QGraphicsItem *item);
    void ensureVisible(const QRectF &rect, int xMargin = 50, int yMargin = 50);
    void ensureVisible(const QGraphicsItem *item, int xMargin = 50, int yMargin = 50);
    void fitInView(const QRectF &rect, Qt::AspectRatioMode mode = Qt::IgnoreAspectRatio);
    void fitInView(const QGraphicsItem *item, Qt::AspectRatioMode mode = Qt::IgnoreAspectRatio);
    void render(QPainter *painter, const QRectF &target = QRectF(), const QRect &source = QRect(), Qt::AspectRatioMode mode = Qt::KeepAspectRatio);
    QList<QGraphicsItem *> items() const;
    QList<QGraphicsItem *> items(const QPoint &pos) const;
    QList<QGraphicsItem *> items(int x, int y) const;
    QList<QGraphicsItem *> items(int x, int y, int w, int h, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape) const;
    QList<QGraphicsItem *> items(const QRect &rect, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape) const;
    QList<QGraphicsItem *> items(const QPolygon &polygon, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape) const;
    QList<QGraphicsItem *> items(const QPainterPath &path, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape) const;
    QGraphicsItem *itemAt(const QPoint &pos) const;
    QPointF mapToScene(const QPoint &point) const;
    QPolygonF mapToScene(const QRect &rect) const;
    QPolygonF mapToScene(const QPolygon &polygon) const;
    QPainterPath mapToScene(const QPainterPath &path) const;
    QPoint mapFromScene(const QPointF &point) const;
    QPolygon mapFromScene(const QRectF &rect) const;
    QPolygon mapFromScene(const QPolygonF &polygon) const;
    QPainterPath mapFromScene(const QPainterPath &path) const;
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery query) const;
    QBrush backgroundBrush() const;
    void setBackgroundBrush(const QBrush &brush);
    QBrush foregroundBrush() const;
    void setForegroundBrush(const QBrush &brush);

public slots:
    void invalidateScene(const QRectF &rect = QRectF(), QGraphicsScene::SceneLayers layers = QGraphicsScene::AllLayers);
    void updateScene(const QList<QRectF> &rects);
    void updateSceneRect(const QRectF &rect);

protected slots:
    virtual void setupViewport(QWidget *widget);

protected:
    virtual bool event(QEvent *event);
    virtual bool viewportEvent(QEvent *event);
    virtual void contextMenuEvent(QContextMenuEvent *event);
    virtual void dragEnterEvent(QDragEnterEvent *event);
    virtual void dragLeaveEvent(QDragLeaveEvent *event);
    virtual void dragMoveEvent(QDragMoveEvent *event);
    virtual void dropEvent(QDropEvent *event);
    virtual void focusInEvent(QFocusEvent *event);
    virtual void focusOutEvent(QFocusEvent *event);
    virtual bool focusNextPrevChild(bool next);
    virtual void keyPressEvent(QKeyEvent *event);
    virtual void keyReleaseEvent(QKeyEvent *event);
    virtual void mouseDoubleClickEvent(QMouseEvent *event);
    virtual void mousePressEvent(QMouseEvent *event);
    virtual void mouseMoveEvent(QMouseEvent *event);
    virtual void mouseReleaseEvent(QMouseEvent *event);
    virtual void wheelEvent(QWheelEvent *event);
    virtual void paintEvent(QPaintEvent *event);
    virtual void resizeEvent(QResizeEvent *event);
    virtual void scrollContentsBy(int dx, int dy);
    virtual void showEvent(QShowEvent *event);
    virtual void inputMethodEvent(QInputMethodEvent *event);
    virtual void drawBackground(QPainter *painter, const QRectF &rect);
    virtual void drawForeground(QPainter *painter, const QRectF &rect);

public:
    void setSceneRect(qreal ax, qreal ay, qreal aw, qreal ah);
    void centerOn(qreal ax, qreal ay);
    void ensureVisible(qreal x, qreal y, qreal w, qreal h, int xMargin = 50, int yMargin = 50);
    void fitInView(qreal x, qreal y, qreal w, qreal h, Qt::AspectRatioMode mode = Qt::IgnoreAspectRatio);
    QGraphicsItem *itemAt(int ax, int ay) const;
    QPointF mapToScene(int ax, int ay) const;
    QPolygonF mapToScene(int ax, int ay, int w, int h) const;
    QPoint mapFromScene(qreal ax, qreal ay) const;
    QPolygon mapFromScene(qreal ax, qreal ay, qreal w, qreal h) const;

    enum ViewportUpdateMode
    {
        FullViewportUpdate,
        MinimalViewportUpdate,
        SmartViewportUpdate,
        BoundingRectViewportUpdate,
        NoViewportUpdate,
    };

    enum OptimizationFlag
    {
        DontClipPainter,
        DontSavePainterState,
        DontAdjustForAntialiasing,
    };

    typedef QFlags<QGraphicsView::OptimizationFlag> OptimizationFlags;
    QGraphicsView::ViewportUpdateMode viewportUpdateMode() const;
    void setViewportUpdateMode(QGraphicsView::ViewportUpdateMode mode);
    QGraphicsView::OptimizationFlags optimizationFlags() const;
    void setOptimizationFlag(QGraphicsView::OptimizationFlag flag, bool enabled = true);
    void setOptimizationFlags(QGraphicsView::OptimizationFlags flags);
    Qt::ItemSelectionMode rubberBandSelectionMode() const;
    void setRubberBandSelectionMode(Qt::ItemSelectionMode mode);
    QTransform transform() const;
    QTransform viewportTransform() const;
    void setTransform(const QTransform &matrix, bool combine = false);
    void resetTransform();
    bool isTransformed() const;
%If (Qt_5_1_0 -)
    QRect rubberBandRect() const;
%End

signals:
%If (Qt_5_1_0 -)
    void rubberBandChanged(QRect viewportRect, QPointF fromScenePoint, QPointF toScenePoint);
%End
};

QFlags<QGraphicsView::CacheModeFlag> operator|(QGraphicsView::CacheModeFlag f1, QFlags<QGraphicsView::CacheModeFlag> f2);
QFlags<QGraphicsView::OptimizationFlag> operator|(QGraphicsView::OptimizationFlag f1, QFlags<QGraphicsView::OptimizationFlag> f2);
