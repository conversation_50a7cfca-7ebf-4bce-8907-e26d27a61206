// qinputdialog.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QInputDialog : public QDialog
{
%TypeHeaderCode
#include <qinputdialog.h>
%End

public:
    enum InputDialogOption
    {
        NoButtons,
        UseListViewForComboBoxItems,
%If (Qt_5_2_0 -)
        UsePlainTextEditForTextInput,
%End
    };

    typedef QFlags<QInputDialog::InputDialogOption> InputDialogOptions;

    enum InputMode
    {
        TextInput,
        IntInput,
        DoubleInput,
    };

    static QString getText(QWidget *parent, const QString &title, const QString &label, QLineEdit::EchoMode echo = QLineEdit::Normal, const QString &text = QString(), bool *ok = 0, Qt::WindowFlags flags = Qt::WindowFlags(), Qt::InputMethodHints inputMethodHints = Qt::ImhNone) /ReleaseGIL/;
    static int getInt(QWidget *parent, const QString &title, const QString &label, int value = 0, int min = -2147483647, int max = 2147483647, int step = 1, bool *ok = 0, Qt::WindowFlags flags = Qt::WindowFlags()) /ReleaseGIL/;
    static double getDouble(QWidget *parent, const QString &title, const QString &label, double value = 0, double min = -2147483647, double max = 2147483647, int decimals = 1, bool *ok = 0, Qt::WindowFlags flags = Qt::WindowFlags()) /ReleaseGIL/;
%If (Qt_5_10_0 -)
    static double getDouble(QWidget *parent, const QString &title, const QString &label, double value, double minValue, double maxValue, int decimals, bool *ok, Qt::WindowFlags flags, double step);
%End
    static QString getItem(QWidget *parent, const QString &title, const QString &label, const QStringList &items, int current = 0, bool editable = true, bool *ok = 0, Qt::WindowFlags flags = Qt::WindowFlags(), Qt::InputMethodHints inputMethodHints = Qt::ImhNone) /ReleaseGIL/;
%If (Qt_5_2_0 -)
    static QString getMultiLineText(QWidget *parent, const QString &title, const QString &label, const QString &text = QString(), bool *ok = 0, Qt::WindowFlags flags = Qt::WindowFlags(), Qt::InputMethodHints inputMethodHints = Qt::ImhNone) /ReleaseGIL/;
%End
    QInputDialog(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QInputDialog();
    void setInputMode(QInputDialog::InputMode mode);
    QInputDialog::InputMode inputMode() const;
    void setLabelText(const QString &text);
    QString labelText() const;
    void setOption(QInputDialog::InputDialogOption option, bool on = true);
    bool testOption(QInputDialog::InputDialogOption option) const;
    void setOptions(QInputDialog::InputDialogOptions options);
    QInputDialog::InputDialogOptions options() const;
    void setTextValue(const QString &text);
    QString textValue() const;
    void setTextEchoMode(QLineEdit::EchoMode mode);
    QLineEdit::EchoMode textEchoMode() const;
    void setComboBoxEditable(bool editable);
    bool isComboBoxEditable() const;
    void setComboBoxItems(const QStringList &items);
    QStringList comboBoxItems() const;
    void setIntValue(int value);
    int intValue() const;
    void setIntMinimum(int min);
    int intMinimum() const;
    void setIntMaximum(int max);
    int intMaximum() const;
    void setIntRange(int min, int max);
    void setIntStep(int step);
    int intStep() const;
    void setDoubleValue(double value);
    double doubleValue() const;
    void setDoubleMinimum(double min);
    double doubleMinimum() const;
    void setDoubleMaximum(double max);
    double doubleMaximum() const;
    void setDoubleRange(double min, double max);
    void setDoubleDecimals(int decimals);
    int doubleDecimals() const;
    void setOkButtonText(const QString &text);
    QString okButtonText() const;
    void setCancelButtonText(const QString &text);
    QString cancelButtonText() const;
    virtual void open();
    void open(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt5_qtwidgets_get_connection_parts(a0, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipCpp->open(receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    virtual QSize minimumSizeHint() const;
    virtual QSize sizeHint() const;
    virtual void setVisible(bool visible);
    virtual void done(int result);

signals:
    void textValueChanged(const QString &text);
    void textValueSelected(const QString &text);
    void intValueChanged(int value);
    void intValueSelected(int value);
    void doubleValueChanged(double value);
    void doubleValueSelected(double value);

public:
%If (Qt_5_10_0 -)
    void setDoubleStep(double step);
%End
%If (Qt_5_10_0 -)
    double doubleStep() const;
%End
};

QFlags<QInputDialog::InputDialogOption> operator|(QInputDialog::InputDialogOption f1, QFlags<QInputDialog::InputDialogOption> f2);
