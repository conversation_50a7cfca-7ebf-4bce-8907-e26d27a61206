// qopenglwidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)
%If (PyQt_OpenGL)

class QOpenGLWidget : public QWidget
{
%TypeHeaderCode
#include <qopenglwidget.h>
%End

public:
    QOpenGLWidget(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QOpenGLWidget();
    void setFormat(const QSurfaceFormat &format);
    QSurfaceFormat format() const;
    bool isValid() const;
    void makeCurrent();
    void doneCurrent();
    QOpenGLContext *context() const;
    GLuint defaultFramebufferObject() const;
    QImage grabFramebuffer();

signals:
    void aboutToCompose();
    void frameSwapped();
    void aboutToResize();
    void resized();

protected:
    virtual void initializeGL();
    virtual void resizeGL(int w, int h);
    virtual void paintGL();
    virtual void paintEvent(QPaintEvent *e);
    virtual void resizeEvent(QResizeEvent *e);
    virtual bool event(QEvent *e);
    virtual int metric(QPaintDevice::PaintDeviceMetric metric) const;
    virtual QPaintEngine *paintEngine() const;

public:
%If (Qt_5_5_0 -)

    enum UpdateBehavior
    {
        NoPartialUpdate,
        PartialUpdate,
    };

%End
%If (Qt_5_5_0 -)
    void setUpdateBehavior(QOpenGLWidget::UpdateBehavior updateBehavior);
%End
%If (Qt_5_5_0 -)
    QOpenGLWidget::UpdateBehavior updateBehavior() const;
%End
%If (Qt_5_10_0 -)
    GLenum textureFormat() const;
%End
%If (Qt_5_10_0 -)
    void setTextureFormat(GLenum texFormat);
%End
};

%End
%End
