// qxmlschema.sip generated by MetaSIP
//
// This file is part of the QtXmlPatterns Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlSchema
{
%TypeHeaderCode
#include <qxmlschema.h>
%End

public:
    QXmlSchema();
    QXmlSchema(const QXmlSchema &other);

private:
    QXmlSchema &operator=(const QXmlSchema &);

public:
    ~QXmlSchema();
    bool load(const QUrl &source) /ReleaseGIL/;
    bool load(QIODevice *source, const QUrl &documentUri = QUrl()) /ReleaseGIL/;
    bool load(const QByteArray &data, const QUrl &documentUri = QUrl());
    bool isValid() const;
    QXmlNamePool namePool() const;
    QUrl documentUri() const;
    void setMessageHandler(QAbstractMessageHandler *handler /KeepReference/);
    QAbstractMessageHandler *messageHandler() const;
    void setUriResolver(const QAbstractUriResolver *resolver /KeepReference/);
    const QAbstractUriResolver *uriResolver() const;
    void setNetworkAccessManager(QNetworkAccessManager *networkmanager /KeepReference/);
    QNetworkAccessManager *networkAccessManager() const;
};
