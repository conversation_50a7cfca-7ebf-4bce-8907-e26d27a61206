Metadata-Version: 2.1
Name: eval_type_backport
Version: 0.2.2
Summary: Like `typing._eval_type`, but lets older Python versions use newer typing features.
Home-page: https://github.com/alexmojaki/eval_type_backport
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE.txt
Provides-Extra: tests
Requires-Dist: pytest; extra == "tests"

# eval_type_backport

[![Build Status](https://github.com/alexmojaki/eval_type_backport/workflows/Tests/badge.svg)](https://github.com/alexmojaki/eval_type_backport/actions) [![Coverage Status](https://coveralls.io/repos/github/alexmojaki/eval_type_backport/badge.svg)](https://coveralls.io/github/alexmojaki/eval_type_backport) [![Supports Python versions 3.8+, including PyPy](https://img.shields.io/pypi/pyversions/eval_type_backport.svg)](https://pypi.python.org/pypi/eval_type_backport) [![Anaconda's conda-forge channel](https://anaconda.org/conda-forge/eval-type-backport/badges/version.svg)](https://anaconda.org/conda-forge/eval-type-backport)

This is a tiny package providing a replacement for `typing._eval_type` to support newer typing features in older Python versions.

Yes, that's very specific, and yes, `typing._eval_type` is a protected function that you shouldn't normally be using. Really this package is specifically made for https://github.com/pydantic/pydantic/issues/7873.

Specifically, this transforms `X | Y` into `typing.Union[X, Y]`
and `list[X]` into `typing.List[X]` etc. (for all the types made generic in PEP 585)
if the original syntax is not supported in the current Python version.

## Install

From PyPI:

```shell
pip install eval-type-backport
```

or with Conda:

```shell
conda install -c conda-forge eval-type-backport
```
