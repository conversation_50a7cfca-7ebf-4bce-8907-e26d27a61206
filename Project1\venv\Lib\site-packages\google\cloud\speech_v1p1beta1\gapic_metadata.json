{"comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "python", "libraryPackage": "google.cloud.speech_v1p1beta1", "protoPackage": "google.cloud.speech.v1p1beta1", "schema": "1.0", "services": {"Adaptation": {"clients": {"grpc": {"libraryClient": "AdaptationClient", "rpcs": {"CreateCustomClass": {"methods": ["create_custom_class"]}, "CreatePhraseSet": {"methods": ["create_phrase_set"]}, "DeleteCustomClass": {"methods": ["delete_custom_class"]}, "DeletePhraseSet": {"methods": ["delete_phrase_set"]}, "GetCustomClass": {"methods": ["get_custom_class"]}, "GetPhraseSet": {"methods": ["get_phrase_set"]}, "ListCustomClasses": {"methods": ["list_custom_classes"]}, "ListPhraseSet": {"methods": ["list_phrase_set"]}, "UpdateCustomClass": {"methods": ["update_custom_class"]}, "UpdatePhraseSet": {"methods": ["update_phrase_set"]}}}, "grpc-async": {"libraryClient": "AdaptationAsyncClient", "rpcs": {"CreateCustomClass": {"methods": ["create_custom_class"]}, "CreatePhraseSet": {"methods": ["create_phrase_set"]}, "DeleteCustomClass": {"methods": ["delete_custom_class"]}, "DeletePhraseSet": {"methods": ["delete_phrase_set"]}, "GetCustomClass": {"methods": ["get_custom_class"]}, "GetPhraseSet": {"methods": ["get_phrase_set"]}, "ListCustomClasses": {"methods": ["list_custom_classes"]}, "ListPhraseSet": {"methods": ["list_phrase_set"]}, "UpdateCustomClass": {"methods": ["update_custom_class"]}, "UpdatePhraseSet": {"methods": ["update_phrase_set"]}}}, "rest": {"libraryClient": "AdaptationClient", "rpcs": {"CreateCustomClass": {"methods": ["create_custom_class"]}, "CreatePhraseSet": {"methods": ["create_phrase_set"]}, "DeleteCustomClass": {"methods": ["delete_custom_class"]}, "DeletePhraseSet": {"methods": ["delete_phrase_set"]}, "GetCustomClass": {"methods": ["get_custom_class"]}, "GetPhraseSet": {"methods": ["get_phrase_set"]}, "ListCustomClasses": {"methods": ["list_custom_classes"]}, "ListPhraseSet": {"methods": ["list_phrase_set"]}, "UpdateCustomClass": {"methods": ["update_custom_class"]}, "UpdatePhraseSet": {"methods": ["update_phrase_set"]}}}}}, "Speech": {"clients": {"grpc": {"libraryClient": "SpeechClient", "rpcs": {"LongRunningRecognize": {"methods": ["long_running_recognize"]}, "Recognize": {"methods": ["recognize"]}, "StreamingRecognize": {"methods": ["streaming_recognize"]}}}, "grpc-async": {"libraryClient": "SpeechAsyncClient", "rpcs": {"LongRunningRecognize": {"methods": ["long_running_recognize"]}, "Recognize": {"methods": ["recognize"]}, "StreamingRecognize": {"methods": ["streaming_recognize"]}}}, "rest": {"libraryClient": "SpeechClient", "rpcs": {"LongRunningRecognize": {"methods": ["long_running_recognize"]}, "Recognize": {"methods": ["recognize"]}, "StreamingRecognize": {"methods": ["streaming_recognize"]}}}}}}}