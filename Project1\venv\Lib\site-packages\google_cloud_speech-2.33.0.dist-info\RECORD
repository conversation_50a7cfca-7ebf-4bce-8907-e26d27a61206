google/cloud/speech/__init__.py,sha256=aA6UhjQbidVIWyhFEsUbFEPVBtqWuWWT8rEXxxjNnw4,3274
google/cloud/speech/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/speech/gapic_version.py,sha256=zcFN_IeaOSUsAk922mORuJ00D3yffXVDPJPmEhO0y3A,653
google/cloud/speech/py.typed,sha256=9XnjN9OQ5mzBvUTS4LvkASo3060SLCUBOLJepHrwVDU,80
google/cloud/speech_v1/__init__.py,sha256=4eCu-WAiL3ZlDYLOS7QGRISeMuBzYG9n2naX4y4sC1c,3182
google/cloud/speech_v1/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/speech_v1/__pycache__/helpers.cpython-312.pyc,,
google/cloud/speech_v1/gapic_metadata.json,sha256=a-l-L2sMQfzpzutluqBp36Hj9_jpJ4i6xZTKXbOeHe4,5975
google/cloud/speech_v1/gapic_version.py,sha256=zcFN_IeaOSUsAk922mORuJ00D3yffXVDPJPmEhO0y3A,653
google/cloud/speech_v1/helpers.py,sha256=zg0hbqgPZPAVA2RGnPYpaUJNbL4ywt5UEvzLzX1tTJw,4114
google/cloud/speech_v1/py.typed,sha256=9XnjN9OQ5mzBvUTS4LvkASo3060SLCUBOLJepHrwVDU,80
google/cloud/speech_v1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/speech_v1/services/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/__init__.py,sha256=mc6LsNyncWccRfmKtQfcxrCI6W-4T8Kyir1Pto1c0LY,753
google/cloud/speech_v1/services/adaptation/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/__pycache__/async_client.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/__pycache__/client.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/__pycache__/pagers.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/async_client.py,sha256=Rrh8rokEbrVZPBWQRU_c9mQxTO6GM0rtGyOnobRet_0,73295
google/cloud/speech_v1/services/adaptation/client.py,sha256=aKgeLhR-Rq9BwjyazGqOEHxT92Qs8SBIQ_0fefBhhaY,89878
google/cloud/speech_v1/services/adaptation/pagers.py,sha256=kiz_lyimycDXzGE2mkuvjftvHJTtS7h_A0fYGP1wXcA,14505
google/cloud/speech_v1/services/adaptation/transports/__init__.py,sha256=LJGhfINJd8V2b2SFfRy5jijBwD0Onw-VmzVaR5ZhNBk,1344
google/cloud/speech_v1/services/adaptation/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/speech_v1/services/adaptation/transports/base.py,sha256=sRcT1sm8CXdbOpPanQ2TQjBoOulf6KBA54FmvYyMNfE,11643
google/cloud/speech_v1/services/adaptation/transports/grpc.py,sha256=Q53Box6mSrCj75-TdrPHwLdnLzyTh4b6-qtuAMKUYXI,28117
google/cloud/speech_v1/services/adaptation/transports/grpc_asyncio.py,sha256=s7a8jBfI-oRWEyy7DDNF9UgKsp5Zh8jS4DZP5N_o6fA,31572
google/cloud/speech_v1/services/adaptation/transports/rest.py,sha256=02Ez_W0QSWKMeJ-_Ty6ygCaWwAqP1UjheloSb4K-8JM,104569
google/cloud/speech_v1/services/adaptation/transports/rest_base.py,sha256=_w0pXdqKzLefdKNdSqE49XSB-KPY9iMAG3SsfVdbaKo,23236
google/cloud/speech_v1/services/speech/__init__.py,sha256=5GEd48GerNYOl9YFwLLaFGxmQeP1EcQYnHVDmEtr31Q,737
google/cloud/speech_v1/services/speech/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/__pycache__/async_client.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/__pycache__/client.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/async_client.py,sha256=iEC0ksNX4VuDzAaJKv9Aua5BEayg8GBz8FKnBpFrNcI,36337
google/cloud/speech_v1/services/speech/client.py,sha256=LSmjZITXkoYRlNEvMghP8WAFuHbTuqswQKsP0G33mAI,53509
google/cloud/speech_v1/services/speech/transports/__init__.py,sha256=ryIYQZxXYoEeBjhX3nA2ER98UArnqkioiMUzq0X8dlY,1288
google/cloud/speech_v1/services/speech/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/speech_v1/services/speech/transports/base.py,sha256=o7tWIf-YXvGhQxCjM8El2DlTeHmy7t7zy0VXl5BI6WI,9362
google/cloud/speech_v1/services/speech/transports/grpc.py,sha256=MMswI79BhKIKhAiBsgeYkgfW_2AL2z2rprGkHi1ArG0,21134
google/cloud/speech_v1/services/speech/transports/grpc_asyncio.py,sha256=9V7eiTrlWCTHXqzWNL5R4mDtzN55KplK7uE_Wpc7G6c,23877
google/cloud/speech_v1/services/speech/transports/rest.py,sha256=_IzJZlK4w4BAmw9N4s8Yn_eLu28yQRPBIvBj8vZKNd8,40799
google/cloud/speech_v1/services/speech/transports/rest_base.py,sha256=oo-wp5SAWOtu7poM2qSSf4kYhuPdbXG9ZsE-58xAbGs,9396
google/cloud/speech_v1/types/__init__.py,sha256=afFghqGHCklVKAx_wrWVfFs3S6XdPjgSz6Px7NVFzUQ,2653
google/cloud/speech_v1/types/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1/types/__pycache__/cloud_speech.cpython-312.pyc,,
google/cloud/speech_v1/types/__pycache__/cloud_speech_adaptation.cpython-312.pyc,,
google/cloud/speech_v1/types/__pycache__/resource.cpython-312.pyc,,
google/cloud/speech_v1/types/cloud_speech.py,sha256=DDRwKXJC8V2LYf91pLjrbApuVveNxvB_ZJ3BndM8ICI,63294
google/cloud/speech_v1/types/cloud_speech_adaptation.py,sha256=hxLuCkdxTfje9gQRnH0ewJRlMxZFoyInWRnSe60cUGI,14117
google/cloud/speech_v1/types/resource.py,sha256=yzO2lLUgr-4FQo8lnXr51KY8CGbfmIYlIRJtSAO6_7M,10375
google/cloud/speech_v1p1beta1/__init__.py,sha256=3I48nC7NIiMt0DTMrQwsbWF1cj9U5tILsNIGJ86iYUg,3189
google/cloud/speech_v1p1beta1/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/gapic_metadata.json,sha256=_yze4FbBlhR-RkM7nRl6VTSoOJf1lqr4Frq2wkjCGjc,5989
google/cloud/speech_v1p1beta1/gapic_version.py,sha256=zcFN_IeaOSUsAk922mORuJ00D3yffXVDPJPmEhO0y3A,653
google/cloud/speech_v1p1beta1/py.typed,sha256=9XnjN9OQ5mzBvUTS4LvkASo3060SLCUBOLJepHrwVDU,80
google/cloud/speech_v1p1beta1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/speech_v1p1beta1/services/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/__init__.py,sha256=mc6LsNyncWccRfmKtQfcxrCI6W-4T8Kyir1Pto1c0LY,753
google/cloud/speech_v1p1beta1/services/adaptation/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/__pycache__/async_client.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/__pycache__/client.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/__pycache__/pagers.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/async_client.py,sha256=Pnr3ugW0QsV0dzMK-4q4rm6CiRIzd5x4JhUJzGI3C1A,74105
google/cloud/speech_v1p1beta1/services/adaptation/client.py,sha256=E-UuCn0gLLP0p9e__2QDgDvGIo8s6loPnWOq2v3W9Zs,92191
google/cloud/speech_v1p1beta1/services/adaptation/pagers.py,sha256=bfRhVZ0FaWIJGppKnVd-96nI10CbNcAz7N1qim8C9Tg,14624
google/cloud/speech_v1p1beta1/services/adaptation/transports/__init__.py,sha256=LJGhfINJd8V2b2SFfRy5jijBwD0Onw-VmzVaR5ZhNBk,1344
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/base.py,sha256=lfPYUqaYRdloPWheUJPvV2QeXTsXkmSWm3nMOvKlQ40,11657
google/cloud/speech_v1p1beta1/services/adaptation/transports/grpc.py,sha256=34N2UfQx4hMzjmOB1ELULyvhlaYWaeai2nInmvBX7Qs,28208
google/cloud/speech_v1p1beta1/services/adaptation/transports/grpc_asyncio.py,sha256=q9UXfHeqX9AiEK8WyxeZpS4Otaw1WJvIDvdrRahDrf4,31663
google/cloud/speech_v1p1beta1/services/adaptation/transports/rest.py,sha256=H_vi_vHTm2YCrYyITZlrp-vzXKTTDf7w0tsWskIxPSs,104884
google/cloud/speech_v1p1beta1/services/adaptation/transports/rest_base.py,sha256=szemMwAHOR2WL4cC0TaPLEjUbLlDz-QKqh6OaSx6PJM,23327
google/cloud/speech_v1p1beta1/services/speech/__init__.py,sha256=5GEd48GerNYOl9YFwLLaFGxmQeP1EcQYnHVDmEtr31Q,737
google/cloud/speech_v1p1beta1/services/speech/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/__pycache__/async_client.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/__pycache__/client.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/async_client.py,sha256=e7Squj9cSj7aMFnNPAludf30Ilx6uPzm4cjW-voUKJw,36877
google/cloud/speech_v1p1beta1/services/speech/client.py,sha256=6VS5hhVFy7T4ML_lv5nVFsE12B0VERAAMQMQKCBiOUk,55568
google/cloud/speech_v1p1beta1/services/speech/transports/__init__.py,sha256=ryIYQZxXYoEeBjhX3nA2ER98UArnqkioiMUzq0X8dlY,1288
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/base.py,sha256=-0hnGcODTNPEVkYZjKSBMesj4Pz3D4J2PxpoxzbCz0I,9376
google/cloud/speech_v1p1beta1/services/speech/transports/grpc.py,sha256=AgvPyWjd636RDPlEi2X6TUDWYcB4ZW5Z0W1-qKas2tQ,21176
google/cloud/speech_v1p1beta1/services/speech/transports/grpc_asyncio.py,sha256=0BBq5XxLFAui6vBV4UL_qD44o0qXajQU5Gng6jTsaig,23919
google/cloud/speech_v1p1beta1/services/speech/transports/rest.py,sha256=S6RlheZ-Mk9AM8AHGWU05FayJp_IzpoofEyKonhRUFE,40939
google/cloud/speech_v1p1beta1/services/speech/transports/rest_base.py,sha256=z2FG-bqLJRBTFBhKH-cjuzNCdMR4oAKDEjSRE_kTU24,9431
google/cloud/speech_v1p1beta1/types/__init__.py,sha256=afFghqGHCklVKAx_wrWVfFs3S6XdPjgSz6Px7NVFzUQ,2653
google/cloud/speech_v1p1beta1/types/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/types/__pycache__/cloud_speech.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/types/__pycache__/cloud_speech_adaptation.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/types/__pycache__/resource.cpython-312.pyc,,
google/cloud/speech_v1p1beta1/types/cloud_speech.py,sha256=6BBj2ZP9KbjSo-B0FBqhpmyBMTSbdcD9Fa1SZqDFc6E,65365
google/cloud/speech_v1p1beta1/types/cloud_speech_adaptation.py,sha256=xQsp-PeTR0d1GQJzyZQQMSwslyLjtqRBXkb_93PLY-w,14173
google/cloud/speech_v1p1beta1/types/resource.py,sha256=T6ozCpLV0s6JxFYncYNS_BFYhI4NYTGR9r50-ZqJQH0,18251
google/cloud/speech_v2/__init__.py,sha256=hQhc-QviwDsyHyGjkB2Dpmy7a-xJKUCIXVbLIhOJZyw,4754
google/cloud/speech_v2/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v2/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/speech_v2/gapic_metadata.json,sha256=bguz80S758ODnrA5g8PZCoXUjgbYgTzpO39C-sHs4wY,9349
google/cloud/speech_v2/gapic_version.py,sha256=zcFN_IeaOSUsAk922mORuJ00D3yffXVDPJPmEhO0y3A,653
google/cloud/speech_v2/py.typed,sha256=9XnjN9OQ5mzBvUTS4LvkASo3060SLCUBOLJepHrwVDU,80
google/cloud/speech_v2/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/speech_v2/services/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/__init__.py,sha256=5GEd48GerNYOl9YFwLLaFGxmQeP1EcQYnHVDmEtr31Q,737
google/cloud/speech_v2/services/speech/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/__pycache__/async_client.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/__pycache__/client.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/__pycache__/pagers.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/async_client.py,sha256=QsuR4YfZSj5HCH7IskmMKjx2FdVOo-M3eZ_97J8oZ9Q,163954
google/cloud/speech_v2/services/speech/client.py,sha256=3bfMm-5P42F0BIeRb4ZsvMnSrdeCAXYG8JTYnkKRQNs,181622
google/cloud/speech_v2/services/speech/pagers.py,sha256=xFh7p4p89FSNEFi7jbvLln-zoYcd-iSK9cIf08oKA0M,20688
google/cloud/speech_v2/services/speech/transports/__init__.py,sha256=ryIYQZxXYoEeBjhX3nA2ER98UArnqkioiMUzq0X8dlY,1288
google/cloud/speech_v2/services/speech/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/speech_v2/services/speech/transports/base.py,sha256=c_MHAjQ07pFNt2g5Klb4ZvoY_nk_BZcw4yaGpj4u9eE,19193
google/cloud/speech_v2/services/speech/transports/grpc.py,sha256=Ua6jkqsw0CF8jzM4EMAcWO8XbOy5BjpYB0dbPWnsYio,47112
google/cloud/speech_v2/services/speech/transports/grpc_asyncio.py,sha256=o0XgmNLClu-zfUnTRi9K_X2seMM9Dm6T7s5myjhR8KM,54206
google/cloud/speech_v2/services/speech/transports/rest.py,sha256=qHddmNTfpPh0JmZiiNgwMCoMUIQw6RovPct5ey6W2Uw,245958
google/cloud/speech_v2/services/speech/transports/rest_base.py,sha256=rAIvl33qhdPWpvYVTBG3Gz_Tu3-7wyWdLH-BvQ5mLqA,49256
google/cloud/speech_v2/types/__init__.py,sha256=Zz3AzwFEpK-t_JuXrxy6Q8dFPNmGay0ESTYXmtTLz78,4523
google/cloud/speech_v2/types/__pycache__/__init__.cpython-312.pyc,,
google/cloud/speech_v2/types/__pycache__/cloud_speech.cpython-312.pyc,,
google/cloud/speech_v2/types/__pycache__/locations_metadata.cpython-312.pyc,,
google/cloud/speech_v2/types/cloud_speech.py,sha256=GYkxmUA9-ET2WTzT8KEBCVzZUhY9wVjwVl_PocucTCU,123775
google/cloud/speech_v2/types/locations_metadata.py,sha256=rU4a-nskbJHQbRWjrYnjAOwpOzycJ_uWR5W1C16W9tA,4979
google_cloud_speech-2.33.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_speech-2.33.0.dist-info/METADATA,sha256=yOl8axTMIucPP9l6Q50RvS1slJq-qldcB-t7Bj0rCKE,9648
google_cloud_speech-2.33.0.dist-info/RECORD,,
google_cloud_speech-2.33.0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
google_cloud_speech-2.33.0.dist-info/licenses/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_speech-2.33.0.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
