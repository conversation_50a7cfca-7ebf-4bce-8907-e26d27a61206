google_crc32c-1.7.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_crc32c-1.7.1.dist-info/METADATA,sha256=eEiZaSEI3_IYj5sqwdf0yLQs6lsSztKXzFwBuQ7T7LA,2405
google_crc32c-1.7.1.dist-info/RECORD,,
google_crc32c-1.7.1.dist-info/WHEEL,sha256=ovhA9_Ei_7ok2fAych90j-feDV4goiAxbO7REePtvw0,101
google_crc32c-1.7.1.dist-info/licenses/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_crc32c-1.7.1.dist-info/top_level.txt,sha256=r7PLPlKjfhMZLqeRsKXIQdIzbe3Frv_2_b8XmcvZ4FQ,14
google_crc32c-1.7.1.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
google_crc32c/__config__.py,sha256=2i0KpKo-ypmMS_P9i3qObAW2eemCWtrmu_PJXktjHO8,1138
google_crc32c/__init__.py,sha256=Dun6VhGxQ-8fydGR_hX4U9K3n8XPYXCDKmoIBAatpzM,1291
google_crc32c/__pycache__/__config__.cpython-312.pyc,,
google_crc32c/__pycache__/__init__.cpython-312.pyc,,
google_crc32c/__pycache__/_checksum.cpython-312.pyc,,
google_crc32c/__pycache__/cext.cpython-312.pyc,,
google_crc32c/__pycache__/python.cpython-312.pyc,,
google_crc32c/_checksum.py,sha256=r_2PckK1pwWlW9E6e9xys4qdT4Z7JZe2WfYDFzNQbNI,2636
google_crc32c/_crc32c.cp312-win_amd64.pyd,sha256=Jzt7Qj-8d4oPRG8mnc_a0HgB3Lzm0RV-xQEdMsZDCGc,10752
google_crc32c/cext.py,sha256=oF7NFpKWUOEoyYlbVGIAJVo8SNDiWRT_VYyCI14-5jo,1557
google_crc32c/extra-dll/crc32c.dll,sha256=T8QOpnj2VqXMGdCz2gPtY7VUpdejwb2DmPkrm7EMltk,49664
google_crc32c/py.typed,sha256=2rT_wtizx2JwmnzAr6tXdmsEdE5PIp4kum7YsDhOw_w,76
google_crc32c/python.py,sha256=9zy6ABAB_KM8JJRcZnE4b68VqnOEFRNmCGufrjcb7qc,5661
