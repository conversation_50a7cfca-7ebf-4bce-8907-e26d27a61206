# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: livekit_agent_dispatch.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import agent as _agent_


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1clivekit_agent_dispatch.proto\x12\x07livekit\x1a\x13livekit_agent.proto\"P\n\x1a\x43reateAgentDispatchRequest\x12\x12\n\nagent_name\x18\x01 \x01(\t\x12\x0c\n\x04room\x18\x02 \x01(\t\x12\x10\n\x08metadata\x18\x03 \x01(\t\"9\n\x11RoomAgentDispatch\x12\x12\n\nagent_name\x18\x01 \x01(\t\x12\x10\n\x08metadata\x18\x02 \x01(\t\"?\n\x1a\x44\x65leteAgentDispatchRequest\x12\x13\n\x0b\x64ispatch_id\x18\x01 \x01(\t\x12\x0c\n\x04room\x18\x02 \x01(\t\"=\n\x18ListAgentDispatchRequest\x12\x13\n\x0b\x64ispatch_id\x18\x01 \x01(\t\x12\x0c\n\x04room\x18\x02 \x01(\t\"M\n\x19ListAgentDispatchResponse\x12\x30\n\x10\x61gent_dispatches\x18\x01 \x03(\x0b\x32\x16.livekit.AgentDispatch\"{\n\rAgentDispatch\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\nagent_name\x18\x02 \x01(\t\x12\x0c\n\x04room\x18\x03 \x01(\t\x12\x10\n\x08metadata\x18\x04 \x01(\t\x12*\n\x05state\x18\x05 \x01(\x0b\x32\x1b.livekit.AgentDispatchState\"X\n\x12\x41gentDispatchState\x12\x1a\n\x04jobs\x18\x01 \x03(\x0b\x32\x0c.livekit.Job\x12\x12\n\ncreated_at\x18\x02 \x01(\x03\x12\x12\n\ndeleted_at\x18\x03 \x01(\x03\x32\x8b\x02\n\x14\x41gentDispatchService\x12M\n\x0e\x43reateDispatch\x12#.livekit.CreateAgentDispatchRequest\x1a\x16.livekit.AgentDispatch\x12M\n\x0e\x44\x65leteDispatch\x12#.livekit.DeleteAgentDispatchRequest\x1a\x16.livekit.AgentDispatch\x12U\n\x0cListDispatch\x12!.livekit.ListAgentDispatchRequest\x1a\".livekit.ListAgentDispatchResponseBFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'agent_dispatch', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z#github.com/livekit/protocol/livekit\252\002\rLiveKit.Proto\352\002\016LiveKit::Proto'
  _globals['_CREATEAGENTDISPATCHREQUEST']._serialized_start=62
  _globals['_CREATEAGENTDISPATCHREQUEST']._serialized_end=142
  _globals['_ROOMAGENTDISPATCH']._serialized_start=144
  _globals['_ROOMAGENTDISPATCH']._serialized_end=201
  _globals['_DELETEAGENTDISPATCHREQUEST']._serialized_start=203
  _globals['_DELETEAGENTDISPATCHREQUEST']._serialized_end=266
  _globals['_LISTAGENTDISPATCHREQUEST']._serialized_start=268
  _globals['_LISTAGENTDISPATCHREQUEST']._serialized_end=329
  _globals['_LISTAGENTDISPATCHRESPONSE']._serialized_start=331
  _globals['_LISTAGENTDISPATCHRESPONSE']._serialized_end=408
  _globals['_AGENTDISPATCH']._serialized_start=410
  _globals['_AGENTDISPATCH']._serialized_end=533
  _globals['_AGENTDISPATCHSTATE']._serialized_start=535
  _globals['_AGENTDISPATCHSTATE']._serialized_end=623
  _globals['_AGENTDISPATCHSERVICE']._serialized_start=626
  _globals['_AGENTDISPATCHSERVICE']._serialized_end=893
# @@protoc_insertion_point(module_scope)
