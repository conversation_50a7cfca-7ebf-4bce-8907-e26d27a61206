# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: livekit_sip.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from . import models as _models_
from . import room as _room_


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11livekit_sip.proto\x12\x07livekit\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x14livekit_models.proto\x1a\x12livekit_room.proto\"A\n\tSIPStatus\x12$\n\x04\x63ode\x18\x01 \x01(\x0e\x32\x16.livekit.SIPStatusCode\x12\x0e\n\x06status\x18\x02 \x01(\t\"\xaf\x02\n\x15\x43reateSIPTrunkRequest\x12\x19\n\x11inbound_addresses\x18\x01 \x03(\t\x12\x18\n\x10outbound_address\x18\x02 \x01(\t\x12\x17\n\x0foutbound_number\x18\x03 \x01(\t\x12!\n\x15inbound_numbers_regex\x18\x04 \x03(\tB\x02\x18\x01\x12\x17\n\x0finbound_numbers\x18\t \x03(\t\x12\x18\n\x10inbound_username\x18\x05 \x01(\t\x12\x18\n\x10inbound_password\x18\x06 \x01(\t\x12\x19\n\x11outbound_username\x18\x07 \x01(\t\x12\x19\n\x11outbound_password\x18\x08 \x01(\t\x12\x0c\n\x04name\x18\n \x01(\t\x12\x10\n\x08metadata\x18\x0b \x01(\t:\x02\x18\x01\"\xdb\x03\n\x0cSIPTrunkInfo\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\x12-\n\x04kind\x18\x0e \x01(\x0e\x32\x1f.livekit.SIPTrunkInfo.TrunkKind\x12\x19\n\x11inbound_addresses\x18\x02 \x03(\t\x12\x18\n\x10outbound_address\x18\x03 \x01(\t\x12\x17\n\x0foutbound_number\x18\x04 \x01(\t\x12(\n\ttransport\x18\r \x01(\x0e\x32\x15.livekit.SIPTransport\x12!\n\x15inbound_numbers_regex\x18\x05 \x03(\tB\x02\x18\x01\x12\x17\n\x0finbound_numbers\x18\n \x03(\t\x12\x18\n\x10inbound_username\x18\x06 \x01(\t\x12\x18\n\x10inbound_password\x18\x07 \x01(\t\x12\x19\n\x11outbound_username\x18\x08 \x01(\t\x12\x19\n\x11outbound_password\x18\t \x01(\t\x12\x0c\n\x04name\x18\x0b \x01(\t\x12\x10\n\x08metadata\x18\x0c \x01(\t\"D\n\tTrunkKind\x12\x10\n\x0cTRUNK_LEGACY\x10\x00\x12\x11\n\rTRUNK_INBOUND\x10\x01\x12\x12\n\x0eTRUNK_OUTBOUND\x10\x02:\x02\x18\x01\"K\n\x1c\x43reateSIPInboundTrunkRequest\x12+\n\x05trunk\x18\x01 \x01(\x0b\x32\x1c.livekit.SIPInboundTrunkInfo\"\xa1\x01\n\x1cUpdateSIPInboundTrunkRequest\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\x12/\n\x07replace\x18\x02 \x01(\x0b\x32\x1c.livekit.SIPInboundTrunkInfoH\x00\x12\x30\n\x06update\x18\x03 \x01(\x0b\x32\x1e.livekit.SIPInboundTrunkUpdateH\x00\x42\x08\n\x06\x61\x63tion\"\xba\x06\n\x13SIPInboundTrunkInfo\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08metadata\x18\x03 \x01(\t\x12\x0f\n\x07numbers\x18\x04 \x03(\t\x12\x19\n\x11\x61llowed_addresses\x18\x05 \x03(\t\x12\x17\n\x0f\x61llowed_numbers\x18\x06 \x03(\t\x12\x15\n\rauth_username\x18\x07 \x01(\t\x12\x15\n\rauth_password\x18\x08 \x01(\t\x12:\n\x07headers\x18\t \x03(\x0b\x32).livekit.SIPInboundTrunkInfo.HeadersEntry\x12T\n\x15headers_to_attributes\x18\n \x03(\x0b\x32\x35.livekit.SIPInboundTrunkInfo.HeadersToAttributesEntry\x12T\n\x15\x61ttributes_to_headers\x18\x0e \x03(\x0b\x32\x35.livekit.SIPInboundTrunkInfo.AttributesToHeadersEntry\x12\x32\n\x0finclude_headers\x18\x0f \x01(\x0e\x32\x19.livekit.SIPHeaderOptions\x12\x32\n\x0fringing_timeout\x18\x0b \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x34\n\x11max_call_duration\x18\x0c \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x15\n\rkrisp_enabled\x18\r \x01(\x08\x12\x35\n\x10media_encryption\x18\x10 \x01(\x0e\x32\x1b.livekit.SIPMediaEncryption\x1a.\n\x0cHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a:\n\x18HeadersToAttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a:\n\x18\x41ttributesToHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x88\x03\n\x15SIPInboundTrunkUpdate\x12$\n\x07numbers\x18\x01 \x01(\x0b\x32\x13.livekit.ListUpdate\x12.\n\x11\x61llowed_addresses\x18\x02 \x01(\x0b\x32\x13.livekit.ListUpdate\x12,\n\x0f\x61llowed_numbers\x18\x03 \x01(\x0b\x32\x13.livekit.ListUpdate\x12\x1a\n\rauth_username\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x1a\n\rauth_password\x18\x05 \x01(\tH\x01\x88\x01\x01\x12\x11\n\x04name\x18\x06 \x01(\tH\x02\x88\x01\x01\x12\x15\n\x08metadata\x18\x07 \x01(\tH\x03\x88\x01\x01\x12:\n\x10media_encryption\x18\x08 \x01(\x0e\x32\x1b.livekit.SIPMediaEncryptionH\x04\x88\x01\x01\x42\x10\n\x0e_auth_usernameB\x10\n\x0e_auth_passwordB\x07\n\x05_nameB\x0b\n\t_metadataB\x13\n\x11_media_encryption\"M\n\x1d\x43reateSIPOutboundTrunkRequest\x12,\n\x05trunk\x18\x01 \x01(\x0b\x32\x1d.livekit.SIPOutboundTrunkInfo\"\xa4\x01\n\x1dUpdateSIPOutboundTrunkRequest\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\x12\x30\n\x07replace\x18\x02 \x01(\x0b\x32\x1d.livekit.SIPOutboundTrunkInfoH\x00\x12\x31\n\x06update\x18\x03 \x01(\x0b\x32\x1f.livekit.SIPOutboundTrunkUpdateH\x00\x42\x08\n\x06\x61\x63tion\"\xe1\x05\n\x14SIPOutboundTrunkInfo\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08metadata\x18\x03 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x04 \x01(\t\x12\x1b\n\x13\x64\x65stination_country\x18\x0e \x01(\t\x12(\n\ttransport\x18\x05 \x01(\x0e\x32\x15.livekit.SIPTransport\x12\x0f\n\x07numbers\x18\x06 \x03(\t\x12\x15\n\rauth_username\x18\x07 \x01(\t\x12\x15\n\rauth_password\x18\x08 \x01(\t\x12;\n\x07headers\x18\t \x03(\x0b\x32*.livekit.SIPOutboundTrunkInfo.HeadersEntry\x12U\n\x15headers_to_attributes\x18\n \x03(\x0b\x32\x36.livekit.SIPOutboundTrunkInfo.HeadersToAttributesEntry\x12U\n\x15\x61ttributes_to_headers\x18\x0b \x03(\x0b\x32\x36.livekit.SIPOutboundTrunkInfo.AttributesToHeadersEntry\x12\x32\n\x0finclude_headers\x18\x0c \x01(\x0e\x32\x19.livekit.SIPHeaderOptions\x12\x35\n\x10media_encryption\x18\r \x01(\x0e\x32\x1b.livekit.SIPMediaEncryption\x1a.\n\x0cHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a:\n\x18HeadersToAttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a:\n\x18\x41ttributesToHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xc4\x03\n\x16SIPOutboundTrunkUpdate\x12\x14\n\x07\x61\x64\x64ress\x18\x01 \x01(\tH\x00\x88\x01\x01\x12-\n\ttransport\x18\x02 \x01(\x0e\x32\x15.livekit.SIPTransportH\x01\x88\x01\x01\x12 \n\x13\x64\x65stination_country\x18\t \x01(\tH\x02\x88\x01\x01\x12$\n\x07numbers\x18\x03 \x01(\x0b\x32\x13.livekit.ListUpdate\x12\x1a\n\rauth_username\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x1a\n\rauth_password\x18\x05 \x01(\tH\x04\x88\x01\x01\x12\x11\n\x04name\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x15\n\x08metadata\x18\x07 \x01(\tH\x06\x88\x01\x01\x12:\n\x10media_encryption\x18\x08 \x01(\x0e\x32\x1b.livekit.SIPMediaEncryptionH\x07\x88\x01\x01\x42\n\n\x08_addressB\x0c\n\n_transportB\x16\n\x14_destination_countryB\x10\n\x0e_auth_usernameB\x10\n\x0e_auth_passwordB\x07\n\x05_nameB\x0b\n\t_metadataB\x13\n\x11_media_encryption\"1\n\x19GetSIPInboundTrunkRequest\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\"I\n\x1aGetSIPInboundTrunkResponse\x12+\n\x05trunk\x18\x01 \x01(\x0b\x32\x1c.livekit.SIPInboundTrunkInfo\"2\n\x1aGetSIPOutboundTrunkRequest\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\"K\n\x1bGetSIPOutboundTrunkResponse\x12,\n\x05trunk\x18\x01 \x01(\x0b\x32\x1d.livekit.SIPOutboundTrunkInfo\"<\n\x13ListSIPTrunkRequest\x12!\n\x04page\x18\x01 \x01(\x0b\x32\x13.livekit.Pagination:\x02\x18\x01\"@\n\x14ListSIPTrunkResponse\x12$\n\x05items\x18\x01 \x03(\x0b\x32\x15.livekit.SIPTrunkInfo:\x02\x18\x01\"c\n\x1aListSIPInboundTrunkRequest\x12!\n\x04page\x18\x03 \x01(\x0b\x32\x13.livekit.Pagination\x12\x11\n\ttrunk_ids\x18\x01 \x03(\t\x12\x0f\n\x07numbers\x18\x02 \x03(\t\"J\n\x1bListSIPInboundTrunkResponse\x12+\n\x05items\x18\x01 \x03(\x0b\x32\x1c.livekit.SIPInboundTrunkInfo\"d\n\x1bListSIPOutboundTrunkRequest\x12!\n\x04page\x18\x03 \x01(\x0b\x32\x13.livekit.Pagination\x12\x11\n\ttrunk_ids\x18\x01 \x03(\t\x12\x0f\n\x07numbers\x18\x02 \x03(\t\"L\n\x1cListSIPOutboundTrunkResponse\x12,\n\x05items\x18\x01 \x03(\x0b\x32\x1d.livekit.SIPOutboundTrunkInfo\"-\n\x15\x44\x65leteSIPTrunkRequest\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\"7\n\x15SIPDispatchRuleDirect\x12\x11\n\troom_name\x18\x01 \x01(\t\x12\x0b\n\x03pin\x18\x02 \x01(\t\"=\n\x19SIPDispatchRuleIndividual\x12\x13\n\x0broom_prefix\x18\x01 \x01(\t\x12\x0b\n\x03pin\x18\x02 \x01(\t\"L\n\x15SIPDispatchRuleCallee\x12\x13\n\x0broom_prefix\x18\x01 \x01(\t\x12\x0b\n\x03pin\x18\x02 \x01(\t\x12\x11\n\trandomize\x18\x03 \x01(\x08\"\xe1\x01\n\x0fSIPDispatchRule\x12>\n\x14\x64ispatch_rule_direct\x18\x01 \x01(\x0b\x32\x1e.livekit.SIPDispatchRuleDirectH\x00\x12\x46\n\x18\x64ispatch_rule_individual\x18\x02 \x01(\x0b\x32\".livekit.SIPDispatchRuleIndividualH\x00\x12>\n\x14\x64ispatch_rule_callee\x18\x03 \x01(\x0b\x32\x1e.livekit.SIPDispatchRuleCalleeH\x00\x42\x06\n\x04rule\"\xca\x03\n\x1c\x43reateSIPDispatchRuleRequest\x12\x33\n\rdispatch_rule\x18\n \x01(\x0b\x32\x1c.livekit.SIPDispatchRuleInfo\x12*\n\x04rule\x18\x01 \x01(\x0b\x32\x18.livekit.SIPDispatchRuleB\x02\x18\x01\x12\x15\n\ttrunk_ids\x18\x02 \x03(\tB\x02\x18\x01\x12\x1d\n\x11hide_phone_number\x18\x03 \x01(\x08\x42\x02\x18\x01\x12\x1b\n\x0finbound_numbers\x18\x06 \x03(\tB\x02\x18\x01\x12\x10\n\x04name\x18\x04 \x01(\tB\x02\x18\x01\x12\x14\n\x08metadata\x18\x05 \x01(\tB\x02\x18\x01\x12M\n\nattributes\x18\x07 \x03(\x0b\x32\x35.livekit.CreateSIPDispatchRuleRequest.AttributesEntryB\x02\x18\x01\x12\x17\n\x0broom_preset\x18\x08 \x01(\tB\x02\x18\x01\x12\x33\n\x0broom_config\x18\t \x01(\x0b\x32\x1a.livekit.RoomConfigurationB\x02\x18\x01\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xa9\x01\n\x1cUpdateSIPDispatchRuleRequest\x12\x1c\n\x14sip_dispatch_rule_id\x18\x01 \x01(\t\x12/\n\x07replace\x18\x02 \x01(\x0b\x32\x1c.livekit.SIPDispatchRuleInfoH\x00\x12\x30\n\x06update\x18\x03 \x01(\x0b\x32\x1e.livekit.SIPDispatchRuleUpdateH\x00\x42\x08\n\x06\x61\x63tion\"\xcb\x03\n\x13SIPDispatchRuleInfo\x12\x1c\n\x14sip_dispatch_rule_id\x18\x01 \x01(\t\x12&\n\x04rule\x18\x02 \x01(\x0b\x32\x18.livekit.SIPDispatchRule\x12\x11\n\ttrunk_ids\x18\x03 \x03(\t\x12\x19\n\x11hide_phone_number\x18\x04 \x01(\x08\x12\x17\n\x0finbound_numbers\x18\x07 \x03(\t\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x10\n\x08metadata\x18\x06 \x01(\t\x12@\n\nattributes\x18\x08 \x03(\x0b\x32,.livekit.SIPDispatchRuleInfo.AttributesEntry\x12\x13\n\x0broom_preset\x18\t \x01(\t\x12/\n\x0broom_config\x18\n \x01(\x0b\x32\x1a.livekit.RoomConfiguration\x12\x15\n\rkrisp_enabled\x18\x0b \x01(\x08\x12\x35\n\x10media_encryption\x18\x0c \x01(\x0e\x32\x1b.livekit.SIPMediaEncryption\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xef\x02\n\x15SIPDispatchRuleUpdate\x12&\n\ttrunk_ids\x18\x01 \x01(\x0b\x32\x13.livekit.ListUpdate\x12&\n\x04rule\x18\x02 \x01(\x0b\x32\x18.livekit.SIPDispatchRule\x12\x11\n\x04name\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x15\n\x08metadata\x18\x04 \x01(\tH\x01\x88\x01\x01\x12\x42\n\nattributes\x18\x05 \x03(\x0b\x32..livekit.SIPDispatchRuleUpdate.AttributesEntry\x12:\n\x10media_encryption\x18\x06 \x01(\x0e\x32\x1b.livekit.SIPMediaEncryptionH\x02\x88\x01\x01\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x07\n\x05_nameB\x0b\n\t_metadataB\x13\n\x11_media_encryption\"m\n\x1aListSIPDispatchRuleRequest\x12!\n\x04page\x18\x03 \x01(\x0b\x32\x13.livekit.Pagination\x12\x19\n\x11\x64ispatch_rule_ids\x18\x01 \x03(\t\x12\x11\n\ttrunk_ids\x18\x02 \x03(\t\"J\n\x1bListSIPDispatchRuleResponse\x12+\n\x05items\x18\x01 \x03(\x0b\x32\x1c.livekit.SIPDispatchRuleInfo\"<\n\x1c\x44\x65leteSIPDispatchRuleRequest\x12\x1c\n\x14sip_dispatch_rule_id\x18\x01 \x01(\t\"\xba\x03\n\x11SIPOutboundConfig\x12\x10\n\x08hostname\x18\x01 \x01(\t\x12\x1b\n\x13\x64\x65stination_country\x18\x07 \x01(\t\x12(\n\ttransport\x18\x02 \x01(\x0e\x32\x15.livekit.SIPTransport\x12\x15\n\rauth_username\x18\x03 \x01(\t\x12\x15\n\rauth_password\x18\x04 \x01(\t\x12R\n\x15headers_to_attributes\x18\x05 \x03(\x0b\x32\x33.livekit.SIPOutboundConfig.HeadersToAttributesEntry\x12R\n\x15\x61ttributes_to_headers\x18\x06 \x03(\x0b\x32\x33.livekit.SIPOutboundConfig.AttributesToHeadersEntry\x1a:\n\x18HeadersToAttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a:\n\x18\x41ttributesToHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xe7\x06\n\x1b\x43reateSIPParticipantRequest\x12\x14\n\x0csip_trunk_id\x18\x01 \x01(\t\x12)\n\x05trunk\x18\x14 \x01(\x0b\x32\x1a.livekit.SIPOutboundConfig\x12\x13\n\x0bsip_call_to\x18\x02 \x01(\t\x12\x12\n\nsip_number\x18\x0f \x01(\t\x12\x11\n\troom_name\x18\x03 \x01(\t\x12\x1c\n\x14participant_identity\x18\x04 \x01(\t\x12\x18\n\x10participant_name\x18\x07 \x01(\t\x12\x1c\n\x14participant_metadata\x18\x08 \x01(\t\x12_\n\x16participant_attributes\x18\t \x03(\x0b\x32?.livekit.CreateSIPParticipantRequest.ParticipantAttributesEntry\x12\x0c\n\x04\x64tmf\x18\x05 \x01(\t\x12\x19\n\rplay_ringtone\x18\x06 \x01(\x08\x42\x02\x18\x01\x12\x15\n\rplay_dialtone\x18\r \x01(\x08\x12\x19\n\x11hide_phone_number\x18\n \x01(\x08\x12\x42\n\x07headers\x18\x10 \x03(\x0b\x32\x31.livekit.CreateSIPParticipantRequest.HeadersEntry\x12\x32\n\x0finclude_headers\x18\x11 \x01(\x0e\x32\x19.livekit.SIPHeaderOptions\x12\x32\n\x0fringing_timeout\x18\x0b \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x34\n\x11max_call_duration\x18\x0c \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x15\n\rkrisp_enabled\x18\x0e \x01(\x08\x12\x35\n\x10media_encryption\x18\x12 \x01(\x0e\x32\x1b.livekit.SIPMediaEncryption\x12\x1b\n\x13wait_until_answered\x18\x13 \x01(\x08\x1a<\n\x1aParticipantAttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a.\n\x0cHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"r\n\x12SIPParticipantInfo\x12\x16\n\x0eparticipant_id\x18\x01 \x01(\t\x12\x1c\n\x14participant_identity\x18\x02 \x01(\t\x12\x11\n\troom_name\x18\x03 \x01(\t\x12\x13\n\x0bsip_call_id\x18\x04 \x01(\t\"\xa6\x02\n\x1dTransferSIPParticipantRequest\x12\x1c\n\x14participant_identity\x18\x01 \x01(\t\x12\x11\n\troom_name\x18\x02 \x01(\t\x12\x13\n\x0btransfer_to\x18\x03 \x01(\t\x12\x15\n\rplay_dialtone\x18\x04 \x01(\x08\x12\x44\n\x07headers\x18\x05 \x03(\x0b\x32\x33.livekit.TransferSIPParticipantRequest.HeadersEntry\x12\x32\n\x0fringing_timeout\x18\x06 \x01(\x0b\x32\x19.google.protobuf.Duration\x1a.\n\x0cHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xa9\x06\n\x0bSIPCallInfo\x12\x0f\n\x07\x63\x61ll_id\x18\x01 \x01(\t\x12\x10\n\x08trunk_id\x18\x02 \x01(\t\x12\x18\n\x10\x64ispatch_rule_id\x18\x10 \x01(\t\x12\x0e\n\x06region\x18\x11 \x01(\t\x12\x11\n\troom_name\x18\x03 \x01(\t\x12\x0f\n\x07room_id\x18\x04 \x01(\t\x12\x1c\n\x14participant_identity\x18\x05 \x01(\t\x12O\n\x16participant_attributes\x18\x12 \x03(\x0b\x32/.livekit.SIPCallInfo.ParticipantAttributesEntry\x12!\n\x08\x66rom_uri\x18\x06 \x01(\x0b\x32\x0f.livekit.SIPUri\x12\x1f\n\x06to_uri\x18\x07 \x01(\x0b\x32\x0f.livekit.SIPUri\x12\x16\n\ncreated_at\x18\t \x01(\x03\x42\x02\x18\x01\x12\x16\n\nstarted_at\x18\n \x01(\x03\x42\x02\x18\x01\x12\x14\n\x08\x65nded_at\x18\x0b \x01(\x03\x42\x02\x18\x01\x12-\n\x10\x65nabled_features\x18\x0e \x03(\x0e\x32\x13.livekit.SIPFeature\x12\x31\n\x0e\x63\x61ll_direction\x18\x0f \x01(\x0e\x32\x19.livekit.SIPCallDirection\x12+\n\x0b\x63\x61ll_status\x18\x08 \x01(\x0e\x32\x16.livekit.SIPCallStatus\x12\x15\n\rcreated_at_ns\x18\x16 \x01(\x03\x12\x15\n\rstarted_at_ns\x18\x17 \x01(\x03\x12\x13\n\x0b\x65nded_at_ns\x18\x18 \x01(\x03\x12\x34\n\x11\x64isconnect_reason\x18\x0c \x01(\x0e\x32\x19.livekit.DisconnectReason\x12\r\n\x05\x65rror\x18\r \x01(\t\x12,\n\x10\x63\x61ll_status_code\x18\x13 \x01(\x0b\x32\x12.livekit.SIPStatus\x12\x13\n\x0b\x61udio_codec\x18\x14 \x01(\t\x12\x18\n\x10media_encryption\x18\x15 \x01(\t\x1a<\n\x1aParticipantAttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x86\x02\n\x0fSIPTransferInfo\x12\x13\n\x0btransfer_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63\x61ll_id\x18\x02 \x01(\t\x12\x13\n\x0btransfer_to\x18\x03 \x01(\t\x12 \n\x18transfer_initiated_at_ns\x18\x04 \x01(\x03\x12 \n\x18transfer_completed_at_ns\x18\x05 \x01(\x03\x12\x33\n\x0ftransfer_status\x18\x06 \x01(\x0e\x32\x1a.livekit.SIPTransferStatus\x12\r\n\x05\x65rror\x18\x07 \x01(\t\x12\x30\n\x14transfer_status_code\x18\x08 \x01(\x0b\x32\x12.livekit.SIPStatus\"h\n\x06SIPUri\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x0c\n\x04host\x18\x02 \x01(\t\x12\n\n\x02ip\x18\x03 \x01(\t\x12\x0c\n\x04port\x18\x04 \x01(\r\x12(\n\ttransport\x18\x05 \x01(\x0e\x32\x15.livekit.SIPTransport*\xef\x0c\n\rSIPStatusCode\x12\x16\n\x12SIP_STATUS_UNKNOWN\x10\x00\x12\x15\n\x11SIP_STATUS_TRYING\x10\x64\x12\x17\n\x12SIP_STATUS_RINGING\x10\xb4\x01\x12!\n\x1cSIP_STATUS_CALL_IS_FORWARDED\x10\xb5\x01\x12\x16\n\x11SIP_STATUS_QUEUED\x10\xb6\x01\x12 \n\x1bSIP_STATUS_SESSION_PROGRESS\x10\xb7\x01\x12\x12\n\rSIP_STATUS_OK\x10\xc8\x01\x12\x18\n\x13SIP_STATUS_ACCEPTED\x10\xca\x01\x12!\n\x1cSIP_STATUS_MOVED_PERMANENTLY\x10\xad\x02\x12!\n\x1cSIP_STATUS_MOVED_TEMPORARILY\x10\xae\x02\x12\x19\n\x14SIP_STATUS_USE_PROXY\x10\xb1\x02\x12\x1b\n\x16SIP_STATUS_BAD_REQUEST\x10\x90\x03\x12\x1c\n\x17SIP_STATUS_UNAUTHORIZED\x10\x91\x03\x12 \n\x1bSIP_STATUS_PAYMENT_REQUIRED\x10\x92\x03\x12\x19\n\x14SIP_STATUS_FORBIDDEN\x10\x93\x03\x12\x18\n\x13SIP_STATUS_NOTFOUND\x10\x94\x03\x12\"\n\x1dSIP_STATUS_METHOD_NOT_ALLOWED\x10\x95\x03\x12\x1e\n\x19SIP_STATUS_NOT_ACCEPTABLE\x10\x96\x03\x12#\n\x1eSIP_STATUS_PROXY_AUTH_REQUIRED\x10\x97\x03\x12\x1f\n\x1aSIP_STATUS_REQUEST_TIMEOUT\x10\x98\x03\x12\x18\n\x13SIP_STATUS_CONFLICT\x10\x99\x03\x12\x14\n\x0fSIP_STATUS_GONE\x10\x9a\x03\x12(\n#SIP_STATUS_REQUEST_ENTITY_TOO_LARGE\x10\x9d\x03\x12$\n\x1fSIP_STATUS_REQUEST_URI_TOO_LONG\x10\x9e\x03\x12&\n!SIP_STATUS_UNSUPPORTED_MEDIA_TYPE\x10\x9f\x03\x12/\n*SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE\x10\xa0\x03\x12\x1d\n\x18SIP_STATUS_BAD_EXTENSION\x10\xa4\x03\x12\"\n\x1dSIP_STATUS_EXTENSION_REQUIRED\x10\xa5\x03\x12\"\n\x1dSIP_STATUS_INTERVAL_TOO_BRIEF\x10\xa7\x03\x12\'\n\"SIP_STATUS_TEMPORARILY_UNAVAILABLE\x10\xe0\x03\x12\x30\n+SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS\x10\xe1\x03\x12\x1d\n\x18SIP_STATUS_LOOP_DETECTED\x10\xe2\x03\x12\x1d\n\x18SIP_STATUS_TOO_MANY_HOPS\x10\xe3\x03\x12\"\n\x1dSIP_STATUS_ADDRESS_INCOMPLETE\x10\xe4\x03\x12\x19\n\x14SIP_STATUS_AMBIGUOUS\x10\xe5\x03\x12\x19\n\x14SIP_STATUS_BUSY_HERE\x10\xe6\x03\x12\"\n\x1dSIP_STATUS_REQUEST_TERMINATED\x10\xe7\x03\x12#\n\x1eSIP_STATUS_NOT_ACCEPTABLE_HERE\x10\xe8\x03\x12%\n SIP_STATUS_INTERNAL_SERVER_ERROR\x10\xf4\x03\x12\x1f\n\x1aSIP_STATUS_NOT_IMPLEMENTED\x10\xf5\x03\x12\x1b\n\x16SIP_STATUS_BAD_GATEWAY\x10\xf6\x03\x12#\n\x1eSIP_STATUS_SERVICE_UNAVAILABLE\x10\xf7\x03\x12\x1f\n\x1aSIP_STATUS_GATEWAY_TIMEOUT\x10\xf8\x03\x12%\n SIP_STATUS_VERSION_NOT_SUPPORTED\x10\xf9\x03\x12!\n\x1cSIP_STATUS_MESSAGE_TOO_LARGE\x10\x81\x04\x12&\n!SIP_STATUS_GLOBAL_BUSY_EVERYWHERE\x10\xd8\x04\x12\x1e\n\x19SIP_STATUS_GLOBAL_DECLINE\x10\xdb\x04\x12.\n)SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE\x10\xdc\x04\x12%\n SIP_STATUS_GLOBAL_NOT_ACCEPTABLE\x10\xde\x04*k\n\x0cSIPTransport\x12\x16\n\x12SIP_TRANSPORT_AUTO\x10\x00\x12\x15\n\x11SIP_TRANSPORT_UDP\x10\x01\x12\x15\n\x11SIP_TRANSPORT_TCP\x10\x02\x12\x15\n\x11SIP_TRANSPORT_TLS\x10\x03*N\n\x10SIPHeaderOptions\x12\x12\n\x0eSIP_NO_HEADERS\x10\x00\x12\x11\n\rSIP_X_HEADERS\x10\x01\x12\x13\n\x0fSIP_ALL_HEADERS\x10\x02*o\n\x12SIPMediaEncryption\x12\x1d\n\x19SIP_MEDIA_ENCRYPT_DISABLE\x10\x00\x12\x1b\n\x17SIP_MEDIA_ENCRYPT_ALLOW\x10\x01\x12\x1d\n\x19SIP_MEDIA_ENCRYPT_REQUIRE\x10\x02*w\n\rSIPCallStatus\x12\x15\n\x11SCS_CALL_INCOMING\x10\x00\x12\x1a\n\x16SCS_PARTICIPANT_JOINED\x10\x01\x12\x0e\n\nSCS_ACTIVE\x10\x02\x12\x14\n\x10SCS_DISCONNECTED\x10\x03\x12\r\n\tSCS_ERROR\x10\x04*c\n\x11SIPTransferStatus\x12\x18\n\x14STS_TRANSFER_ONGOING\x10\x00\x12\x17\n\x13STS_TRANSFER_FAILED\x10\x01\x12\x1b\n\x17STS_TRANSFER_SUCCESSFUL\x10\x02*)\n\nSIPFeature\x12\x08\n\x04NONE\x10\x00\x12\x11\n\rKRISP_ENABLED\x10\x01*F\n\x10SIPCallDirection\x12\x0f\n\x0bSCD_UNKNOWN\x10\x00\x12\x0f\n\x0bSCD_INBOUND\x10\x01\x12\x10\n\x0cSCD_OUTBOUND\x10\x02\x32\xd7\x0b\n\x03SIP\x12P\n\x0cListSIPTrunk\x12\x1c.livekit.ListSIPTrunkRequest\x1a\x1d.livekit.ListSIPTrunkResponse\"\x03\x88\x02\x01\x12\\\n\x15\x43reateSIPInboundTrunk\x12%.livekit.CreateSIPInboundTrunkRequest\x1a\x1c.livekit.SIPInboundTrunkInfo\x12_\n\x16\x43reateSIPOutboundTrunk\x12&.livekit.CreateSIPOutboundTrunkRequest\x1a\x1d.livekit.SIPOutboundTrunkInfo\x12\\\n\x15UpdateSIPInboundTrunk\x12%.livekit.UpdateSIPInboundTrunkRequest\x1a\x1c.livekit.SIPInboundTrunkInfo\x12_\n\x16UpdateSIPOutboundTrunk\x12&.livekit.UpdateSIPOutboundTrunkRequest\x1a\x1d.livekit.SIPOutboundTrunkInfo\x12]\n\x12GetSIPInboundTrunk\x12\".livekit.GetSIPInboundTrunkRequest\x1a#.livekit.GetSIPInboundTrunkResponse\x12`\n\x13GetSIPOutboundTrunk\x12#.livekit.GetSIPOutboundTrunkRequest\x1a$.livekit.GetSIPOutboundTrunkResponse\x12`\n\x13ListSIPInboundTrunk\x12#.livekit.ListSIPInboundTrunkRequest\x1a$.livekit.ListSIPInboundTrunkResponse\x12\x63\n\x14ListSIPOutboundTrunk\x12$.livekit.ListSIPOutboundTrunkRequest\x1a%.livekit.ListSIPOutboundTrunkResponse\x12G\n\x0e\x44\x65leteSIPTrunk\x12\x1e.livekit.DeleteSIPTrunkRequest\x1a\x15.livekit.SIPTrunkInfo\x12\\\n\x15\x43reateSIPDispatchRule\x12%.livekit.CreateSIPDispatchRuleRequest\x1a\x1c.livekit.SIPDispatchRuleInfo\x12\\\n\x15UpdateSIPDispatchRule\x12%.livekit.UpdateSIPDispatchRuleRequest\x1a\x1c.livekit.SIPDispatchRuleInfo\x12`\n\x13ListSIPDispatchRule\x12#.livekit.ListSIPDispatchRuleRequest\x1a$.livekit.ListSIPDispatchRuleResponse\x12\\\n\x15\x44\x65leteSIPDispatchRule\x12%.livekit.DeleteSIPDispatchRuleRequest\x1a\x1c.livekit.SIPDispatchRuleInfo\x12Y\n\x14\x43reateSIPParticipant\x12$.livekit.CreateSIPParticipantRequest\x1a\x1b.livekit.SIPParticipantInfo\x12X\n\x16TransferSIPParticipant\x12&.livekit.TransferSIPParticipantRequest\x1a\x16.google.protobuf.EmptyBFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sip', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z#github.com/livekit/protocol/livekit\252\002\rLiveKit.Proto\352\002\016LiveKit::Proto'
  _globals['_CREATESIPTRUNKREQUEST'].fields_by_name['inbound_numbers_regex']._options = None
  _globals['_CREATESIPTRUNKREQUEST'].fields_by_name['inbound_numbers_regex']._serialized_options = b'\030\001'
  _globals['_CREATESIPTRUNKREQUEST']._options = None
  _globals['_CREATESIPTRUNKREQUEST']._serialized_options = b'\030\001'
  _globals['_SIPTRUNKINFO'].fields_by_name['inbound_numbers_regex']._options = None
  _globals['_SIPTRUNKINFO'].fields_by_name['inbound_numbers_regex']._serialized_options = b'\030\001'
  _globals['_SIPTRUNKINFO']._options = None
  _globals['_SIPTRUNKINFO']._serialized_options = b'\030\001'
  _globals['_SIPINBOUNDTRUNKINFO_HEADERSENTRY']._options = None
  _globals['_SIPINBOUNDTRUNKINFO_HEADERSENTRY']._serialized_options = b'8\001'
  _globals['_SIPINBOUNDTRUNKINFO_HEADERSTOATTRIBUTESENTRY']._options = None
  _globals['_SIPINBOUNDTRUNKINFO_HEADERSTOATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_SIPINBOUNDTRUNKINFO_ATTRIBUTESTOHEADERSENTRY']._options = None
  _globals['_SIPINBOUNDTRUNKINFO_ATTRIBUTESTOHEADERSENTRY']._serialized_options = b'8\001'
  _globals['_SIPOUTBOUNDTRUNKINFO_HEADERSENTRY']._options = None
  _globals['_SIPOUTBOUNDTRUNKINFO_HEADERSENTRY']._serialized_options = b'8\001'
  _globals['_SIPOUTBOUNDTRUNKINFO_HEADERSTOATTRIBUTESENTRY']._options = None
  _globals['_SIPOUTBOUNDTRUNKINFO_HEADERSTOATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_SIPOUTBOUNDTRUNKINFO_ATTRIBUTESTOHEADERSENTRY']._options = None
  _globals['_SIPOUTBOUNDTRUNKINFO_ATTRIBUTESTOHEADERSENTRY']._serialized_options = b'8\001'
  _globals['_LISTSIPTRUNKREQUEST']._options = None
  _globals['_LISTSIPTRUNKREQUEST']._serialized_options = b'\030\001'
  _globals['_LISTSIPTRUNKRESPONSE']._options = None
  _globals['_LISTSIPTRUNKRESPONSE']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST_ATTRIBUTESENTRY']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['rule']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['rule']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['trunk_ids']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['trunk_ids']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['hide_phone_number']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['hide_phone_number']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['inbound_numbers']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['inbound_numbers']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['name']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['name']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['metadata']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['metadata']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['attributes']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['attributes']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['room_preset']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['room_preset']._serialized_options = b'\030\001'
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['room_config']._options = None
  _globals['_CREATESIPDISPATCHRULEREQUEST'].fields_by_name['room_config']._serialized_options = b'\030\001'
  _globals['_SIPDISPATCHRULEINFO_ATTRIBUTESENTRY']._options = None
  _globals['_SIPDISPATCHRULEINFO_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_SIPDISPATCHRULEUPDATE_ATTRIBUTESENTRY']._options = None
  _globals['_SIPDISPATCHRULEUPDATE_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_SIPOUTBOUNDCONFIG_HEADERSTOATTRIBUTESENTRY']._options = None
  _globals['_SIPOUTBOUNDCONFIG_HEADERSTOATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_SIPOUTBOUNDCONFIG_ATTRIBUTESTOHEADERSENTRY']._options = None
  _globals['_SIPOUTBOUNDCONFIG_ATTRIBUTESTOHEADERSENTRY']._serialized_options = b'8\001'
  _globals['_CREATESIPPARTICIPANTREQUEST_PARTICIPANTATTRIBUTESENTRY']._options = None
  _globals['_CREATESIPPARTICIPANTREQUEST_PARTICIPANTATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_CREATESIPPARTICIPANTREQUEST_HEADERSENTRY']._options = None
  _globals['_CREATESIPPARTICIPANTREQUEST_HEADERSENTRY']._serialized_options = b'8\001'
  _globals['_CREATESIPPARTICIPANTREQUEST'].fields_by_name['play_ringtone']._options = None
  _globals['_CREATESIPPARTICIPANTREQUEST'].fields_by_name['play_ringtone']._serialized_options = b'\030\001'
  _globals['_TRANSFERSIPPARTICIPANTREQUEST_HEADERSENTRY']._options = None
  _globals['_TRANSFERSIPPARTICIPANTREQUEST_HEADERSENTRY']._serialized_options = b'8\001'
  _globals['_SIPCALLINFO_PARTICIPANTATTRIBUTESENTRY']._options = None
  _globals['_SIPCALLINFO_PARTICIPANTATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_SIPCALLINFO'].fields_by_name['created_at']._options = None
  _globals['_SIPCALLINFO'].fields_by_name['created_at']._serialized_options = b'\030\001'
  _globals['_SIPCALLINFO'].fields_by_name['started_at']._options = None
  _globals['_SIPCALLINFO'].fields_by_name['started_at']._serialized_options = b'\030\001'
  _globals['_SIPCALLINFO'].fields_by_name['ended_at']._options = None
  _globals['_SIPCALLINFO'].fields_by_name['ended_at']._serialized_options = b'\030\001'
  _globals['_SIP'].methods_by_name['ListSIPTrunk']._options = None
  _globals['_SIP'].methods_by_name['ListSIPTrunk']._serialized_options = b'\210\002\001'
  _globals['_SIPSTATUSCODE']._serialized_start=9733
  _globals['_SIPSTATUSCODE']._serialized_end=11380
  _globals['_SIPTRANSPORT']._serialized_start=11382
  _globals['_SIPTRANSPORT']._serialized_end=11489
  _globals['_SIPHEADEROPTIONS']._serialized_start=11491
  _globals['_SIPHEADEROPTIONS']._serialized_end=11569
  _globals['_SIPMEDIAENCRYPTION']._serialized_start=11571
  _globals['_SIPMEDIAENCRYPTION']._serialized_end=11682
  _globals['_SIPCALLSTATUS']._serialized_start=11684
  _globals['_SIPCALLSTATUS']._serialized_end=11803
  _globals['_SIPTRANSFERSTATUS']._serialized_start=11805
  _globals['_SIPTRANSFERSTATUS']._serialized_end=11904
  _globals['_SIPFEATURE']._serialized_start=11906
  _globals['_SIPFEATURE']._serialized_end=11947
  _globals['_SIPCALLDIRECTION']._serialized_start=11949
  _globals['_SIPCALLDIRECTION']._serialized_end=12019
  _globals['_SIPSTATUS']._serialized_start=133
  _globals['_SIPSTATUS']._serialized_end=198
  _globals['_CREATESIPTRUNKREQUEST']._serialized_start=201
  _globals['_CREATESIPTRUNKREQUEST']._serialized_end=504
  _globals['_SIPTRUNKINFO']._serialized_start=507
  _globals['_SIPTRUNKINFO']._serialized_end=982
  _globals['_SIPTRUNKINFO_TRUNKKIND']._serialized_start=910
  _globals['_SIPTRUNKINFO_TRUNKKIND']._serialized_end=978
  _globals['_CREATESIPINBOUNDTRUNKREQUEST']._serialized_start=984
  _globals['_CREATESIPINBOUNDTRUNKREQUEST']._serialized_end=1059
  _globals['_UPDATESIPINBOUNDTRUNKREQUEST']._serialized_start=1062
  _globals['_UPDATESIPINBOUNDTRUNKREQUEST']._serialized_end=1223
  _globals['_SIPINBOUNDTRUNKINFO']._serialized_start=1226
  _globals['_SIPINBOUNDTRUNKINFO']._serialized_end=2052
  _globals['_SIPINBOUNDTRUNKINFO_HEADERSENTRY']._serialized_start=1886
  _globals['_SIPINBOUNDTRUNKINFO_HEADERSENTRY']._serialized_end=1932
  _globals['_SIPINBOUNDTRUNKINFO_HEADERSTOATTRIBUTESENTRY']._serialized_start=1934
  _globals['_SIPINBOUNDTRUNKINFO_HEADERSTOATTRIBUTESENTRY']._serialized_end=1992
  _globals['_SIPINBOUNDTRUNKINFO_ATTRIBUTESTOHEADERSENTRY']._serialized_start=1994
  _globals['_SIPINBOUNDTRUNKINFO_ATTRIBUTESTOHEADERSENTRY']._serialized_end=2052
  _globals['_SIPINBOUNDTRUNKUPDATE']._serialized_start=2055
  _globals['_SIPINBOUNDTRUNKUPDATE']._serialized_end=2447
  _globals['_CREATESIPOUTBOUNDTRUNKREQUEST']._serialized_start=2449
  _globals['_CREATESIPOUTBOUNDTRUNKREQUEST']._serialized_end=2526
  _globals['_UPDATESIPOUTBOUNDTRUNKREQUEST']._serialized_start=2529
  _globals['_UPDATESIPOUTBOUNDTRUNKREQUEST']._serialized_end=2693
  _globals['_SIPOUTBOUNDTRUNKINFO']._serialized_start=2696
  _globals['_SIPOUTBOUNDTRUNKINFO']._serialized_end=3433
  _globals['_SIPOUTBOUNDTRUNKINFO_HEADERSENTRY']._serialized_start=1886
  _globals['_SIPOUTBOUNDTRUNKINFO_HEADERSENTRY']._serialized_end=1932
  _globals['_SIPOUTBOUNDTRUNKINFO_HEADERSTOATTRIBUTESENTRY']._serialized_start=1934
  _globals['_SIPOUTBOUNDTRUNKINFO_HEADERSTOATTRIBUTESENTRY']._serialized_end=1992
  _globals['_SIPOUTBOUNDTRUNKINFO_ATTRIBUTESTOHEADERSENTRY']._serialized_start=1994
  _globals['_SIPOUTBOUNDTRUNKINFO_ATTRIBUTESTOHEADERSENTRY']._serialized_end=2052
  _globals['_SIPOUTBOUNDTRUNKUPDATE']._serialized_start=3436
  _globals['_SIPOUTBOUNDTRUNKUPDATE']._serialized_end=3888
  _globals['_GETSIPINBOUNDTRUNKREQUEST']._serialized_start=3890
  _globals['_GETSIPINBOUNDTRUNKREQUEST']._serialized_end=3939
  _globals['_GETSIPINBOUNDTRUNKRESPONSE']._serialized_start=3941
  _globals['_GETSIPINBOUNDTRUNKRESPONSE']._serialized_end=4014
  _globals['_GETSIPOUTBOUNDTRUNKREQUEST']._serialized_start=4016
  _globals['_GETSIPOUTBOUNDTRUNKREQUEST']._serialized_end=4066
  _globals['_GETSIPOUTBOUNDTRUNKRESPONSE']._serialized_start=4068
  _globals['_GETSIPOUTBOUNDTRUNKRESPONSE']._serialized_end=4143
  _globals['_LISTSIPTRUNKREQUEST']._serialized_start=4145
  _globals['_LISTSIPTRUNKREQUEST']._serialized_end=4205
  _globals['_LISTSIPTRUNKRESPONSE']._serialized_start=4207
  _globals['_LISTSIPTRUNKRESPONSE']._serialized_end=4271
  _globals['_LISTSIPINBOUNDTRUNKREQUEST']._serialized_start=4273
  _globals['_LISTSIPINBOUNDTRUNKREQUEST']._serialized_end=4372
  _globals['_LISTSIPINBOUNDTRUNKRESPONSE']._serialized_start=4374
  _globals['_LISTSIPINBOUNDTRUNKRESPONSE']._serialized_end=4448
  _globals['_LISTSIPOUTBOUNDTRUNKREQUEST']._serialized_start=4450
  _globals['_LISTSIPOUTBOUNDTRUNKREQUEST']._serialized_end=4550
  _globals['_LISTSIPOUTBOUNDTRUNKRESPONSE']._serialized_start=4552
  _globals['_LISTSIPOUTBOUNDTRUNKRESPONSE']._serialized_end=4628
  _globals['_DELETESIPTRUNKREQUEST']._serialized_start=4630
  _globals['_DELETESIPTRUNKREQUEST']._serialized_end=4675
  _globals['_SIPDISPATCHRULEDIRECT']._serialized_start=4677
  _globals['_SIPDISPATCHRULEDIRECT']._serialized_end=4732
  _globals['_SIPDISPATCHRULEINDIVIDUAL']._serialized_start=4734
  _globals['_SIPDISPATCHRULEINDIVIDUAL']._serialized_end=4795
  _globals['_SIPDISPATCHRULECALLEE']._serialized_start=4797
  _globals['_SIPDISPATCHRULECALLEE']._serialized_end=4873
  _globals['_SIPDISPATCHRULE']._serialized_start=4876
  _globals['_SIPDISPATCHRULE']._serialized_end=5101
  _globals['_CREATESIPDISPATCHRULEREQUEST']._serialized_start=5104
  _globals['_CREATESIPDISPATCHRULEREQUEST']._serialized_end=5562
  _globals['_CREATESIPDISPATCHRULEREQUEST_ATTRIBUTESENTRY']._serialized_start=5513
  _globals['_CREATESIPDISPATCHRULEREQUEST_ATTRIBUTESENTRY']._serialized_end=5562
  _globals['_UPDATESIPDISPATCHRULEREQUEST']._serialized_start=5565
  _globals['_UPDATESIPDISPATCHRULEREQUEST']._serialized_end=5734
  _globals['_SIPDISPATCHRULEINFO']._serialized_start=5737
  _globals['_SIPDISPATCHRULEINFO']._serialized_end=6196
  _globals['_SIPDISPATCHRULEINFO_ATTRIBUTESENTRY']._serialized_start=5513
  _globals['_SIPDISPATCHRULEINFO_ATTRIBUTESENTRY']._serialized_end=5562
  _globals['_SIPDISPATCHRULEUPDATE']._serialized_start=6199
  _globals['_SIPDISPATCHRULEUPDATE']._serialized_end=6566
  _globals['_SIPDISPATCHRULEUPDATE_ATTRIBUTESENTRY']._serialized_start=5513
  _globals['_SIPDISPATCHRULEUPDATE_ATTRIBUTESENTRY']._serialized_end=5562
  _globals['_LISTSIPDISPATCHRULEREQUEST']._serialized_start=6568
  _globals['_LISTSIPDISPATCHRULEREQUEST']._serialized_end=6677
  _globals['_LISTSIPDISPATCHRULERESPONSE']._serialized_start=6679
  _globals['_LISTSIPDISPATCHRULERESPONSE']._serialized_end=6753
  _globals['_DELETESIPDISPATCHRULEREQUEST']._serialized_start=6755
  _globals['_DELETESIPDISPATCHRULEREQUEST']._serialized_end=6815
  _globals['_SIPOUTBOUNDCONFIG']._serialized_start=6818
  _globals['_SIPOUTBOUNDCONFIG']._serialized_end=7260
  _globals['_SIPOUTBOUNDCONFIG_HEADERSTOATTRIBUTESENTRY']._serialized_start=1934
  _globals['_SIPOUTBOUNDCONFIG_HEADERSTOATTRIBUTESENTRY']._serialized_end=1992
  _globals['_SIPOUTBOUNDCONFIG_ATTRIBUTESTOHEADERSENTRY']._serialized_start=1994
  _globals['_SIPOUTBOUNDCONFIG_ATTRIBUTESTOHEADERSENTRY']._serialized_end=2052
  _globals['_CREATESIPPARTICIPANTREQUEST']._serialized_start=7263
  _globals['_CREATESIPPARTICIPANTREQUEST']._serialized_end=8134
  _globals['_CREATESIPPARTICIPANTREQUEST_PARTICIPANTATTRIBUTESENTRY']._serialized_start=8026
  _globals['_CREATESIPPARTICIPANTREQUEST_PARTICIPANTATTRIBUTESENTRY']._serialized_end=8086
  _globals['_CREATESIPPARTICIPANTREQUEST_HEADERSENTRY']._serialized_start=1886
  _globals['_CREATESIPPARTICIPANTREQUEST_HEADERSENTRY']._serialized_end=1932
  _globals['_SIPPARTICIPANTINFO']._serialized_start=8136
  _globals['_SIPPARTICIPANTINFO']._serialized_end=8250
  _globals['_TRANSFERSIPPARTICIPANTREQUEST']._serialized_start=8253
  _globals['_TRANSFERSIPPARTICIPANTREQUEST']._serialized_end=8547
  _globals['_TRANSFERSIPPARTICIPANTREQUEST_HEADERSENTRY']._serialized_start=1886
  _globals['_TRANSFERSIPPARTICIPANTREQUEST_HEADERSENTRY']._serialized_end=1932
  _globals['_SIPCALLINFO']._serialized_start=8550
  _globals['_SIPCALLINFO']._serialized_end=9359
  _globals['_SIPCALLINFO_PARTICIPANTATTRIBUTESENTRY']._serialized_start=8026
  _globals['_SIPCALLINFO_PARTICIPANTATTRIBUTESENTRY']._serialized_end=8086
  _globals['_SIPTRANSFERINFO']._serialized_start=9362
  _globals['_SIPTRANSFERINFO']._serialized_end=9624
  _globals['_SIPURI']._serialized_start=9626
  _globals['_SIPURI']._serialized_end=9730
  _globals['_SIP']._serialized_start=12022
  _globals['_SIP']._serialized_end=13517
# @@protoc_insertion_point(module_scope)
