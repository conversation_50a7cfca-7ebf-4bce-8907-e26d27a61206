narwhals-2.1.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
narwhals-2.1.2.dist-info/METADATA,sha256=0pK_IezGWhTpd-IpdMyZ4Z5rPZsDH4BZFDkjOeCMUC0,11312
narwhals-2.1.2.dist-info/RECORD,,
narwhals-2.1.2.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
narwhals-2.1.2.dist-info/licenses/LICENSE.md,sha256=heMD6hta6RzeBucppx59AUCgr_ukRY0ABj0bcrN3mKs,1071
narwhals/__init__.py,sha256=FGVSkIo-2xnlr-PAnUz9_lUsoAVLmwSko7vvKt1dhwM,3206
narwhals/__pycache__/__init__.cpython-312.pyc,,
narwhals/__pycache__/_constants.cpython-312.pyc,,
narwhals/__pycache__/_duration.cpython-312.pyc,,
narwhals/__pycache__/_enum.cpython-312.pyc,,
narwhals/__pycache__/_exceptions.cpython-312.pyc,,
narwhals/__pycache__/_expression_parsing.cpython-312.pyc,,
narwhals/__pycache__/_namespace.cpython-312.pyc,,
narwhals/__pycache__/_translate.cpython-312.pyc,,
narwhals/__pycache__/_typing_compat.cpython-312.pyc,,
narwhals/__pycache__/_utils.cpython-312.pyc,,
narwhals/__pycache__/dataframe.cpython-312.pyc,,
narwhals/__pycache__/dependencies.cpython-312.pyc,,
narwhals/__pycache__/dtypes.cpython-312.pyc,,
narwhals/__pycache__/exceptions.cpython-312.pyc,,
narwhals/__pycache__/expr.cpython-312.pyc,,
narwhals/__pycache__/expr_cat.cpython-312.pyc,,
narwhals/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/__pycache__/expr_list.cpython-312.pyc,,
narwhals/__pycache__/expr_name.cpython-312.pyc,,
narwhals/__pycache__/expr_str.cpython-312.pyc,,
narwhals/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/__pycache__/functions.cpython-312.pyc,,
narwhals/__pycache__/group_by.cpython-312.pyc,,
narwhals/__pycache__/schema.cpython-312.pyc,,
narwhals/__pycache__/selectors.cpython-312.pyc,,
narwhals/__pycache__/series.cpython-312.pyc,,
narwhals/__pycache__/series_cat.cpython-312.pyc,,
narwhals/__pycache__/series_dt.cpython-312.pyc,,
narwhals/__pycache__/series_list.cpython-312.pyc,,
narwhals/__pycache__/series_str.cpython-312.pyc,,
narwhals/__pycache__/series_struct.cpython-312.pyc,,
narwhals/__pycache__/this.cpython-312.pyc,,
narwhals/__pycache__/translate.cpython-312.pyc,,
narwhals/__pycache__/typing.cpython-312.pyc,,
narwhals/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_arrow/__pycache__/__init__.cpython-312.pyc,,
narwhals/_arrow/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_arrow/__pycache__/expr.cpython-312.pyc,,
narwhals/_arrow/__pycache__/group_by.cpython-312.pyc,,
narwhals/_arrow/__pycache__/namespace.cpython-312.pyc,,
narwhals/_arrow/__pycache__/selectors.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_list.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_str.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_arrow/__pycache__/typing.cpython-312.pyc,,
narwhals/_arrow/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/dataframe.py,sha256=yqNkKBv3Gp-JKCHLdRzbAxkal7OYAZmCC1Gz1dBFG5k,27928
narwhals/_arrow/expr.py,sha256=fzEgEwVXETPfoxyvsI7fwTRGuh_t7BNCih0QP-fK4Io,6436
narwhals/_arrow/group_by.py,sha256=SkDRYpKaZXkwxtC-5s1yinBSgVgj2KoAiFFpjSvo9Fo,6458
narwhals/_arrow/namespace.py,sha256=kGQ6WM-JSTrejGEKrWIn7LQ8jTVYT8vYv1IDwhFiWYQ,12056
narwhals/_arrow/selectors.py,sha256=qIfCnMNlQ5svQzGaB-DV5YE4xSaUaVzElTPYJl_0BJc,1128
narwhals/_arrow/series.py,sha256=FbSWnApKEJOsrg_gb349PQDLMVxS-fRTfNeBQXocyNo,44428
narwhals/_arrow/series_cat.py,sha256=vvNlPaHHcA-ORzh_79-oY03wt6aIg1rLI0At8FXr2Ok,598
narwhals/_arrow/series_dt.py,sha256=tTJg3Kxde356LNmqfHOHseWkNofyigFu7s17EtEYTQs,8922
narwhals/_arrow/series_list.py,sha256=hhIE7wZGVQs-J9iX-RyP4sedZ413fStDDj2aW006ALI,647
narwhals/_arrow/series_str.py,sha256=iouTrb8GkJJlZc2ImMLRt8Knh3SvuygVmqORrJc_FSA,3998
narwhals/_arrow/series_struct.py,sha256=85pQSUqOdeMyjsnjaSr_4YBC2HRGD-dsnNy2tPveJRM,410
narwhals/_arrow/typing.py,sha256=TmgG8eqF4uCRW5NFzWTiBvlUGvD46govtIC8gRyrkmA,2286
narwhals/_arrow/utils.py,sha256=SXKRu3aRfRwzdpg1Gagfwb1Ll5dBDd4PPev3I--EX5s,15940
narwhals/_compliant/__init__.py,sha256=fN7ey51bkGcJ2D3G-SmcEXQWA1nwt9LI2xEW4MYP1rY,2466
narwhals/_compliant/__pycache__/__init__.cpython-312.pyc,,
narwhals/_compliant/__pycache__/any_namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/column.cpython-312.pyc,,
narwhals/_compliant/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_compliant/__pycache__/expr.cpython-312.pyc,,
narwhals/_compliant/__pycache__/group_by.cpython-312.pyc,,
narwhals/_compliant/__pycache__/namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/selectors.cpython-312.pyc,,
narwhals/_compliant/__pycache__/series.cpython-312.pyc,,
narwhals/_compliant/__pycache__/typing.cpython-312.pyc,,
narwhals/_compliant/__pycache__/when_then.cpython-312.pyc,,
narwhals/_compliant/__pycache__/window.cpython-312.pyc,,
narwhals/_compliant/any_namespace.py,sha256=kXBY2yAN8Oz5E7k-vlq_aQHwHVEmJtYZbL_BgG089xo,3698
narwhals/_compliant/column.py,sha256=kkuUO14wlBubbnKXYsd63Zi-DH87KlZPhsuMfVOzNmU,7339
narwhals/_compliant/dataframe.py,sha256=wuTsNCSB2ye66TCvzYjwQh0S81La7ySO_eWyNj6UZ6E,16291
narwhals/_compliant/expr.py,sha256=CJtA4z4Kd_zmDTh2c6jkjXMIb-yahuh6lt3ZhM-DeDc,38947
narwhals/_compliant/group_by.py,sha256=QAOCqvaK-Q7I7FsaBYsIkWFzazTaTn_mQlVVJDPtt8s,6961
narwhals/_compliant/namespace.py,sha256=hP0zh5vgWI7Vh4dmD95ttb3nnpk76wLPZgPhGqAcubI,7171
narwhals/_compliant/selectors.py,sha256=4UMULBdP3Sfw5PaTKpSVifnXeGFbialOq5qVar0cgyk,11561
narwhals/_compliant/series.py,sha256=z0ktqZ2LsIlYV9jTxPPX4AngZ5iyJfuv1XmOwFxhzdE,13819
narwhals/_compliant/typing.py,sha256=qtWtyVOXwgbIE7rUf6O3CngklYJqVYp6CExh2qqM1iQ,7081
narwhals/_compliant/when_then.py,sha256=hY2O8dNYUaa-9OTUzYYfrzmQp0w13cEf0GtV1hKAiWs,4323
narwhals/_compliant/window.py,sha256=_ji4goVKkT4YPTyZa_I0N2yGmwBfB1_LDG0WSXGbmlo,505
narwhals/_constants.py,sha256=kE1KWsIky4ryabH-Z117ZtGW24ccTcreWOZJjpacO6I,1094
narwhals/_dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_dask/__pycache__/__init__.cpython-312.pyc,,
narwhals/_dask/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_dask/__pycache__/group_by.cpython-312.pyc,,
narwhals/_dask/__pycache__/namespace.cpython-312.pyc,,
narwhals/_dask/__pycache__/selectors.cpython-312.pyc,,
narwhals/_dask/__pycache__/utils.cpython-312.pyc,,
narwhals/_dask/dataframe.py,sha256=YvI1c4W5OoPzGdnLu_desUBmVwa2oiNhHO57cCMoPZ4,17415
narwhals/_dask/expr.py,sha256=0Cq7V_bTlwb0EtabHBICbmghITMt_es7F2-TLg_cfZM,25201
narwhals/_dask/expr_dt.py,sha256=7vaSQPZIWsyQsZVP0vN9_60hP6bOI0WP5UDF7jksl_Y,6886
narwhals/_dask/expr_str.py,sha256=SrDcJq_3rHvx1jfQcfi07oS0SGnVkcLE6Xu3uPZfkuA,3558
narwhals/_dask/group_by.py,sha256=2-iqle5m6i5TC8nKRl7B1t3nsAJUXDsUurRQkMFovV4,4860
narwhals/_dask/namespace.py,sha256=du47m6M3oAEUjRE5gdh1FDgXwXH0hJzZVsBbALgJe7A,13575
narwhals/_dask/selectors.py,sha256=FafFcfFWM6uTcKUsEeqfbmBUIgYVzH5XdN6sFUVLMKU,1148
narwhals/_dask/utils.py,sha256=qdsSkVId_G6i778nfWEl5xqb1Kaq4MjkhGmUGG0eBnY,5484
narwhals/_duckdb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_duckdb/__pycache__/__init__.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/group_by.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/namespace.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/selectors.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/series.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/typing.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/utils.cpython-312.pyc,,
narwhals/_duckdb/dataframe.py,sha256=muAwdfug9-6Z_40g3GBkb000lxPvsNVLqzYifor1zG0,19532
narwhals/_duckdb/expr.py,sha256=CGajgB01QGGr-24pnEW1kdL3VULrJmfI43aLYWz7wa4,10657
narwhals/_duckdb/expr_dt.py,sha256=QoNbABk0aIuRNyIa790HvisfB177_Ds1H_xMiZWNnHM,4990
narwhals/_duckdb/expr_list.py,sha256=gXPHQZ3oqliATMLye7JugEar-SKOTliCYkfjKv6KZBM,1326
narwhals/_duckdb/expr_str.py,sha256=M7UTLjnHI66I7XYGECORpsJwrrYaYUxyesK2NqGGuok,999
narwhals/_duckdb/expr_struct.py,sha256=eN06QA1JS6wjAt7_AZzW3xoztHM_hoadlFUl_hwsEiE,576
narwhals/_duckdb/group_by.py,sha256=nuueeiJYRcs31Ja973VvtLbWM2wnms0GYL7kAHDeju0,1123
narwhals/_duckdb/namespace.py,sha256=XyTCDc-sdSCdtasrqbkJhFviY375Y2fW_CvCNb0LtPo,5476
narwhals/_duckdb/selectors.py,sha256=yA16Z-MlJUJBjOu0XI9qVO4Zx7L_T5FN2DQqNAYhu-o,1033
narwhals/_duckdb/series.py,sha256=xBpuPUnSSIQ1vYEKjHQFZN7ix1ZyMwSchliDPpkf3Wk,1397
narwhals/_duckdb/typing.py,sha256=gO_Odyinkn4QZY_TU4uuzda6mbeo38glOOUUripcWgg,454
narwhals/_duckdb/utils.py,sha256=LsUYkxwOJNQ5b9qyAWe2VYX_292t1x0GK_ubxf9Vi-Y,13599
narwhals/_duration.py,sha256=WGzj3FVcC2KogqRhNeim3YDIwUn8HkXQHAljtvHrjwQ,3139
narwhals/_enum.py,sha256=sUR-04yIwjAMsX5eelKnc1UKXc5dBoj1do0krubAE04,1192
narwhals/_exceptions.py,sha256=OhT0MiQbcw_wE85Bl1YYZJjvtlX0rJMNUoZtKNCjTq8,1928
narwhals/_expression_parsing.py,sha256=RQSuNigmVOZ3PocYKi0l2K4JkdRQyZPDg3RChKPuCzI,23181
narwhals/_ibis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_ibis/__pycache__/__init__.cpython-312.pyc,,
narwhals/_ibis/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_ibis/__pycache__/group_by.cpython-312.pyc,,
narwhals/_ibis/__pycache__/namespace.cpython-312.pyc,,
narwhals/_ibis/__pycache__/selectors.cpython-312.pyc,,
narwhals/_ibis/__pycache__/series.cpython-312.pyc,,
narwhals/_ibis/__pycache__/utils.cpython-312.pyc,,
narwhals/_ibis/dataframe.py,sha256=L5m7I8Cvf0bITpdBnp8BiWzYfGeG7g9h6CN2_NTgosA,16072
narwhals/_ibis/expr.py,sha256=nqYSlp6fS6_LjEtAhno3ngoJ91zJL2tJtPgoGfj17t4,13090
narwhals/_ibis/expr_dt.py,sha256=2sDgjR5HalXE3IBUc7LvIe4QPjFCnafJPs9ZikbW5xw,3314
narwhals/_ibis/expr_list.py,sha256=TSfb_4EKRdTFIbZ2VJ9zqXJl62ZDkivweK5BiUWFsBc,948
narwhals/_ibis/expr_str.py,sha256=y36FonuKUe5cGUT-wAQriy_4sR-bbb_kPfMBCisomHk,2592
narwhals/_ibis/expr_struct.py,sha256=FDsa5MqcHhqPmpZIEfGBASdqxPkyImrlGTH7XUSw3cs,565
narwhals/_ibis/group_by.py,sha256=enNzAPUsA_LIwPNJ7jG_MJKyqG2HyCiesBEX3pJgJBg,1031
narwhals/_ibis/namespace.py,sha256=0hRod5QixeON0gr4XCqEJrHh3Wa3JK9_4Dz7MTJlFbI,5521
narwhals/_ibis/selectors.py,sha256=SkFxoukpKc_OjwKoKHRm8VwMaphCMUeWBJ2g_oWz3D0,961
narwhals/_ibis/series.py,sha256=CZDwDPsdELKtdr7OWmcFyGqexr33Ucfnv_RU95VJxIQ,1218
narwhals/_ibis/utils.py,sha256=C77hUPh-gl07T31XUHLy1YtSX7nlKvHfpjtQHn007w0,9288
narwhals/_interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_interchange/__pycache__/__init__.cpython-312.pyc,,
narwhals/_interchange/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_interchange/__pycache__/series.cpython-312.pyc,,
narwhals/_interchange/dataframe.py,sha256=ln9qzwPvb-KPUenoutNWE-870iBeiMZLyVlvlZ77EEo,5902
narwhals/_interchange/series.py,sha256=nSxdlOZrw3wtavS42TMR_b_EGgPBv224ioZBMo5eoC8,1651
narwhals/_namespace.py,sha256=4Z7EduNVBynT-4zvxpMQ4rRgN9yGNi_yJYwyWLhr3dU,15254
narwhals/_pandas_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_pandas_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_list.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_str.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_pandas_like/dataframe.py,sha256=JBI4E2pSS5Gy-Kcas6MmiQSEVVynWHlPxw2vMzBTbTQ,41744
narwhals/_pandas_like/expr.py,sha256=TH1R3ySx2MAAopASc6vW9wIa3DYIaVH7hBsM-Yn6kK8,14833
narwhals/_pandas_like/group_by.py,sha256=I6fb_Anr_D6hL0IuisskJQi2HVr_NwMk4qjt9kJTEkE,12019
narwhals/_pandas_like/namespace.py,sha256=cr4LxcfHSq13B_pKjnuE1PIvKrhmQtHWtgA_kIMwJFw,16854
narwhals/_pandas_like/selectors.py,sha256=Qf7r0H6R8cniwDwC2zlWxddsPx-AHFsZwDPQ9iCEiH8,1261
narwhals/_pandas_like/series.py,sha256=DM7xAtODaN-Cuu-E8aidvQLasDN0vaYBaLT3EVZ9f-E,41993
narwhals/_pandas_like/series_cat.py,sha256=MJwCnJ49hfnODh6JgMHOCQ2KBlTbmySU6_X4XWaqiz4,527
narwhals/_pandas_like/series_dt.py,sha256=EnNPp6StDmwDX9p3mlQw3m4pZWOp0UA1Zh5bJr-gOZE,11587
narwhals/_pandas_like/series_list.py,sha256=xc9m4c5ftCQPfiTy7EujhfNHn7KHbjBUNa-iXHdV9t8,1391
narwhals/_pandas_like/series_str.py,sha256=r_iqLsVZt29ZqGKKcdHupqlror_C8VDU04twU48L3dc,3680
narwhals/_pandas_like/series_struct.py,sha256=vX9HoO42vHackvVozUfp8odM9uJ4owct49ydKDnohdk,518
narwhals/_pandas_like/typing.py,sha256=Awm2YnewvdA3l_4SEwb_5AithhwBYNx1t1ajaHnvUsM,1064
narwhals/_pandas_like/utils.py,sha256=x7DPArSs6CQPc211udUl6eYp_xFWp3xXVSnMRyWCLWM,25293
narwhals/_polars/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_polars/__pycache__/__init__.cpython-312.pyc,,
narwhals/_polars/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_polars/__pycache__/expr.cpython-312.pyc,,
narwhals/_polars/__pycache__/group_by.cpython-312.pyc,,
narwhals/_polars/__pycache__/namespace.cpython-312.pyc,,
narwhals/_polars/__pycache__/series.cpython-312.pyc,,
narwhals/_polars/__pycache__/typing.cpython-312.pyc,,
narwhals/_polars/__pycache__/utils.cpython-312.pyc,,
narwhals/_polars/dataframe.py,sha256=419MSSsS3Fi3W_aFJyFTJgqDrddK7-wCR-MRssPb2MY,21778
narwhals/_polars/expr.py,sha256=CiS_RztXRms58LWGjaynXLZCkErf5C6L0f9KBYPraUM,16066
narwhals/_polars/group_by.py,sha256=v88hD-rOCNtCeT_YqMVII2V1c1B5TEwd0s6qOa1yXb4,2491
narwhals/_polars/namespace.py,sha256=TcQo-hUXfgFmWZK6u_ruRpm-VPpxZ3q67CI-sIdCu_A,9662
narwhals/_polars/series.py,sha256=bKQ53NHyJkqp7wq2qFIU--bF2f4bWsON8T25NCqa00A,25731
narwhals/_polars/typing.py,sha256=tiAgtciFZmlqqH3Q6MdQLXZEb1ajH-YbePpaKjeuqQE,786
narwhals/_polars/utils.py,sha256=q5hLZYWtLQpA1YTRH8_wOVXWo8ikrHkRcI7KA6AFglE,12029
narwhals/_spark_like/__init__.py,sha256=T7_UCePrrYs1ZeMatvCUYUvQcXvrDjQ4b08_ugWIHAo,87
narwhals/_spark_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_spark_like/dataframe.py,sha256=v9zRtBdOTUCv302oEyEyfb59PHCqbB0pioR0eWdCQA0,21682
narwhals/_spark_like/expr.py,sha256=x0tQr-9-vO8mnu9VHRwaCDHfMoYRPOzOTxy39rGMoEM,14157
narwhals/_spark_like/expr_dt.py,sha256=nDDTQ__16EgflwP2j57SKRxTFC3GXyWwOweugHSwumk,7585
narwhals/_spark_like/expr_list.py,sha256=eWjSKtrxaKBRgNSvAzbAbCnp5hpplHGfBo0gFtwJZoo,1146
narwhals/_spark_like/expr_str.py,sha256=jBB2TahMTuR9IuLBLRDPh76e2nTrU8h1RTD1bQGo-UM,1326
narwhals/_spark_like/expr_struct.py,sha256=haBDpuRhn_nGAFjMF3arhhRr6NfefNei9vEmAOa0fQI,613
narwhals/_spark_like/group_by.py,sha256=rsAhSHEoA1pHzPk--9xtKvLJbTHOtJ45ftVKUhI7KUc,1246
narwhals/_spark_like/namespace.py,sha256=7oqYOfN1Yhi74jxon23X8D8BZ45HWB_FMxlgQ6FhD-U,7994
narwhals/_spark_like/selectors.py,sha256=SzJPoFjyIEviSSvPRvL81o1jjQJcM-Veqb52vFU66JQ,1086
narwhals/_spark_like/utils.py,sha256=Lf7NwfvqQaL6I1IsRwLTfYazR1E-pT2q18_7xri_2O0,11381
narwhals/_sql/__init__.py,sha256=T7_UCePrrYs1ZeMatvCUYUvQcXvrDjQ4b08_ugWIHAo,87
narwhals/_sql/__pycache__/__init__.cpython-312.pyc,,
narwhals/_sql/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_sql/__pycache__/expr.cpython-312.pyc,,
narwhals/_sql/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_sql/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_sql/__pycache__/group_by.cpython-312.pyc,,
narwhals/_sql/__pycache__/namespace.cpython-312.pyc,,
narwhals/_sql/__pycache__/typing.cpython-312.pyc,,
narwhals/_sql/__pycache__/when_then.cpython-312.pyc,,
narwhals/_sql/dataframe.py,sha256=QaoS2torw4nwB1eTqdVrF6pZMOsVXdByEvjWencaXiM,1462
narwhals/_sql/expr.py,sha256=udKmj9FEmC9mX4yNlX_rrhIkBX_AmkL7IJ6537O9BUA,28720
narwhals/_sql/expr_dt.py,sha256=CWFn7Ki3YW3XT_Hy88pdCTZs8j6jP4GpP38vgPd-vX4,1612
narwhals/_sql/expr_str.py,sha256=XNFamhOoxX9yW_zr2BEDXukzjWMCGL-yy3m5qW9pVm0,5061
narwhals/_sql/group_by.py,sha256=34PG1zfg3ZM_o1sP71lILfVuDzYT_HirdPoUesbYI40,1598
narwhals/_sql/namespace.py,sha256=3lMgLlI36K5yJhdwXLwuN-m6z8JSNRUCDwkpVJ2ooUc,2893
narwhals/_sql/typing.py,sha256=e3LkLPI4oa2IzykR7BgO9IIfCKRw0vrX4uHxPTB-uJM,487
narwhals/_sql/when_then.py,sha256=4lXcc_J_N6vHGby6kPJl1PGqLPUGbgHYuIXiYROyoW4,3636
narwhals/_translate.py,sha256=e8RjNCNX4QGJWKjM6VANDTG_bVT2VusjNfjsnkCBO3g,6112
narwhals/_typing_compat.py,sha256=h-BtLEl7CrZ-hYLlwYTOcCdsUS3dvgvkxQTcDQ7RYmA,2516
narwhals/_utils.py,sha256=-6mqXxXUQkLgcwlT4ZiRqtIjvhd5RZ5hpfZTYRQc6dw,65089
narwhals/dataframe.py,sha256=Rdou0nxWNn0xm24pCl4pzx_2eza1NyPnJ9fR6oXmmc4,131182
narwhals/dependencies.py,sha256=C_dqrNq9GCN1gcv18PwF7ALIjF7sHiCvWLqYxUVLnRk,18766
narwhals/dtypes.py,sha256=ZIAjCTpzASp14awMVwwqdFrY_j56uS0m0uP0IOeDWWI,24658
narwhals/exceptions.py,sha256=9ocrbLNP7fZLqP2gV1PS9OexNhzf8h7of2wR5wi5kE0,3704
narwhals/expr.py,sha256=f_dkZmQNvKU2AFLwjNbt5uy_13h_rLEPSw_2CeMbUz4,95385
narwhals/expr_cat.py,sha256=o4MhGmPoO3_DlkRB06Z3ilyqyj70mwcW3kftRayDq2s,1210
narwhals/expr_dt.py,sha256=R3F9z9FAluZBZ7ETt2lOdkMrq_OmG2YMYBpkIkGzUQc,32465
narwhals/expr_list.py,sha256=8-_L7wzxm90kcRooFW2QEdzn0FgJNMnUispBReBE5fs,6744
narwhals/expr_name.py,sha256=0QD8Yg7FKu739ENSJI2lxIGSjho89J9l9DjxeBOz9bM,4866
narwhals/expr_str.py,sha256=PoSIJB9R9l-RPwEXkchMbxonpc0N4Ppgb5bAkrUALL4,19519
narwhals/expr_struct.py,sha256=V_Hj3kChdcZPIlfb_Kslp5W-j-XGFcfdMFzYpZdjNWE,1742
narwhals/functions.py,sha256=QgDfmUxSDHjq3aNMmglcHZndxHM5kJHTUuXz7LCAjLE,65414
narwhals/group_by.py,sha256=7UkbFvCZ6n0rtgDovbElueEA8p4oS3i3ny_g5TGabek,7164
narwhals/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/schema.py,sha256=liy3_5wdGLOmcfR5LECH2b0lEcKD695Y4Os9kRRGNUs,5618
narwhals/selectors.py,sha256=ybbFG7Sjebr8qoMgD43O6QuHBGl52yUpGRe08L1LKyo,10759
narwhals/series.py,sha256=B6-rIx7AfbHPAsiwlj6TNA2SpI11OIQkz1e4V3YGWmc,89909
narwhals/series_cat.py,sha256=KU5DMtCqi0KKVrmTfCLpgI32AGuY3MYZclNF6soh1Xc,834
narwhals/series_dt.py,sha256=rBoJAkYPb0Xf1dq-cxbB809m9Sqv5ZzhjOhXDPMQ4cM,23027
narwhals/series_list.py,sha256=PMXSEL_Pai2ZoMcNi0KZ6WdXHlMvTVyFK600TTGhCeg,3802
narwhals/series_str.py,sha256=yT6DD17wR0vspZT7KaIvG3Fav2nHJ9JuxkTD5pqPOj0,15133
narwhals/series_struct.py,sha256=bixxdASlxYyP1prDjMmfltVU5212a596VQZE11yszUg,930
narwhals/stable/__init__.py,sha256=b9soCkGkQzgF5jO5EdQ6IOQpnc6G6eqWmY6WwpoSjhk,85
narwhals/stable/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__init__.py,sha256=epJ2xCRChIGmUYqjz20Pvdb7sVp-f86jSGC0kPTjRxU,42066
narwhals/stable/v1/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v1/_dtypes.py,sha256=7zGmarnurUTgY6DI4KQ1MSAC7B9ZZiI5Em7plb-HAEs,2700
narwhals/stable/v1/_namespace.py,sha256=gfsbT4R4aLmmdArY35LRpEHPiUeZKEEnXGiY9ypFtwE,296
narwhals/stable/v1/dependencies.py,sha256=aM0IShF4hbaaMEDRJQXvsu4RABZOdBG4QhrpJPxb7fg,5001
narwhals/stable/v1/dtypes.py,sha256=u2NFDJyCkjsK6p3K9ULJS7CoG16z0Z1MQiACTVkhkH4,1082
narwhals/stable/v1/selectors.py,sha256=xEA9bBzkpTwUanGGoFwBCcHIAXb8alwrPX1mjzE9mDM,312
narwhals/stable/v1/typing.py,sha256=fmC1UUCsXapGUEUQYi0fHMpKe4x-SgzoZOXPDB4cGZQ,6112
narwhals/stable/v2/__init__.py,sha256=e8sos0B-BK9X_QJESJE5Xo2YY10mEMtkyx9W4QPUkuE,38186
narwhals/stable/v2/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v2/_namespace.py,sha256=oYB5nrFGxqqTonkRx9vUanyBxGs2Yb0j7_juMyvnvWA,296
narwhals/stable/v2/dependencies.py,sha256=vpYWx_dron6wFdbQ60G06EV2UJ_LMd52LDodCrAY5Jg,86
narwhals/stable/v2/dtypes.py,sha256=iMpk2Kc1mNiQYmboOSgmiAijklSUBHSHF2LTKMKnGe8,80
narwhals/stable/v2/selectors.py,sha256=sjJL3agHd8Rgf_lWhgCmEKruhWEkwHdX32-n85OqVJU,83
narwhals/stable/v2/typing.py,sha256=5A_ug36wr9ViG7kRfdjMY1b5zCc8jxQ65U7r3CHBazc,5782
narwhals/this.py,sha256=BbKcj0ReWqE01lznzKjuqq7otXONvjBevWWC5aJhQxs,1584
narwhals/translate.py,sha256=-rwYy87aQzAO7AeHvkxrtUMERBmItZHH2CeIuab1puo,24927
narwhals/typing.py,sha256=AbmAZtyZ4A_K38YwCgBLnsmb4X8UwD2KhNwMyp9KgTA,16483
narwhals/utils.py,sha256=2GT3XxucWI6l9r9jTwMw7Aha2G73FsSXgXNFZ3O_ZyA,223
