# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: opentelemetry/proto/profiles/v1development/profiles.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from opentelemetry.proto.common.v1 import common_pb2 as opentelemetry_dot_proto_dot_common_dot_v1_dot_common__pb2
from opentelemetry.proto.resource.v1 import resource_pb2 as opentelemetry_dot_proto_dot_resource_dot_v1_dot_resource__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9opentelemetry/proto/profiles/v1development/profiles.proto\x12*opentelemetry.proto.profiles.v1development\x1a*opentelemetry/proto/common/v1/common.proto\x1a.opentelemetry/proto/resource/v1/resource.proto\"\xee\x03\n\x12ProfilesDictionary\x12J\n\rmapping_table\x18\x01 \x03(\x0b\x32\x33.opentelemetry.proto.profiles.v1development.Mapping\x12L\n\x0elocation_table\x18\x02 \x03(\x0b\x32\x34.opentelemetry.proto.profiles.v1development.Location\x12L\n\x0e\x66unction_table\x18\x03 \x03(\x0b\x32\x34.opentelemetry.proto.profiles.v1development.Function\x12\x44\n\nlink_table\x18\x04 \x03(\x0b\x32\x30.opentelemetry.proto.profiles.v1development.Link\x12\x14\n\x0cstring_table\x18\x05 \x03(\t\x12@\n\x0f\x61ttribute_table\x18\x06 \x03(\x0b\x32\'.opentelemetry.proto.common.v1.KeyValue\x12R\n\x0f\x61ttribute_units\x18\x07 \x03(\x0b\x32\x39.opentelemetry.proto.profiles.v1development.AttributeUnit\"\xbb\x01\n\x0cProfilesData\x12W\n\x11resource_profiles\x18\x01 \x03(\x0b\x32<.opentelemetry.proto.profiles.v1development.ResourceProfiles\x12R\n\ndictionary\x18\x02 \x01(\x0b\x32>.opentelemetry.proto.profiles.v1development.ProfilesDictionary\"\xbe\x01\n\x10ResourceProfiles\x12;\n\x08resource\x18\x01 \x01(\x0b\x32).opentelemetry.proto.resource.v1.Resource\x12Q\n\x0escope_profiles\x18\x02 \x03(\x0b\x32\x39.opentelemetry.proto.profiles.v1development.ScopeProfiles\x12\x12\n\nschema_url\x18\x03 \x01(\tJ\x06\x08\xe8\x07\x10\xe9\x07\"\xae\x01\n\rScopeProfiles\x12\x42\n\x05scope\x18\x01 \x01(\x0b\x32\x33.opentelemetry.proto.common.v1.InstrumentationScope\x12\x45\n\x08profiles\x18\x02 \x03(\x0b\x32\x33.opentelemetry.proto.profiles.v1development.Profile\x12\x12\n\nschema_url\x18\x03 \x01(\t\"\x86\x04\n\x07Profile\x12J\n\x0bsample_type\x18\x01 \x03(\x0b\x32\x35.opentelemetry.proto.profiles.v1development.ValueType\x12\x42\n\x06sample\x18\x02 \x03(\x0b\x32\x32.opentelemetry.proto.profiles.v1development.Sample\x12\x18\n\x10location_indices\x18\x03 \x03(\x05\x12\x12\n\ntime_nanos\x18\x04 \x01(\x03\x12\x16\n\x0e\x64uration_nanos\x18\x05 \x01(\x03\x12J\n\x0bperiod_type\x18\x06 \x01(\x0b\x32\x35.opentelemetry.proto.profiles.v1development.ValueType\x12\x0e\n\x06period\x18\x07 \x01(\x03\x12\x1a\n\x12\x63omment_strindices\x18\x08 \x03(\x05\x12!\n\x19\x64\x65\x66\x61ult_sample_type_index\x18\t \x01(\x05\x12\x12\n\nprofile_id\x18\n \x01(\x0c\x12 \n\x18\x64ropped_attributes_count\x18\x0b \x01(\r\x12\x1f\n\x17original_payload_format\x18\x0c \x01(\t\x12\x18\n\x10original_payload\x18\r \x01(\x0c\x12\x19\n\x11\x61ttribute_indices\x18\x0e \x03(\x05\"F\n\rAttributeUnit\x12\x1e\n\x16\x61ttribute_key_strindex\x18\x01 \x01(\x05\x12\x15\n\runit_strindex\x18\x02 \x01(\x05\")\n\x04Link\x12\x10\n\x08trace_id\x18\x01 \x01(\x0c\x12\x0f\n\x07span_id\x18\x02 \x01(\x0c\"\x9e\x01\n\tValueType\x12\x15\n\rtype_strindex\x18\x01 \x01(\x05\x12\x15\n\runit_strindex\x18\x02 \x01(\x05\x12\x63\n\x17\x61ggregation_temporality\x18\x03 \x01(\x0e\x32\x42.opentelemetry.proto.profiles.v1development.AggregationTemporality\"\xb1\x01\n\x06Sample\x12\x1d\n\x15locations_start_index\x18\x01 \x01(\x05\x12\x18\n\x10locations_length\x18\x02 \x01(\x05\x12\r\n\x05value\x18\x03 \x03(\x03\x12\x19\n\x11\x61ttribute_indices\x18\x04 \x03(\x05\x12\x17\n\nlink_index\x18\x05 \x01(\x05H\x00\x88\x01\x01\x12\x1c\n\x14timestamps_unix_nano\x18\x06 \x03(\x04\x42\r\n\x0b_link_index\"\xe3\x01\n\x07Mapping\x12\x14\n\x0cmemory_start\x18\x01 \x01(\x04\x12\x14\n\x0cmemory_limit\x18\x02 \x01(\x04\x12\x13\n\x0b\x66ile_offset\x18\x03 \x01(\x04\x12\x19\n\x11\x66ilename_strindex\x18\x04 \x01(\x05\x12\x19\n\x11\x61ttribute_indices\x18\x05 \x03(\x05\x12\x15\n\rhas_functions\x18\x06 \x01(\x08\x12\x15\n\rhas_filenames\x18\x07 \x01(\x08\x12\x18\n\x10has_line_numbers\x18\x08 \x01(\x08\x12\x19\n\x11has_inline_frames\x18\t \x01(\x08\"\xb7\x01\n\x08Location\x12\x1a\n\rmapping_index\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\x04\x12>\n\x04line\x18\x03 \x03(\x0b\x32\x30.opentelemetry.proto.profiles.v1development.Line\x12\x11\n\tis_folded\x18\x04 \x01(\x08\x12\x19\n\x11\x61ttribute_indices\x18\x05 \x03(\x05\x42\x10\n\x0e_mapping_index\"<\n\x04Line\x12\x16\n\x0e\x66unction_index\x18\x01 \x01(\x05\x12\x0c\n\x04line\x18\x02 \x01(\x03\x12\x0e\n\x06\x63olumn\x18\x03 \x01(\x03\"n\n\x08\x46unction\x12\x15\n\rname_strindex\x18\x01 \x01(\x05\x12\x1c\n\x14system_name_strindex\x18\x02 \x01(\x05\x12\x19\n\x11\x66ilename_strindex\x18\x03 \x01(\x05\x12\x12\n\nstart_line\x18\x04 \x01(\x03*\x8c\x01\n\x16\x41ggregationTemporality\x12\'\n#AGGREGATION_TEMPORALITY_UNSPECIFIED\x10\x00\x12!\n\x1d\x41GGREGATION_TEMPORALITY_DELTA\x10\x01\x12&\n\"AGGREGATION_TEMPORALITY_CUMULATIVE\x10\x02\x42\xa4\x01\n-io.opentelemetry.proto.profiles.v1developmentB\rProfilesProtoP\x01Z5go.opentelemetry.io/proto/otlp/profiles/v1development\xaa\x02*OpenTelemetry.Proto.Profiles.V1Developmentb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'opentelemetry.proto.profiles.v1development.profiles_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n-io.opentelemetry.proto.profiles.v1developmentB\rProfilesProtoP\001Z5go.opentelemetry.io/proto/otlp/profiles/v1development\252\002*OpenTelemetry.Proto.Profiles.V1Development'
  _globals['_AGGREGATIONTEMPORALITY']._serialized_start=2822
  _globals['_AGGREGATIONTEMPORALITY']._serialized_end=2962
  _globals['_PROFILESDICTIONARY']._serialized_start=198
  _globals['_PROFILESDICTIONARY']._serialized_end=692
  _globals['_PROFILESDATA']._serialized_start=695
  _globals['_PROFILESDATA']._serialized_end=882
  _globals['_RESOURCEPROFILES']._serialized_start=885
  _globals['_RESOURCEPROFILES']._serialized_end=1075
  _globals['_SCOPEPROFILES']._serialized_start=1078
  _globals['_SCOPEPROFILES']._serialized_end=1252
  _globals['_PROFILE']._serialized_start=1255
  _globals['_PROFILE']._serialized_end=1773
  _globals['_ATTRIBUTEUNIT']._serialized_start=1775
  _globals['_ATTRIBUTEUNIT']._serialized_end=1845
  _globals['_LINK']._serialized_start=1847
  _globals['_LINK']._serialized_end=1888
  _globals['_VALUETYPE']._serialized_start=1891
  _globals['_VALUETYPE']._serialized_end=2049
  _globals['_SAMPLE']._serialized_start=2052
  _globals['_SAMPLE']._serialized_end=2229
  _globals['_MAPPING']._serialized_start=2232
  _globals['_MAPPING']._serialized_end=2459
  _globals['_LOCATION']._serialized_start=2462
  _globals['_LOCATION']._serialized_end=2645
  _globals['_LINE']._serialized_start=2647
  _globals['_LINE']._serialized_end=2707
  _globals['_FUNCTION']._serialized_start=2709
  _globals['_FUNCTION']._serialized_end=2819
# @@protoc_insertion_point(module_scope)
