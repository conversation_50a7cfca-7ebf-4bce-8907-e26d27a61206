"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023, OpenTelemetry Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

This file includes work covered by the following copyright and permission notices:

Copyright 2016 Google Inc. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import opentelemetry.proto.common.v1.common_pb2
import opentelemetry.proto.resource.v1.resource_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _AggregationTemporality:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AggregationTemporalityEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AggregationTemporality.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    AGGREGATION_TEMPORALITY_UNSPECIFIED: _AggregationTemporality.ValueType  # 0
    """UNSPECIFIED is the default AggregationTemporality, it MUST not be used."""
    AGGREGATION_TEMPORALITY_DELTA: _AggregationTemporality.ValueType  # 1
    """* DELTA is an AggregationTemporality for a profiler which reports
    changes since last report time. Successive metrics contain aggregation of
    values from continuous and non-overlapping intervals.

    The values for a DELTA metric are based only on the time interval
    associated with one measurement cycle. There is no dependency on
    previous measurements like is the case for CUMULATIVE metrics.

    For example, consider a system measuring the number of requests that
    it receives and reports the sum of these requests every second as a
    DELTA metric:

    1. The system starts receiving at time=t_0.
    2. A request is received, the system measures 1 request.
    3. A request is received, the system measures 1 request.
    4. A request is received, the system measures 1 request.
    5. The 1 second collection cycle ends. A metric is exported for the
    number of requests received over the interval of time t_0 to
    t_0+1 with a value of 3.
    6. A request is received, the system measures 1 request.
    7. A request is received, the system measures 1 request.
    8. The 1 second collection cycle ends. A metric is exported for the
    number of requests received over the interval of time t_0+1 to
    t_0+2 with a value of 2.
    """
    AGGREGATION_TEMPORALITY_CUMULATIVE: _AggregationTemporality.ValueType  # 2
    """* CUMULATIVE is an AggregationTemporality for a profiler which
    reports changes since a fixed start time. This means that current values
    of a CUMULATIVE metric depend on all previous measurements since the
    start time. Because of this, the sender is required to retain this state
    in some form. If this state is lost or invalidated, the CUMULATIVE metric
    values MUST be reset and a new fixed start time following the last
    reported measurement time sent MUST be used.

    For example, consider a system measuring the number of requests that
    it receives and reports the sum of these requests every second as a
    CUMULATIVE metric:

    1. The system starts receiving at time=t_0.
    2. A request is received, the system measures 1 request.
    3. A request is received, the system measures 1 request.
    4. A request is received, the system measures 1 request.
    5. The 1 second collection cycle ends. A metric is exported for the
    number of requests received over the interval of time t_0 to
    t_0+1 with a value of 3.
    6. A request is received, the system measures 1 request.
    7. A request is received, the system measures 1 request.
    8. The 1 second collection cycle ends. A metric is exported for the
    number of requests received over the interval of time t_0 to
    t_0+2 with a value of 5.
    9. The system experiences a fault and loses state.
    10. The system recovers and resumes receiving at time=t_1.
    11. A request is received, the system measures 1 request.
    12. The 1 second collection cycle ends. A metric is exported for the
    number of requests received over the interval of time t_1 to
    t_1+1 with a value of 1.

    Note: Even though, when reporting changes since last report time, using
    CUMULATIVE is valid, it is not recommended.
    """

class AggregationTemporality(_AggregationTemporality, metaclass=_AggregationTemporalityEnumTypeWrapper):
    """Specifies the method of aggregating metric values, either DELTA (change since last report)
    or CUMULATIVE (total since a fixed start time).
    """

AGGREGATION_TEMPORALITY_UNSPECIFIED: AggregationTemporality.ValueType  # 0
"""UNSPECIFIED is the default AggregationTemporality, it MUST not be used."""
AGGREGATION_TEMPORALITY_DELTA: AggregationTemporality.ValueType  # 1
"""* DELTA is an AggregationTemporality for a profiler which reports
changes since last report time. Successive metrics contain aggregation of
values from continuous and non-overlapping intervals.

The values for a DELTA metric are based only on the time interval
associated with one measurement cycle. There is no dependency on
previous measurements like is the case for CUMULATIVE metrics.

For example, consider a system measuring the number of requests that
it receives and reports the sum of these requests every second as a
DELTA metric:

1. The system starts receiving at time=t_0.
2. A request is received, the system measures 1 request.
3. A request is received, the system measures 1 request.
4. A request is received, the system measures 1 request.
5. The 1 second collection cycle ends. A metric is exported for the
number of requests received over the interval of time t_0 to
t_0+1 with a value of 3.
6. A request is received, the system measures 1 request.
7. A request is received, the system measures 1 request.
8. The 1 second collection cycle ends. A metric is exported for the
number of requests received over the interval of time t_0+1 to
t_0+2 with a value of 2.
"""
AGGREGATION_TEMPORALITY_CUMULATIVE: AggregationTemporality.ValueType  # 2
"""* CUMULATIVE is an AggregationTemporality for a profiler which
reports changes since a fixed start time. This means that current values
of a CUMULATIVE metric depend on all previous measurements since the
start time. Because of this, the sender is required to retain this state
in some form. If this state is lost or invalidated, the CUMULATIVE metric
values MUST be reset and a new fixed start time following the last
reported measurement time sent MUST be used.

For example, consider a system measuring the number of requests that
it receives and reports the sum of these requests every second as a
CUMULATIVE metric:

1. The system starts receiving at time=t_0.
2. A request is received, the system measures 1 request.
3. A request is received, the system measures 1 request.
4. A request is received, the system measures 1 request.
5. The 1 second collection cycle ends. A metric is exported for the
number of requests received over the interval of time t_0 to
t_0+1 with a value of 3.
6. A request is received, the system measures 1 request.
7. A request is received, the system measures 1 request.
8. The 1 second collection cycle ends. A metric is exported for the
number of requests received over the interval of time t_0 to
t_0+2 with a value of 5.
9. The system experiences a fault and loses state.
10. The system recovers and resumes receiving at time=t_1.
11. A request is received, the system measures 1 request.
12. The 1 second collection cycle ends. A metric is exported for the
number of requests received over the interval of time t_1 to
t_1+1 with a value of 1.

Note: Even though, when reporting changes since last report time, using
CUMULATIVE is valid, it is not recommended.
"""
global___AggregationTemporality = AggregationTemporality

@typing_extensions.final
class ProfilesDictionary(google.protobuf.message.Message):
    """               Relationships Diagram

    ┌──────────────────┐                      LEGEND
    │   ProfilesData   │ ─────┐
    └──────────────────┘      │           ─────▶ embedded
      │                       │
      │ 1-n                   │           ─────▷ referenced by index
      ▼                       ▼
    ┌──────────────────┐   ┌────────────────────┐
    │ ResourceProfiles │   │ ProfilesDictionary │
    └──────────────────┘   └────────────────────┘
      │
      │ 1-n
      ▼
    ┌──────────────────┐
    │  ScopeProfiles   │
    └──────────────────┘
      │
      │ 1-1
      ▼
    ┌──────────────────┐
    │      Profile     │
    └──────────────────┘
      │                                n-1
      │ 1-n         ┌───────────────────────────────────────┐
      ▼             │                                       ▽
    ┌──────────────────┐   1-n   ┌──────────────┐      ┌──────────┐
    │      Sample      │ ──────▷ │   KeyValue   │      │   Link   │
    └──────────────────┘         └──────────────┘      └──────────┘
      │                    1-n       △      △
      │ 1-n        ┌─────────────────┘      │ 1-n
      ▽            │                        │
    ┌──────────────────┐   n-1   ┌──────────────┐
    │     Location     │ ──────▷ │   Mapping    │
    └──────────────────┘         └──────────────┘
      │
      │ 1-n
      ▼
    ┌──────────────────┐
    │       Line       │
    └──────────────────┘
      │
      │ 1-1
      ▽
    ┌──────────────────┐
    │     Function     │
    └──────────────────┘

    ProfilesDictionary represents the profiles data shared across the
    entire message being sent.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MAPPING_TABLE_FIELD_NUMBER: builtins.int
    LOCATION_TABLE_FIELD_NUMBER: builtins.int
    FUNCTION_TABLE_FIELD_NUMBER: builtins.int
    LINK_TABLE_FIELD_NUMBER: builtins.int
    STRING_TABLE_FIELD_NUMBER: builtins.int
    ATTRIBUTE_TABLE_FIELD_NUMBER: builtins.int
    ATTRIBUTE_UNITS_FIELD_NUMBER: builtins.int
    @property
    def mapping_table(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Mapping]:
        """Mappings from address ranges to the image/binary/library mapped
        into that address range referenced by locations via Location.mapping_index.
        """
    @property
    def location_table(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Location]:
        """Locations referenced by samples via Profile.location_indices."""
    @property
    def function_table(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Function]:
        """Functions referenced by locations via Line.function_index."""
    @property
    def link_table(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Link]:
        """Links referenced by samples via Sample.link_index."""
    @property
    def string_table(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """A common table for strings referenced by various messages.
        string_table[0] must always be "".
        """
    @property
    def attribute_table(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[opentelemetry.proto.common.v1.common_pb2.KeyValue]:
        """A common table for attributes referenced by various messages."""
    @property
    def attribute_units(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___AttributeUnit]:
        """Represents a mapping between Attribute Keys and Units."""
    def __init__(
        self,
        *,
        mapping_table: collections.abc.Iterable[global___Mapping] | None = ...,
        location_table: collections.abc.Iterable[global___Location] | None = ...,
        function_table: collections.abc.Iterable[global___Function] | None = ...,
        link_table: collections.abc.Iterable[global___Link] | None = ...,
        string_table: collections.abc.Iterable[builtins.str] | None = ...,
        attribute_table: collections.abc.Iterable[opentelemetry.proto.common.v1.common_pb2.KeyValue] | None = ...,
        attribute_units: collections.abc.Iterable[global___AttributeUnit] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["attribute_table", b"attribute_table", "attribute_units", b"attribute_units", "function_table", b"function_table", "link_table", b"link_table", "location_table", b"location_table", "mapping_table", b"mapping_table", "string_table", b"string_table"]) -> None: ...

global___ProfilesDictionary = ProfilesDictionary

@typing_extensions.final
class ProfilesData(google.protobuf.message.Message):
    """ProfilesData represents the profiles data that can be stored in persistent storage,
    OR can be embedded by other protocols that transfer OTLP profiles data but do not
    implement the OTLP protocol.

    The main difference between this message and collector protocol is that
    in this message there will not be any "control" or "metadata" specific to
    OTLP protocol.

    When new fields are added into this message, the OTLP request MUST be updated
    as well.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESOURCE_PROFILES_FIELD_NUMBER: builtins.int
    DICTIONARY_FIELD_NUMBER: builtins.int
    @property
    def resource_profiles(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ResourceProfiles]:
        """An array of ResourceProfiles.
        For data coming from an SDK profiler, this array will typically contain one
        element. Host-level profilers will usually create one ResourceProfile per
        container, as well as one additional ResourceProfile grouping all samples
        from non-containerized processes.
        Other resource groupings are possible as well and clarified via
        Resource.attributes and semantic conventions.
        """
    @property
    def dictionary(self) -> global___ProfilesDictionary:
        """One instance of ProfilesDictionary"""
    def __init__(
        self,
        *,
        resource_profiles: collections.abc.Iterable[global___ResourceProfiles] | None = ...,
        dictionary: global___ProfilesDictionary | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["dictionary", b"dictionary"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["dictionary", b"dictionary", "resource_profiles", b"resource_profiles"]) -> None: ...

global___ProfilesData = ProfilesData

@typing_extensions.final
class ResourceProfiles(google.protobuf.message.Message):
    """A collection of ScopeProfiles from a Resource."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESOURCE_FIELD_NUMBER: builtins.int
    SCOPE_PROFILES_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def resource(self) -> opentelemetry.proto.resource.v1.resource_pb2.Resource:
        """The resource for the profiles in this message.
        If this field is not set then no resource info is known.
        """
    @property
    def scope_profiles(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ScopeProfiles]:
        """A list of ScopeProfiles that originate from a resource."""
    schema_url: builtins.str
    """The Schema URL, if known. This is the identifier of the Schema that the resource data
    is recorded in. Notably, the last part of the URL path is the version number of the
    schema: http[s]://server[:port]/path/<version>. To learn more about Schema URL see
    https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
    This schema_url applies to the data in the "resource" field. It does not apply
    to the data in the "scope_profiles" field which have their own schema_url field.
    """
    def __init__(
        self,
        *,
        resource: opentelemetry.proto.resource.v1.resource_pb2.Resource | None = ...,
        scope_profiles: collections.abc.Iterable[global___ScopeProfiles] | None = ...,
        schema_url: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["resource", b"resource"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["resource", b"resource", "schema_url", b"schema_url", "scope_profiles", b"scope_profiles"]) -> None: ...

global___ResourceProfiles = ResourceProfiles

@typing_extensions.final
class ScopeProfiles(google.protobuf.message.Message):
    """A collection of Profiles produced by an InstrumentationScope."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SCOPE_FIELD_NUMBER: builtins.int
    PROFILES_FIELD_NUMBER: builtins.int
    SCHEMA_URL_FIELD_NUMBER: builtins.int
    @property
    def scope(self) -> opentelemetry.proto.common.v1.common_pb2.InstrumentationScope:
        """The instrumentation scope information for the profiles in this message.
        Semantically when InstrumentationScope isn't set, it is equivalent with
        an empty instrumentation scope name (unknown).
        """
    @property
    def profiles(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Profile]:
        """A list of Profiles that originate from an instrumentation scope."""
    schema_url: builtins.str
    """The Schema URL, if known. This is the identifier of the Schema that the profile data
    is recorded in. Notably, the last part of the URL path is the version number of the
    schema: http[s]://server[:port]/path/<version>. To learn more about Schema URL see
    https://opentelemetry.io/docs/specs/otel/schemas/#schema-url
    This schema_url applies to all profiles in the "profiles" field.
    """
    def __init__(
        self,
        *,
        scope: opentelemetry.proto.common.v1.common_pb2.InstrumentationScope | None = ...,
        profiles: collections.abc.Iterable[global___Profile] | None = ...,
        schema_url: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["scope", b"scope"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["profiles", b"profiles", "schema_url", b"schema_url", "scope", b"scope"]) -> None: ...

global___ScopeProfiles = ScopeProfiles

@typing_extensions.final
class Profile(google.protobuf.message.Message):
    """Profile is a common stacktrace profile format.

    Measurements represented with this format should follow the
    following conventions:

    - Consumers should treat unset optional fields as if they had been
      set with their default value.

    - When possible, measurements should be stored in "unsampled" form
      that is most useful to humans.  There should be enough
      information present to determine the original sampled values.

    - On-disk, the serialized proto must be gzip-compressed.

    - The profile is represented as a set of samples, where each sample
      references a sequence of locations, and where each location belongs
      to a mapping.
    - There is a N->1 relationship from sample.location_id entries to
      locations. For every sample.location_id entry there must be a
      unique Location with that index.
    - There is an optional N->1 relationship from locations to
      mappings. For every nonzero Location.mapping_id there must be a
      unique Mapping with that index.

    Represents a complete profile, including sample types, samples,
    mappings to binaries, locations, functions, string table, and additional metadata.
    It modifies and annotates pprof Profile with OpenTelemetry specific fields.

    Note that whilst fields in this message retain the name and field id from pprof in most cases
    for ease of understanding data migration, it is not intended that pprof:Profile and
    OpenTelemetry:Profile encoding be wire compatible.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SAMPLE_TYPE_FIELD_NUMBER: builtins.int
    SAMPLE_FIELD_NUMBER: builtins.int
    LOCATION_INDICES_FIELD_NUMBER: builtins.int
    TIME_NANOS_FIELD_NUMBER: builtins.int
    DURATION_NANOS_FIELD_NUMBER: builtins.int
    PERIOD_TYPE_FIELD_NUMBER: builtins.int
    PERIOD_FIELD_NUMBER: builtins.int
    COMMENT_STRINDICES_FIELD_NUMBER: builtins.int
    DEFAULT_SAMPLE_TYPE_INDEX_FIELD_NUMBER: builtins.int
    PROFILE_ID_FIELD_NUMBER: builtins.int
    DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
    ORIGINAL_PAYLOAD_FORMAT_FIELD_NUMBER: builtins.int
    ORIGINAL_PAYLOAD_FIELD_NUMBER: builtins.int
    ATTRIBUTE_INDICES_FIELD_NUMBER: builtins.int
    @property
    def sample_type(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ValueType]:
        """A description of the samples associated with each Sample.value.
        For a cpu profile this might be:
          [["cpu","nanoseconds"]] or [["wall","seconds"]] or [["syscall","count"]]
        For a heap profile, this might be:
          [["allocations","count"], ["space","bytes"]],
        If one of the values represents the number of events represented
        by the sample, by convention it should be at index 0 and use
        sample_type.unit == "count".
        """
    @property
    def sample(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Sample]:
        """The set of samples recorded in this profile."""
    @property
    def location_indices(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """References to locations in ProfilesDictionary.location_table."""
    time_nanos: builtins.int
    """The following fields 4-14 are informational, do not affect
    interpretation of results.

    Time of collection (UTC) represented as nanoseconds past the epoch.
    """
    duration_nanos: builtins.int
    """Duration of the profile, if a duration makes sense."""
    @property
    def period_type(self) -> global___ValueType:
        """The kind of events between sampled occurrences.
        e.g [ "cpu","cycles" ] or [ "heap","bytes" ]
        """
    period: builtins.int
    """The number of events between sampled occurrences."""
    @property
    def comment_strindices(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Free-form text associated with the profile. The text is displayed as is
        to the user by the tools that read profiles (e.g. by pprof). This field
        should not be used to store any machine-readable information, it is only
        for human-friendly content. The profile must stay functional if this field
        is cleaned.
        Indices into ProfilesDictionary.string_table.
        """
    default_sample_type_index: builtins.int
    """Index into the sample_type array to the default sample type."""
    profile_id: builtins.bytes
    """A globally unique identifier for a profile. The ID is a 16-byte array. An ID with
    all zeroes is considered invalid.

    This field is required.
    """
    dropped_attributes_count: builtins.int
    """dropped_attributes_count is the number of attributes that were discarded. Attributes
    can be discarded because their keys are too long or because there are too many
    attributes. If this value is 0, then no attributes were dropped.
    """
    original_payload_format: builtins.str
    """Specifies format of the original payload. Common values are defined in semantic conventions. [required if original_payload is present]"""
    original_payload: builtins.bytes
    """Original payload can be stored in this field. This can be useful for users who want to get the original payload.
    Formats such as JFR are highly extensible and can contain more information than what is defined in this spec.
    Inclusion of original payload should be configurable by the user. Default behavior should be to not include the original payload.
    If the original payload is in pprof format, it SHOULD not be included in this field.
    The field is optional, however if it is present then equivalent converted data should be populated in other fields
    of this message as far as is practicable.
    """
    @property
    def attribute_indices(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """References to attributes in attribute_table. [optional]
        It is a collection of key/value pairs. Note, global attributes
        like server name can be set using the resource API. Examples of attributes:

            "/http/user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36"
            "/http/server_latency": 300
            "abc.com/myattribute": true
            "abc.com/score": 10.239

        The OpenTelemetry API specification further restricts the allowed value types:
        https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/common/README.md#attribute
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
    def __init__(
        self,
        *,
        sample_type: collections.abc.Iterable[global___ValueType] | None = ...,
        sample: collections.abc.Iterable[global___Sample] | None = ...,
        location_indices: collections.abc.Iterable[builtins.int] | None = ...,
        time_nanos: builtins.int = ...,
        duration_nanos: builtins.int = ...,
        period_type: global___ValueType | None = ...,
        period: builtins.int = ...,
        comment_strindices: collections.abc.Iterable[builtins.int] | None = ...,
        default_sample_type_index: builtins.int = ...,
        profile_id: builtins.bytes = ...,
        dropped_attributes_count: builtins.int = ...,
        original_payload_format: builtins.str = ...,
        original_payload: builtins.bytes = ...,
        attribute_indices: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["period_type", b"period_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["attribute_indices", b"attribute_indices", "comment_strindices", b"comment_strindices", "default_sample_type_index", b"default_sample_type_index", "dropped_attributes_count", b"dropped_attributes_count", "duration_nanos", b"duration_nanos", "location_indices", b"location_indices", "original_payload", b"original_payload", "original_payload_format", b"original_payload_format", "period", b"period", "period_type", b"period_type", "profile_id", b"profile_id", "sample", b"sample", "sample_type", b"sample_type", "time_nanos", b"time_nanos"]) -> None: ...

global___Profile = Profile

@typing_extensions.final
class AttributeUnit(google.protobuf.message.Message):
    """Represents a mapping between Attribute Keys and Units."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ATTRIBUTE_KEY_STRINDEX_FIELD_NUMBER: builtins.int
    UNIT_STRINDEX_FIELD_NUMBER: builtins.int
    attribute_key_strindex: builtins.int
    """Index into string table."""
    unit_strindex: builtins.int
    """Index into string table."""
    def __init__(
        self,
        *,
        attribute_key_strindex: builtins.int = ...,
        unit_strindex: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["attribute_key_strindex", b"attribute_key_strindex", "unit_strindex", b"unit_strindex"]) -> None: ...

global___AttributeUnit = AttributeUnit

@typing_extensions.final
class Link(google.protobuf.message.Message):
    """A pointer from a profile Sample to a trace Span.
    Connects a profile sample to a trace span, identified by unique trace and span IDs.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRACE_ID_FIELD_NUMBER: builtins.int
    SPAN_ID_FIELD_NUMBER: builtins.int
    trace_id: builtins.bytes
    """A unique identifier of a trace that this linked span is part of. The ID is a
    16-byte array.
    """
    span_id: builtins.bytes
    """A unique identifier for the linked span. The ID is an 8-byte array."""
    def __init__(
        self,
        *,
        trace_id: builtins.bytes = ...,
        span_id: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["span_id", b"span_id", "trace_id", b"trace_id"]) -> None: ...

global___Link = Link

@typing_extensions.final
class ValueType(google.protobuf.message.Message):
    """ValueType describes the type and units of a value, with an optional aggregation temporality."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_STRINDEX_FIELD_NUMBER: builtins.int
    UNIT_STRINDEX_FIELD_NUMBER: builtins.int
    AGGREGATION_TEMPORALITY_FIELD_NUMBER: builtins.int
    type_strindex: builtins.int
    """Index into ProfilesDictionary.string_table."""
    unit_strindex: builtins.int
    """Index into ProfilesDictionary.string_table."""
    aggregation_temporality: global___AggregationTemporality.ValueType
    def __init__(
        self,
        *,
        type_strindex: builtins.int = ...,
        unit_strindex: builtins.int = ...,
        aggregation_temporality: global___AggregationTemporality.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["aggregation_temporality", b"aggregation_temporality", "type_strindex", b"type_strindex", "unit_strindex", b"unit_strindex"]) -> None: ...

global___ValueType = ValueType

@typing_extensions.final
class Sample(google.protobuf.message.Message):
    """Each Sample records values encountered in some program
    context. The program context is typically a stack trace, perhaps
    augmented with auxiliary information like the thread-id, some
    indicator of a higher level request being handled etc.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCATIONS_START_INDEX_FIELD_NUMBER: builtins.int
    LOCATIONS_LENGTH_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    ATTRIBUTE_INDICES_FIELD_NUMBER: builtins.int
    LINK_INDEX_FIELD_NUMBER: builtins.int
    TIMESTAMPS_UNIX_NANO_FIELD_NUMBER: builtins.int
    locations_start_index: builtins.int
    """locations_start_index along with locations_length refers to to a slice of locations in Profile.location_indices."""
    locations_length: builtins.int
    """locations_length along with locations_start_index refers to a slice of locations in Profile.location_indices.
    Supersedes location_index.
    """
    @property
    def value(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """The type and unit of each value is defined by the corresponding
        entry in Profile.sample_type. All samples must have the same
        number of values, the same as the length of Profile.sample_type.
        When aggregating multiple samples into a single sample, the
        result has a list of values that is the element-wise sum of the
        lists of the originals.
        """
    @property
    def attribute_indices(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """References to attributes in ProfilesDictionary.attribute_table. [optional]"""
    link_index: builtins.int
    """Reference to link in ProfilesDictionary.link_table. [optional]"""
    @property
    def timestamps_unix_nano(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Timestamps associated with Sample represented in nanoseconds. These timestamps are expected
        to fall within the Profile's time range. [optional]
        """
    def __init__(
        self,
        *,
        locations_start_index: builtins.int = ...,
        locations_length: builtins.int = ...,
        value: collections.abc.Iterable[builtins.int] | None = ...,
        attribute_indices: collections.abc.Iterable[builtins.int] | None = ...,
        link_index: builtins.int | None = ...,
        timestamps_unix_nano: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_link_index", b"_link_index", "link_index", b"link_index"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_link_index", b"_link_index", "attribute_indices", b"attribute_indices", "link_index", b"link_index", "locations_length", b"locations_length", "locations_start_index", b"locations_start_index", "timestamps_unix_nano", b"timestamps_unix_nano", "value", b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_link_index", b"_link_index"]) -> typing_extensions.Literal["link_index"] | None: ...

global___Sample = Sample

@typing_extensions.final
class Mapping(google.protobuf.message.Message):
    """Describes the mapping of a binary in memory, including its address range,
    file offset, and metadata like build ID
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MEMORY_START_FIELD_NUMBER: builtins.int
    MEMORY_LIMIT_FIELD_NUMBER: builtins.int
    FILE_OFFSET_FIELD_NUMBER: builtins.int
    FILENAME_STRINDEX_FIELD_NUMBER: builtins.int
    ATTRIBUTE_INDICES_FIELD_NUMBER: builtins.int
    HAS_FUNCTIONS_FIELD_NUMBER: builtins.int
    HAS_FILENAMES_FIELD_NUMBER: builtins.int
    HAS_LINE_NUMBERS_FIELD_NUMBER: builtins.int
    HAS_INLINE_FRAMES_FIELD_NUMBER: builtins.int
    memory_start: builtins.int
    """Address at which the binary (or DLL) is loaded into memory."""
    memory_limit: builtins.int
    """The limit of the address range occupied by this mapping."""
    file_offset: builtins.int
    """Offset in the binary that corresponds to the first mapped address."""
    filename_strindex: builtins.int
    """The object this entry is loaded from.  This can be a filename on
    disk for the main binary and shared libraries, or virtual
    abstractions like "[vdso]".
    Index into ProfilesDictionary.string_table.
    """
    @property
    def attribute_indices(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """References to attributes in ProfilesDictionary.attribute_table. [optional]"""
    has_functions: builtins.bool
    """The following fields indicate the resolution of symbolic info."""
    has_filenames: builtins.bool
    has_line_numbers: builtins.bool
    has_inline_frames: builtins.bool
    def __init__(
        self,
        *,
        memory_start: builtins.int = ...,
        memory_limit: builtins.int = ...,
        file_offset: builtins.int = ...,
        filename_strindex: builtins.int = ...,
        attribute_indices: collections.abc.Iterable[builtins.int] | None = ...,
        has_functions: builtins.bool = ...,
        has_filenames: builtins.bool = ...,
        has_line_numbers: builtins.bool = ...,
        has_inline_frames: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["attribute_indices", b"attribute_indices", "file_offset", b"file_offset", "filename_strindex", b"filename_strindex", "has_filenames", b"has_filenames", "has_functions", b"has_functions", "has_inline_frames", b"has_inline_frames", "has_line_numbers", b"has_line_numbers", "memory_limit", b"memory_limit", "memory_start", b"memory_start"]) -> None: ...

global___Mapping = Mapping

@typing_extensions.final
class Location(google.protobuf.message.Message):
    """Describes function and line table debug information."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MAPPING_INDEX_FIELD_NUMBER: builtins.int
    ADDRESS_FIELD_NUMBER: builtins.int
    LINE_FIELD_NUMBER: builtins.int
    IS_FOLDED_FIELD_NUMBER: builtins.int
    ATTRIBUTE_INDICES_FIELD_NUMBER: builtins.int
    mapping_index: builtins.int
    """Reference to mapping in ProfilesDictionary.mapping_table.
    It can be unset if the mapping is unknown or not applicable for
    this profile type.
    """
    address: builtins.int
    """The instruction address for this location, if available.  It
    should be within [Mapping.memory_start...Mapping.memory_limit]
    for the corresponding mapping. A non-leaf address may be in the
    middle of a call instruction. It is up to display tools to find
    the beginning of the instruction if necessary.
    """
    @property
    def line(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Line]:
        """Multiple line indicates this location has inlined functions,
        where the last entry represents the caller into which the
        preceding entries were inlined.

        E.g., if memcpy() is inlined into printf:
           line[0].function_name == "memcpy"
           line[1].function_name == "printf"
        """
    is_folded: builtins.bool
    """Provides an indication that multiple symbols map to this location's
    address, for example due to identical code folding by the linker. In that
    case the line information above represents one of the multiple
    symbols. This field must be recomputed when the symbolization state of the
    profile changes.
    """
    @property
    def attribute_indices(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """References to attributes in ProfilesDictionary.attribute_table. [optional]"""
    def __init__(
        self,
        *,
        mapping_index: builtins.int | None = ...,
        address: builtins.int = ...,
        line: collections.abc.Iterable[global___Line] | None = ...,
        is_folded: builtins.bool = ...,
        attribute_indices: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_mapping_index", b"_mapping_index", "mapping_index", b"mapping_index"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_mapping_index", b"_mapping_index", "address", b"address", "attribute_indices", b"attribute_indices", "is_folded", b"is_folded", "line", b"line", "mapping_index", b"mapping_index"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_mapping_index", b"_mapping_index"]) -> typing_extensions.Literal["mapping_index"] | None: ...

global___Location = Location

@typing_extensions.final
class Line(google.protobuf.message.Message):
    """Details a specific line in a source code, linked to a function."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_INDEX_FIELD_NUMBER: builtins.int
    LINE_FIELD_NUMBER: builtins.int
    COLUMN_FIELD_NUMBER: builtins.int
    function_index: builtins.int
    """Reference to function in ProfilesDictionary.function_table."""
    line: builtins.int
    """Line number in source code. 0 means unset."""
    column: builtins.int
    """Column number in source code. 0 means unset."""
    def __init__(
        self,
        *,
        function_index: builtins.int = ...,
        line: builtins.int = ...,
        column: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["column", b"column", "function_index", b"function_index", "line", b"line"]) -> None: ...

global___Line = Line

@typing_extensions.final
class Function(google.protobuf.message.Message):
    """Describes a function, including its human-readable name, system name,
    source file, and starting line number in the source.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_STRINDEX_FIELD_NUMBER: builtins.int
    SYSTEM_NAME_STRINDEX_FIELD_NUMBER: builtins.int
    FILENAME_STRINDEX_FIELD_NUMBER: builtins.int
    START_LINE_FIELD_NUMBER: builtins.int
    name_strindex: builtins.int
    """Function name. Empty string if not available."""
    system_name_strindex: builtins.int
    """Function name, as identified by the system. For instance,
    it can be a C++ mangled name. Empty string if not available.
    """
    filename_strindex: builtins.int
    """Source file containing the function. Empty string if not available."""
    start_line: builtins.int
    """Line number in source file. 0 means unset."""
    def __init__(
        self,
        *,
        name_strindex: builtins.int = ...,
        system_name_strindex: builtins.int = ...,
        filename_strindex: builtins.int = ...,
        start_line: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["filename_strindex", b"filename_strindex", "name_strindex", b"name_strindex", "start_line", b"start_line", "system_name_strindex", b"system_name_strindex"]) -> None: ...

global___Function = Function
