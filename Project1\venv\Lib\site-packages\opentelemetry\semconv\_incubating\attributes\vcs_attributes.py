# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from enum import Enum
from typing import Final

from typing_extensions import deprecated

VCS_CHANGE_ID: Final = "vcs.change.id"
"""
The ID of the change (pull request/merge request/changelist) if applicable. This is usually a unique (within repository) identifier generated by the VCS system.
"""

VCS_CHANGE_STATE: Final = "vcs.change.state"
"""
The state of the change (pull request/merge request/changelist).
"""

VCS_CHANGE_TITLE: Final = "vcs.change.title"
"""
The human readable title of the change (pull request/merge request/changelist). This title is often a brief summary of the change and may get merged in to a ref as the commit summary.
"""

VCS_LINE_CHANGE_TYPE: Final = "vcs.line_change.type"
"""
The type of line change being measured on a branch or change.
"""

VCS_OWNER_NAME: Final = "vcs.owner.name"
"""
The group owner within the version control system.
"""

VCS_PROVIDER_NAME: Final = "vcs.provider.name"
"""
The name of the version control system provider.
"""

VCS_REF_BASE_NAME: Final = "vcs.ref.base.name"
"""
The name of the [reference](https://git-scm.com/docs/gitglossary#def_ref) such as **branch** or **tag** in the repository.
Note: `base` refers to the starting point of a change. For example, `main`
would be the base reference of type branch if you've created a new
reference of type branch from it and created new commits.
"""

VCS_REF_BASE_REVISION: Final = "vcs.ref.base.revision"
"""
The revision, literally [revised version](https://www.merriam-webster.com/dictionary/revision), The revision most often refers to a commit object in Git, or a revision number in SVN.
Note: `base` refers to the starting point of a change. For example, `main`
would be the base reference of type branch if you've created a new
reference of type branch from it and created new commits. The
revision can be a full [hash value (see
glossary)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-5.pdf),
of the recorded change to a ref within a repository pointing to a
commit [commit](https://git-scm.com/docs/git-commit) object. It does
not necessarily have to be a hash; it can simply define a [revision
number](https://svnbook.red-bean.com/en/1.7/svn.tour.revs.specifiers.html)
which is an integer that is monotonically increasing. In cases where
it is identical to the `ref.base.name`, it SHOULD still be included.
It is up to the implementer to decide which value to set as the
revision based on the VCS system and situational context.
"""

VCS_REF_BASE_TYPE: Final = "vcs.ref.base.type"
"""
The type of the [reference](https://git-scm.com/docs/gitglossary#def_ref) in the repository.
Note: `base` refers to the starting point of a change. For example, `main`
would be the base reference of type branch if you've created a new
reference of type branch from it and created new commits.
"""

VCS_REF_HEAD_NAME: Final = "vcs.ref.head.name"
"""
The name of the [reference](https://git-scm.com/docs/gitglossary#def_ref) such as **branch** or **tag** in the repository.
Note: `head` refers to where you are right now; the current reference at a
given time.
"""

VCS_REF_HEAD_REVISION: Final = "vcs.ref.head.revision"
"""
The revision, literally [revised version](https://www.merriam-webster.com/dictionary/revision), The revision most often refers to a commit object in Git, or a revision number in SVN.
Note: `head` refers to where you are right now; the current reference at a
given time.The revision can be a full [hash value (see
glossary)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-5.pdf),
of the recorded change to a ref within a repository pointing to a
commit [commit](https://git-scm.com/docs/git-commit) object. It does
not necessarily have to be a hash; it can simply define a [revision
number](https://svnbook.red-bean.com/en/1.7/svn.tour.revs.specifiers.html)
which is an integer that is monotonically increasing. In cases where
it is identical to the `ref.head.name`, it SHOULD still be included.
It is up to the implementer to decide which value to set as the
revision based on the VCS system and situational context.
"""

VCS_REF_HEAD_TYPE: Final = "vcs.ref.head.type"
"""
The type of the [reference](https://git-scm.com/docs/gitglossary#def_ref) in the repository.
Note: `head` refers to where you are right now; the current reference at a
given time.
"""

VCS_REF_TYPE: Final = "vcs.ref.type"
"""
The type of the [reference](https://git-scm.com/docs/gitglossary#def_ref) in the repository.
"""

VCS_REPOSITORY_CHANGE_ID: Final = "vcs.repository.change.id"
"""
Deprecated: Replaced by `vcs.change.id`.
"""

VCS_REPOSITORY_CHANGE_TITLE: Final = "vcs.repository.change.title"
"""
Deprecated: Replaced by `vcs.change.title`.
"""

VCS_REPOSITORY_NAME: Final = "vcs.repository.name"
"""
The human readable name of the repository. It SHOULD NOT include any additional identifier like Group/SubGroup in GitLab or organization in GitHub.
Note: Due to it only being the name, it can clash with forks of the same
repository if collecting telemetry across multiple orgs or groups in
the same backends.
"""

VCS_REPOSITORY_REF_NAME: Final = "vcs.repository.ref.name"
"""
Deprecated: Replaced by `vcs.ref.head.name`.
"""

VCS_REPOSITORY_REF_REVISION: Final = "vcs.repository.ref.revision"
"""
Deprecated: Replaced by `vcs.ref.head.revision`.
"""

VCS_REPOSITORY_REF_TYPE: Final = "vcs.repository.ref.type"
"""
Deprecated: Replaced by `vcs.ref.head.type`.
"""

VCS_REPOSITORY_URL_FULL: Final = "vcs.repository.url.full"
"""
The [canonical URL](https://support.google.com/webmasters/answer/10347851?hl=en#:~:text=A%20canonical%20URL%20is%20the,Google%20chooses%20one%20as%20canonical.) of the repository providing the complete HTTP(S) address in order to locate and identify the repository through a browser.
Note: In Git Version Control Systems, the canonical URL SHOULD NOT include
the `.git` extension.
"""

VCS_REVISION_DELTA_DIRECTION: Final = "vcs.revision_delta.direction"
"""
The type of revision comparison.
"""


class VcsChangeStateValues(Enum):
    OPEN = "open"
    """Open means the change is currently active and under review. It hasn't been merged into the target branch yet, and it's still possible to make changes or add comments."""
    WIP = "wip"
    """WIP (work-in-progress, draft) means the change is still in progress and not yet ready for a full review. It might still undergo significant changes."""
    CLOSED = "closed"
    """Closed means the merge request has been closed without merging. This can happen for various reasons, such as the changes being deemed unnecessary, the issue being resolved in another way, or the author deciding to withdraw the request."""
    MERGED = "merged"
    """Merged indicates that the change has been successfully integrated into the target codebase."""


class VcsLineChangeTypeValues(Enum):
    ADDED = "added"
    """How many lines were added."""
    REMOVED = "removed"
    """How many lines were removed."""


class VcsProviderNameValues(Enum):
    GITHUB = "github"
    """[GitHub](https://github.com)."""
    GITLAB = "gitlab"
    """[GitLab](https://gitlab.com)."""
    GITTEA = "gittea"
    """Deprecated: Replaced by `gitea`."""
    GITEA = "gitea"
    """[Gitea](https://gitea.io)."""
    BITBUCKET = "bitbucket"
    """[Bitbucket](https://bitbucket.org)."""


class VcsRefBaseTypeValues(Enum):
    BRANCH = "branch"
    """[branch](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddefbranchabranch)."""
    TAG = "tag"
    """[tag](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddeftagatag)."""


class VcsRefHeadTypeValues(Enum):
    BRANCH = "branch"
    """[branch](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddefbranchabranch)."""
    TAG = "tag"
    """[tag](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddeftagatag)."""


class VcsRefTypeValues(Enum):
    BRANCH = "branch"
    """[branch](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddefbranchabranch)."""
    TAG = "tag"
    """[tag](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddeftagatag)."""


@deprecated(
    "The attribute vcs.repository.ref.type is deprecated - Replaced by `vcs.ref.head.type`"
)
class VcsRepositoryRefTypeValues(Enum):
    BRANCH = "branch"
    """[branch](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddefbranchabranch)."""
    TAG = "tag"
    """[tag](https://git-scm.com/docs/gitglossary#Documentation/gitglossary.txt-aiddeftagatag)."""


class VcsRevisionDeltaDirectionValues(Enum):
    BEHIND = "behind"
    """How many revisions the change is behind the target ref."""
    AHEAD = "ahead"
    """How many revisions the change is ahead of the target ref."""
