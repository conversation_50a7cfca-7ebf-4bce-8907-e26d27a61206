import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._cumulative import Cumulative
    from ._error_x import <PERSON><PERSON>r<PERSON>
    from ._error_y import <PERSON><PERSON>r<PERSON>
    from ._hoverlabel import Hoverlabel
    from ._insidetextfont import Insidetextfont
    from ._legendgrouptitle import Legendgrouptitle
    from ._marker import Marker
    from ._outsidetextfont import Outsidetextfont
    from ._selected import Selected
    from ._stream import Stream
    from ._textfont import Textfont
    from ._unselected import Unselected
    from ._xbins import XBins
    from ._ybins import YBins
    from . import hoverlabel
    from . import legendgrouptitle
    from . import marker
    from . import selected
    from . import unselected
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [".hoverlabel", ".legendgrouptitle", ".marker", ".selected", ".unselected"],
        [
            "._cumulative.Cumulative",
            "._error_x.ErrorX",
            "._error_y.ErrorY",
            "._hoverlabel.Hoverlabel",
            "._insidetextfont.Insidetextfont",
            "._legendgrouptitle.Legendgrouptitle",
            "._marker.Marker",
            "._outsidetextfont.Outsidetextfont",
            "._selected.Selected",
            "._stream.Stream",
            "._textfont.Textfont",
            "._unselected.Unselected",
            "._xbins.XBins",
            "._ybins.YBins",
        ],
    )
