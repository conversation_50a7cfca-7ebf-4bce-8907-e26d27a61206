<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>API exported by pygame.event &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="API exported by pygame._freetype" href="freetype.html" />
    <link rel="prev" title="API exported by pygame.display" href="display.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="../ref/color.html">Color</a> | 
	    <a href="../ref/display.html">display</a> | 
	    <a href="../ref/draw.html">draw</a> | 
	    <a href="../ref/event.html">event</a> | 
	    <a href="../ref/font.html">font</a> | 
	    <a href="../ref/image.html">image</a> | 
	    <a href="../ref/key.html">key</a> | 
	    <a href="../ref/locals.html">locals</a> | 
	    <a href="../ref/mixer.html">mixer</a> | 
	    <a href="../ref/mouse.html">mouse</a> | 
	    <a href="../ref/rect.html">Rect</a> | 
	    <a href="../ref/surface.html">Surface</a> | 
	    <a href="../ref/time.html">time</a> | 
	    <a href="../ref/music.html">music</a> | 
	    <a href="../ref/pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="../ref/cursors.html">cursors</a> | 
	    <a href="../ref/joystick.html">joystick</a> | 
	    <a href="../ref/mask.html">mask</a> | 
	    <a href="../ref/sprite.html">sprite</a> | 
	    <a href="../ref/transform.html">transform</a> | 
	    <a href="../ref/bufferproxy.html">BufferProxy</a> | 
	    <a href="../ref/freetype.html">freetype</a> | 
	    <a href="../ref/gfxdraw.html">gfxdraw</a> | 
	    <a href="../ref/midi.html">midi</a> | 
	    <a href="../ref/pixelarray.html">PixelArray</a> | 
	    <a href="../ref/pixelcopy.html">pixelcopy</a> | 
	    <a href="../ref/sndarray.html">sndarray</a> | 
	    <a href="../ref/surfarray.html">surfarray</a> | 
	    <a href="../ref/math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="../ref/camera.html">camera</a> | 
	    <a href="../ref/sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="../ref/examples.html">examples</a> | 
	    <a href="../ref/fastevent.html">fastevent</a> | 
	    <a href="../ref/scrap.html">scrap</a> | 
	    <a href="../ref/tests.html">tests</a> | 
	    <a href="../ref/touch.html">touch</a> | 
	    <a href="../ref/pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="api-exported-by-pygame-event">
<section id="src-c-event-c">
<h2>src_c/event.c<a class="headerlink" href="#src-c-event-c" title="Link to this heading">¶</a></h2>
<p>The extension module <a class="tooltip reference internal" href="../ref/event.html#module-pygame.event" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.event</span></code><span class="tooltip-content">pygame module for interacting with events and queues</span></a>.</p>
<p>Header file: src_c/include/pygame.h</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.pgEventObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgEventObject</span></span></span><a class="headerlink" href="#c.pgEventObject" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.event.EventType</span></code> object C struct.</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.pgEventObject.type">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">type</span></span></span><a class="headerlink" href="#c.pgEventObject.type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The event type code.</p>
</dd></dl>

</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.pgEvent_Type">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgEvent_Type</span></span></span><a class="headerlink" href="#c.pgEvent_Type" title="Link to this definition">¶</a><br /></dt>
<dd><p>The pygame event object type <code class="xref py py-class docutils literal notranslate"><span class="pre">pygame.event.EventType</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgEvent_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgEvent_Check</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">x</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgEvent_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>x</em> is a pygame event instance</p>
<p>Will return false if <em>x</em> is a subclass of event.
This is a macro. No check is made that <em>x</em> is not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgEvent_New">
<span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgEvent_New</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">SDL_Event</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">event</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgEvent_New" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return a new pygame event instance for the SDL <em>event</em>.
If <em>event</em> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code> then create an empty event object.
On failure raise a Python exception and return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgEvent_New2">
<span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pgEvent_New2</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">type</span></span>, <span class="n"><span class="pre">PyObject</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">dict</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgEvent_New2" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return a new pygame event instance of SDL <em>type</em> and with
attribute dictionary <em>dict</em>.
If <cite>dict</cite> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code> an empty attribute dictionary is created.
On failure raise a Python exception and return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.pgEvent_FillUserEvent">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pgEvent_FillUserEvent</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.pgEventObject" title="pgEventObject"><span class="n"><span class="pre">pgEventObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">e</span></span>, <span class="n"><span class="pre">SDL_Event</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">event</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.pgEvent_FillUserEvent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Fill SDL event <em>event</em> with information from pygame user event instance <em>e</em>.
Return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, <code class="docutils literal notranslate"><span class="pre">-1</span></code> otherwise.</p>
</dd></dl>

</section>
</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/c_api\event.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="freetype.html" title="API exported by pygame._freetype"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="display.html" title="API exported by pygame.display"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../c_api.html" accesskey="U">pygame C API</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API exported by pygame.event</a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>