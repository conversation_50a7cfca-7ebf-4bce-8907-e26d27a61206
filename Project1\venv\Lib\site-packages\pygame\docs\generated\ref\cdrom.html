<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.cdrom &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.Color" href="color.html" />
    <link rel="prev" title="pygame.camera" href="camera.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame.cdrom">
<span id="pygame-cdrom"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame.cdrom</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">pygame module for audio cdrom control</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.init">pygame.cdrom.init</a></div>
</td>
<td>—</td>
<td>initialize the cdrom module</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.quit">pygame.cdrom.quit</a></div>
</td>
<td>—</td>
<td>uninitialize the cdrom module</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.get_init">pygame.cdrom.get_init</a></div>
</td>
<td>—</td>
<td>true if the cdrom module is initialized</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.get_count">pygame.cdrom.get_count</a></div>
</td>
<td>—</td>
<td>number of cd drives on the system</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD">pygame.cdrom.CD</a></div>
</td>
<td>—</td>
<td>class to manage a cdrom drive</td>
</tr>
</tbody>
</table>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This module is non functional in pygame 2.0 and above, unless you have manually compiled pygame with SDL1.
This module will not be supported in the future.
One alternative for python cdrom functionality is <a class="reference external" href="https://pypi.org/project/pycdio/">pycdio</a>.</p>
</div>
<p>The cdrom module manages the <code class="docutils literal notranslate"><span class="pre">CD</span></code> and <code class="docutils literal notranslate"><span class="pre">DVD</span></code> drives on a computer. It can
also control the playback of audio CDs. This module needs to be initialized
before it can do anything. Each <code class="docutils literal notranslate"><span class="pre">CD</span></code> object you create represents a cdrom
drive and must also be initialized individually before it can do most things.</p>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.cdrom.init">
<span class="sig-prename descclassname"><span class="pre">pygame.cdrom.</span></span><span class="sig-name descname"><span class="pre">init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">initialize the cdrom module</span></div>
<div class="line"><span class="signature">init() -&gt; None</span></div>
</div>
<p>Initialize the cdrom module. This will scan the system for all <code class="docutils literal notranslate"><span class="pre">CD</span></code>
devices. The module must be initialized before any other functions will
work. This automatically happens when you call <code class="docutils literal notranslate"><span class="pre">pygame.init()</span></code>.</p>
<p>It is safe to call this function more than once.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.cdrom.quit">
<span class="sig-prename descclassname"><span class="pre">pygame.cdrom.</span></span><span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.quit" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">uninitialize the cdrom module</span></div>
<div class="line"><span class="signature">quit() -&gt; None</span></div>
</div>
<p>Uninitialize the cdrom module. After you call this any existing <code class="docutils literal notranslate"><span class="pre">CD</span></code>
objects will no longer work.</p>
<p>It is safe to call this function more than once.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.cdrom.get_init">
<span class="sig-prename descclassname"><span class="pre">pygame.cdrom.</span></span><span class="sig-name descname"><span class="pre">get_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.get_init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">true if the cdrom module is initialized</span></div>
<div class="line"><span class="signature">get_init() -&gt; bool</span></div>
</div>
<p>Test if the cdrom module is initialized or not. This is different than the
<code class="docutils literal notranslate"><span class="pre">CD.init()</span></code> since each drive must also be initialized individually.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.cdrom.get_count">
<span class="sig-prename descclassname"><span class="pre">pygame.cdrom.</span></span><span class="sig-name descname"><span class="pre">get_count</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.get_count" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">number of cd drives on the system</span></div>
<div class="line"><span class="signature">get_count() -&gt; count</span></div>
</div>
<p>Return the number of cd drives on the system. When you create <code class="docutils literal notranslate"><span class="pre">CD</span></code> objects
you need to pass an integer id that must be lower than this count. The count
will be 0 if there are no drives on the system.</p>
</dd></dl>

<dl class="py class definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD">
<span class="sig-prename descclassname"><span class="pre">pygame.cdrom.</span></span><span class="sig-name descname"><span class="pre">CD</span></span><a class="headerlink" href="#pygame.cdrom.CD" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">class to manage a cdrom drive</span></div>
<div class="line"><span class="signature">CD(id) -&gt; CD</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.init">pygame.cdrom.CD.init</a></div>
</td>
<td>—</td>
<td>initialize a cdrom drive for use</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.quit">pygame.cdrom.CD.quit</a></div>
</td>
<td>—</td>
<td>uninitialize a cdrom drive for use</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_init">pygame.cdrom.CD.get_init</a></div>
</td>
<td>—</td>
<td>true if this cd device initialized</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.play">pygame.cdrom.CD.play</a></div>
</td>
<td>—</td>
<td>start playing audio</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.stop">pygame.cdrom.CD.stop</a></div>
</td>
<td>—</td>
<td>stop audio playback</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.pause">pygame.cdrom.CD.pause</a></div>
</td>
<td>—</td>
<td>temporarily stop audio playback</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.resume">pygame.cdrom.CD.resume</a></div>
</td>
<td>—</td>
<td>unpause audio playback</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.eject">pygame.cdrom.CD.eject</a></div>
</td>
<td>—</td>
<td>eject or open the cdrom drive</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_id">pygame.cdrom.CD.get_id</a></div>
</td>
<td>—</td>
<td>the index of the cdrom drive</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_name">pygame.cdrom.CD.get_name</a></div>
</td>
<td>—</td>
<td>the system name of the cdrom drive</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_busy">pygame.cdrom.CD.get_busy</a></div>
</td>
<td>—</td>
<td>true if the drive is playing audio</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_paused">pygame.cdrom.CD.get_paused</a></div>
</td>
<td>—</td>
<td>true if the drive is paused</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_current">pygame.cdrom.CD.get_current</a></div>
</td>
<td>—</td>
<td>the current audio playback position</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_empty">pygame.cdrom.CD.get_empty</a></div>
</td>
<td>—</td>
<td>False if a cdrom is in the drive</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_numtracks">pygame.cdrom.CD.get_numtracks</a></div>
</td>
<td>—</td>
<td>the number of tracks on the cdrom</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_track_audio">pygame.cdrom.CD.get_track_audio</a></div>
</td>
<td>—</td>
<td>true if the cdrom track has audio data</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_all">pygame.cdrom.CD.get_all</a></div>
</td>
<td>—</td>
<td>get all track information</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_track_start">pygame.cdrom.CD.get_track_start</a></div>
</td>
<td>—</td>
<td>start time of a cdrom track</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="cdrom.html#pygame.cdrom.CD.get_track_length">pygame.cdrom.CD.get_track_length</a></div>
</td>
<td>—</td>
<td>length of a cdrom track</td>
</tr>
</tbody>
</table>
<p>You can create a <code class="docutils literal notranslate"><span class="pre">CD</span></code> object for each cdrom on the system. Use
<code class="docutils literal notranslate"><span class="pre">pygame.cdrom.get_count()</span></code> to determine how many drives actually exist.
The id argument is an integer of the drive, starting at zero.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">CD</span></code> object is not initialized, you can only call <code class="docutils literal notranslate"><span class="pre">CD.get_id()</span></code> and
<code class="docutils literal notranslate"><span class="pre">CD.get_name()</span></code> on an uninitialized drive.</p>
<p>It is safe to create multiple <code class="docutils literal notranslate"><span class="pre">CD</span></code> objects for the same drive, they will
all cooperate normally.</p>
<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.init">
<span class="sig-name descname"><span class="pre">init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">initialize a cdrom drive for use</span></div>
<div class="line"><span class="signature">init() -&gt; None</span></div>
</div>
<p>Initialize the cdrom drive for use. The drive must be initialized for
most <code class="docutils literal notranslate"><span class="pre">CD</span></code> methods to work. Even if the rest of pygame has been
initialized.</p>
<p>There may be a brief pause while the drive is initialized. Avoid
<code class="docutils literal notranslate"><span class="pre">CD.init()</span></code> if the program should not stop for a second or two.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.quit">
<span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.quit" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">uninitialize a cdrom drive for use</span></div>
<div class="line"><span class="signature">quit() -&gt; None</span></div>
</div>
<p>Uninitialize a drive for use. Call this when your program will not be
accessing the drive for awhile.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_init">
<span class="sig-name descname"><span class="pre">get_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">true if this cd device initialized</span></div>
<div class="line"><span class="signature">get_init() -&gt; bool</span></div>
</div>
<p>Test if this <code class="docutils literal notranslate"><span class="pre">CDROM</span></code> device is initialized. This is different than the
<code class="docutils literal notranslate"><span class="pre">pygame.cdrom.init()</span></code> since each drive must also be initialized
individually.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.play">
<span class="sig-name descname"><span class="pre">play</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.play" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">start playing audio</span></div>
<div class="line"><span class="signature">play(track, start=None, end=None) -&gt; None</span></div>
</div>
<p>Playback audio from an audio cdrom in the drive. Besides the track number
argument, you can also pass a starting and ending time for playback. The
start and end time are in seconds, and can limit the section of an audio
track played.</p>
<p>If you pass a start time but no end, the audio will play to the end of
the track. If you pass a start time and 'None' for the end time, the
audio will play to the end of the entire disc.</p>
<p>See the <code class="docutils literal notranslate"><span class="pre">CD.get_numtracks()</span></code> and <code class="docutils literal notranslate"><span class="pre">CD.get_track_audio()</span></code> to find
tracks to playback.</p>
<p>Note, track 0 is the first track on the <code class="docutils literal notranslate"><span class="pre">CD</span></code>. Track numbers start at
zero.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.stop">
<span class="sig-name descname"><span class="pre">stop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.stop" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">stop audio playback</span></div>
<div class="line"><span class="signature">stop() -&gt; None</span></div>
</div>
<p>Stops playback of audio from the cdrom. This will also lose the current
playback position. This method does nothing if the drive isn't already
playing audio.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.pause">
<span class="sig-name descname"><span class="pre">pause</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.pause" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">temporarily stop audio playback</span></div>
<div class="line"><span class="signature">pause() -&gt; None</span></div>
</div>
<p>Temporarily stop audio playback on the <code class="docutils literal notranslate"><span class="pre">CD</span></code>. The playback can be
resumed at the same point with the <code class="docutils literal notranslate"><span class="pre">CD.resume()</span></code> method. If the <code class="docutils literal notranslate"><span class="pre">CD</span></code>
is not playing this method does nothing.</p>
<p>Note, track 0 is the first track on the <code class="docutils literal notranslate"><span class="pre">CD</span></code>. Track numbers start at
zero.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.resume">
<span class="sig-name descname"><span class="pre">resume</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.resume" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">unpause audio playback</span></div>
<div class="line"><span class="signature">resume() -&gt; None</span></div>
</div>
<p>Unpause a paused <code class="docutils literal notranslate"><span class="pre">CD</span></code>. If the <code class="docutils literal notranslate"><span class="pre">CD</span></code> is not paused or already playing,
this method does nothing.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.eject">
<span class="sig-name descname"><span class="pre">eject</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.eject" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">eject or open the cdrom drive</span></div>
<div class="line"><span class="signature">eject() -&gt; None</span></div>
</div>
<p>This will open the cdrom drive and eject the cdrom. If the drive is
playing or paused it will be stopped.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_id">
<span class="sig-name descname"><span class="pre">get_id</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_id" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">the index of the cdrom drive</span></div>
<div class="line"><span class="signature">get_id() -&gt; id</span></div>
</div>
<p>Returns the integer id that was used to create the <code class="docutils literal notranslate"><span class="pre">CD</span></code> instance. This
method can work on an uninitialized <code class="docutils literal notranslate"><span class="pre">CD</span></code>.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_name">
<span class="sig-name descname"><span class="pre">get_name</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_name" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">the system name of the cdrom drive</span></div>
<div class="line"><span class="signature">get_name() -&gt; name</span></div>
</div>
<p>Return the string name of the drive. This is the system name used to
represent the drive. It is often the drive letter or device name. This
method can work on an uninitialized <code class="docutils literal notranslate"><span class="pre">CD</span></code>.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_busy">
<span class="sig-name descname"><span class="pre">get_busy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_busy" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">true if the drive is playing audio</span></div>
<div class="line"><span class="signature">get_busy() -&gt; bool</span></div>
</div>
<p>Returns True if the drive busy playing back audio.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_paused">
<span class="sig-name descname"><span class="pre">get_paused</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_paused" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">true if the drive is paused</span></div>
<div class="line"><span class="signature">get_paused() -&gt; bool</span></div>
</div>
<p>Returns True if the drive is currently paused.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_current">
<span class="sig-name descname"><span class="pre">get_current</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_current" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">the current audio playback position</span></div>
<div class="line"><span class="signature">get_current() -&gt; track, seconds</span></div>
</div>
<p>Returns both the current track and time of that track. This method works
when the drive is either playing or paused.</p>
<p>Note, track 0 is the first track on the <code class="docutils literal notranslate"><span class="pre">CD</span></code>. Track numbers start at
zero.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_empty">
<span class="sig-name descname"><span class="pre">get_empty</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_empty" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">False if a cdrom is in the drive</span></div>
<div class="line"><span class="signature">get_empty() -&gt; bool</span></div>
</div>
<p>Return False if there is a cdrom currently in the drive. If the drive is
empty this will return True.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_numtracks">
<span class="sig-name descname"><span class="pre">get_numtracks</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_numtracks" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">the number of tracks on the cdrom</span></div>
<div class="line"><span class="signature">get_numtracks() -&gt; count</span></div>
</div>
<p>Return the number of tracks on the cdrom in the drive. This will return
zero of the drive is empty or has no tracks.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_track_audio">
<span class="sig-name descname"><span class="pre">get_track_audio</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_track_audio" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">true if the cdrom track has audio data</span></div>
<div class="line"><span class="signature">get_track_audio(track) -&gt; bool</span></div>
</div>
<p>Determine if a track on a cdrom contains audio data. You can also call
<code class="docutils literal notranslate"><span class="pre">CD.num_tracks()</span></code> and <code class="docutils literal notranslate"><span class="pre">CD.get_all()</span></code> to determine more information
about the cdrom.</p>
<p>Note, track 0 is the first track on the <code class="docutils literal notranslate"><span class="pre">CD</span></code>. Track numbers start at
zero.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_all">
<span class="sig-name descname"><span class="pre">get_all</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_all" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get all track information</span></div>
<div class="line"><span class="signature">get_all() -&gt; [(audio, start, end, length), ...]</span></div>
</div>
<p>Return a list with information for every track on the cdrom. The
information consists of a tuple with four values. The audio value is True
if the track contains audio data. The start, end, and length values are
floating point numbers in seconds. Start and end represent absolute times
on the entire disc.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_track_start">
<span class="sig-name descname"><span class="pre">get_track_start</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_track_start" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">start time of a cdrom track</span></div>
<div class="line"><span class="signature">get_track_start(track) -&gt; seconds</span></div>
</div>
<p>Return the absolute time in seconds where at start of the cdrom track.</p>
<p>Note, track 0 is the first track on the <code class="docutils literal notranslate"><span class="pre">CD</span></code>. Track numbers start at
zero.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame.cdrom.CD.get_track_length">
<span class="sig-name descname"><span class="pre">get_track_length</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.cdrom.CD.get_track_length" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">length of a cdrom track</span></div>
<div class="line"><span class="signature">get_track_length(track) -&gt; seconds</span></div>
</div>
<p>Return a floating point value in seconds of the length of the cdrom
track.</p>
<p>Note, track 0 is the first track on the <code class="docutils literal notranslate"><span class="pre">CD</span></code>. Track numbers start at
zero.</p>
</dd></dl>

</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref\cdrom.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="color.html" title="pygame.Color"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="camera.html" title="pygame.camera"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.cdrom</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>