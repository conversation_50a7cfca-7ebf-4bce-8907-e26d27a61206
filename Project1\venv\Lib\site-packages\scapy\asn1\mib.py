# SPDX-License-Identifier: GPL-2.0-only
# This file is part of Scapy
# See https://scapy.net/ for more information
# Copyright (C) <PERSON> <<EMAIL>>
# Acknowledgment: <PERSON><PERSON> <<EMAIL>>

"""
Management Information Base (MIB) parsing
"""

import re
from glob import glob
from scapy.dadict import DADict, fixname
from scapy.config import conf
from scapy.utils import do_graph
from scapy.compat import plain_str

from typing import (
    Any,
    Dict,
    List,
    Optional,
    Tuple,
)

#################
#  MIB parsing  #
#################

_mib_re_integer = re.compile(r"^[0-9]+$")
_mib_re_both = re.compile(r"^([a-zA-Z_][a-zA-Z0-9_-]*)\(([0-9]+)\)$")
_mib_re_oiddecl = re.compile(
    r"$\s*([a-zA-Z0-9_-]+)\s+OBJECT[^:\{\}]+::=\s*\{([^\}]+)\}", re.M)
_mib_re_strings = re.compile(r'"[^"]*"')
_mib_re_comments = re.compile(r'--.*(\r|\n)')


class MIBDict(DADict[str, str]):
    def _findroot(self, x):
        # type: (str) -> Tuple[str, str, str]
        """Internal MIBDict function used to find a partial OID"""
        if x.startswith("."):
            x = x[1:]
        if not x.endswith("."):
            x += "."
        max = 0
        root = "."
        root_key = ""
        for k in self:
            if x.startswith(k + "."):
                if max < len(k):
                    max = len(k)
                    root = self[k]
                    root_key = k
        return root, root_key, x[max:-1]

    def _oidname(self, x):
        # type: (str) -> str
        """Deduce the OID name from its OID ID"""
        root, _, remainder = self._findroot(x)
        return root + remainder

    def _oid(self, x):
        # type: (str) -> str
        """Parse the OID id/OID generator, and return real OID"""
        xl = x.strip(".").split(".")
        p = len(xl) - 1
        while p >= 0 and _mib_re_integer.match(xl[p]):
            p -= 1
        if p != 0 or xl[p] not in self.d.values():
            return x
        xl[p] = next(k for k, v in self.d.items() if v == xl[p])
        return ".".join(xl[p:])

    def _make_graph(self, other_keys=None, **kargs):
        # type: (Optional[Any], **Any) -> None
        if other_keys is None:
            other_keys = []
        nodes = [(self[key], key) for key in self.iterkeys()]
        oids = set(self.iterkeys())
        for k in other_keys:
            if k not in oids:
                nodes.append((self._oidname(k), k))
        s = 'digraph "mib" {\n\trankdir=LR;\n\n'
        for k, o in nodes:
            s += '\t"%s" [ label="%s"  ];\n' % (o, k)
        s += "\n"
        for k, o in nodes:
            parent, parent_key, remainder = self._findroot(o[:-1])
            remainder = remainder[1:] + o[-1]
            if parent != ".":
                parent = parent_key
            s += '\t"%s" -> "%s" [label="%s"];\n' % (parent, o, remainder)
        s += "}\n"
        do_graph(s, **kargs)


def _mib_register(ident,  # type: str
                  value,  # type: List[str]
                  the_mib,  # type: Dict[str, List[str]]
                  unresolved,  # type: Dict[str, List[str]]
                  alias,  # type: Dict[str, str]
                  ):
    # type: (...) -> bool
    """
    Internal function used to register an OID and its name in a MIBDict
    """
    if ident in the_mib:
        # We have already resolved this one. Store the alias
        alias[".".join(value)] = ident
        return True
    if ident in unresolved:
        # We know we can't resolve this one
        return False
    resval = []
    not_resolved = 0
    # Resolve the OID
    # (e.g. 2.basicConstraints.3 -> 2.********9.3)
    for v in value:
        if _mib_re_integer.match(v):
            resval.append(v)
        else:
            v = fixname(plain_str(v))
            if v not in the_mib:
                not_resolved = 1
            if v in the_mib:
                resval += the_mib[v]
            elif v in unresolved:
                resval += unresolved[v]
            else:
                resval.append(v)
    if not_resolved:
        # Unresolved
        unresolved[ident] = resval
        return False
    else:
        # Fully resolved
        the_mib[ident] = resval
        keys = list(unresolved)
        i = 0
        # Go through the unresolved to update the ones that
        # depended on the one we just did
        while i < len(keys):
            k = keys[i]
            if _mib_register(k, unresolved[k], the_mib, {}, alias):
                # Now resolved: we can remove it from unresolved
                del unresolved[k]
                del keys[i]
                i = 0
            else:
                i += 1

        return True


def load_mib(filenames):
    # type: (str) -> None
    """
    Load the conf.mib dict from a list of filenames
    """
    the_mib = {'iso': ['1']}
    unresolved = {}  # type: Dict[str, List[str]]
    alias = {}  # type: Dict[str, str]
    # Export the current MIB to a working dictionary
    for k in conf.mib:
        _mib_register(conf.mib[k], k.split("."), the_mib, unresolved, alias)

    # Read the files
    if isinstance(filenames, (str, bytes)):
        files_list = [filenames]
    else:
        files_list = filenames
    for fnames in files_list:
        for fname in glob(fnames):
            with open(fname) as f:
                text = f.read()
            cleantext = " ".join(
                _mib_re_strings.split(" ".join(_mib_re_comments.split(text)))
            )
            for m in _mib_re_oiddecl.finditer(cleantext):
                gr = m.groups()
                ident, oid_s = gr[0], gr[-1]
                ident = fixname(ident)
                oid_l = oid_s.split()
                for i, elt in enumerate(oid_l):
                    m2 = _mib_re_both.match(elt)
                    if m2:
                        oid_l[i] = m2.groups()[1]
                _mib_register(ident, oid_l, the_mib, unresolved, alias)

    # Create the new MIB
    newmib = MIBDict(_name="MIB")
    # Add resolved values
    for oid, key in the_mib.items():
        newmib[".".join(key)] = oid
    # Add unresolved values
    for oid, key in unresolved.items():
        newmib[".".join(key)] = oid
    # Add aliases
    for key_s, oid in alias.items():
        newmib[key_s] = oid

    conf.mib = newmib


####################
#  OID references  #
####################

#      pkcs1       #

pkcs1_oids = {
    "1.2.840.113549.1.1": "pkcs1",
    "1.2.840.113549.1.1.1": "rsaEncryption",
    "1.2.840.113549.1.1.2": "md2WithRSAEncryption",
    "1.2.840.113549.1.1.3": "md4WithRSAEncryption",
    "1.2.840.113549.1.1.4": "md5WithRSAEncryption",
    "1.2.840.113549.1.1.5": "sha1-with-rsa-signature",
    "1.2.840.113549.1.1.6": "rsaOAEPEncryptionSET",
    "1.2.840.113549.1.1.7": "id-RSAES-OAEP",
    "1.2.840.113549.1.1.8": "id-mgf1",
    "1.2.840.113549.1.1.9": "id-pSpecified",
    "1.2.840.113549.1.1.10": "rsassa-pss",
    "1.2.840.113549.1.1.11": "sha256WithRSAEncryption",
    "1.2.840.113549.1.1.12": "sha384WithRSAEncryption",
    "1.2.840.113549.1.1.13": "sha512WithRSAEncryption",
    "1.2.840.113549.1.1.14": "sha224WithRSAEncryption"
}

#       secsig oiw       #

secsig_oids = {
    "********.2": "OIWSEC",
    "********.2.2": "md4RSA",
    "********.2.3": "md5RSA",
    "********.2.4": "md4RSA2",
    "********.2.6": "desECB",
    "********.2.7": "desCBC",
    "********.2.8": "desOFB",
    "********.2.9": "desCFB",
    "********.2.10": "desMAC",
    "********.2.11": "rsaSign",
    "********.2.12": "dsa",
    "********.2.13": "shaDSA",
    "********.2.14": "mdc2RSA",
    "********.2.15": "shaRSA",
    "********.2.16": "dhCommMod",
    "********.2.17": "desEDE",
    "********.2.18": "sha",
    "********.2.19": "mdc2",
    "********.2.20": "dsaComm",
    "********.2.21": "dsaCommSHA",
    "1.3.*********": "rsaXchg",
    "********.2.23": "keyHashSeal",
    "********.2.24": "md2RSASign",
    "********.2.25": "md5RSASign",
    "********.2.26": "sha1",
    "********.2.27": "dsaSHA1",
    "********.2.28": "dsaCommSHA1",
    "********.2.29": "sha1RSASign",
}

#       thawte      #

thawte_oids = {
    "***********": "Ed25519",
    "***********": "Ed448",
}

#       pkcs9       #

pkcs9_oids = {
    "1.2.840.113549.1.9": "pkcs9",
    "1.2.840.113549.1.9.0": "modules",
    "1.2.840.113549.1.9.1": "emailAddress",
    "1.2.840.113549.1.9.2": "unstructuredName",
    "1.2.840.113549.1.9.3": "contentType",
    "1.2.840.113549.1.9.4": "messageDigest",
    "1.2.840.113549.1.9.5": "signing-time",
    "1.2.840.113549.1.9.6": "countersignature",
    "1.2.840.113549.1.9.7": "challengePassword",
    "1.2.840.113549.1.9.8": "unstructuredAddress",
    "1.2.840.113549.1.9.9": "extendedCertificateAttributes",
    "1.2.840.113549.1.9.13": "signingDescription",
    "1.2.840.113549.1.9.14": "extensionRequest",
    "1.2.840.113549.1.9.15": "smimeCapabilities",
    "1.2.840.113549.1.9.16": "smime",
    "1.2.840.113549.1.9.17": "pgpKeyID",
    "1.2.840.113549.1.9.20": "friendlyName",
    "1.2.840.113549.1.9.21": "localKeyID",
    "1.2.840.113549.1.9.22": "certTypes",
    "1.2.840.113549.1.9.23": "crlTypes",
    "1.2.840.113549.1.9.24": "pkcs-9-oc",
    "1.2.840.113549.1.9.25": "pkcs-9-at",
    "1.2.840.113549.1.9.26": "pkcs-9-sx",
    "1.2.840.113549.1.9.27": "pkcs-9-mr",
    "1.2.840.113549.1.9.52": "id-aa-CMSAlgorithmProtection"
}

#       x509       #

attributeType_oids = {
    "*******": "objectClass",
    "*******": "aliasedEntryName",
    "*******": "knowledgeInformation",
    "*******": "commonName",
    "*******": "surname",
    "*******": "serialNumber",
    "*******": "countryName",
    "*******": "localityName",
    "*******": "stateOrProvinceName",
    "*******": "streetAddress",
    "*******0": "organizationName",
    "*******1": "organizationUnitName",
    "********": "title",
    "*******3": "description",
    "*******4": "searchGuide",
    "*******5": "businessCategory",
    "********": "postalAddress",
    "********": "postalCode",
    "*******8": "postOfficeBox",
    "*******9": "physicalDeliveryOfficeName",
    "********": "telephoneNumber",
    "*******1": "telexNumber",
    "*******2": "teletexTerminalIdentifier",
    "********": "facsimileTelephoneNumber",
    "*******4": "x121Address",
    "*******5": "internationalISDNNumber",
    "********": "registeredAddress",
    "*******7": "destinationIndicator",
    "*******8": "preferredDeliveryMethod",
    "*******9": "presentationAddress",
    "*******0": "supportedApplicationContext",
    "*******1": "member",
    "*******2": "owner",
    "*******3": "roleOccupant",
    "*******4": "seeAlso",
    "*******5": "userPassword",
    "*******6": "userCertificate",
    "*******7": "cACertificate",
    "*******8": "authorityRevocationList",
    "*******9": "certificateRevocationList",
    "*******0": "crossCertificatePair",
    "*******1": "name",
    "*******2": "givenName",
    "*******3": "initials",
    "*******4": "generationQualifier",
    "*******5": "uniqueIdentifier",
    "*******6": "dnQualifier",
    "*******7": "enhancedSearchGuide",
    "*******8": "protocolInformation",
    "*******9": "distinguishedName",
    "********": "uniqueMember",
    "********": "houseIdentifier",
    "********": "supportedAlgorithms",
    "********": "deltaRevocationList",
    "********": "dmdName",
    "********": "clearance",
    "********": "defaultDirQop",
    "********": "attributeIntegrityInfo",
    "********": "attributeCertificate",
    "*******9": "attributeCertificateRevocationList",
    "*******0": "confKeyInfo",
    "*******1": "aACertificate",
    "*******2": "attributeDescriptorCertificate",
    "*******3": "attributeAuthorityRevocationList",
    "*******4": "family-information",
    "*******5": "pseudonym",
    "*******6": "communicationsService",
    "*******7": "communicationsNetwork",
    "*******8": "certificationPracticeStmt",
    "*******9": "certificatePolicy",
    "*******0": "pkiPath",
    "*******1": "privPolicy",
    "*******2": "role",
    "*******3": "delegationPath",
    "*******4": "protPrivPolicy",
    "*******5": "xMLPrivilegeInfo",
    "*******6": "xmlPrivPolicy",
    "*******7": "uuidpair",
    "*******8": "tagOid",
    "*******9": "uiiFormat",
    "*******0": "uiiInUrh",
    "********": "contentUrl",
    "*******2": "permission",
    "*******3": "uri",
    "*******4": "pwdAttribute",
    "*******5": "userPwd",
    "*******6": "urn",
    "*******7": "url",
    "*******8": "utmCoordinates",
    "********": "urnC",
    "*******0": "uii",
    "*******1": "epc",
    "*******2": "tagAfi",
    "*******3": "epcFormat",
    "*******4": "epcInUrn",
    "*******5": "ldapUrl",
    "*******6": "ldapUrl",
    "*******7": "organizationIdentifier",
    # RFC 4519
    "0.9.2342.19200300.100.1.25": "dc",
}

certificateExtension_oids = {
    "********": "authorityKeyIdentifier(obsolete)",
    "********": "keyAttributes",
    "********": "certificatePolicies(obsolete)",
    "2.5.29.4": "keyUsageRestriction",
    "2.5.29.5": "policyMapping",
    "2.5.29.6": "subtreesConstraint",
    "2.5.29.7": "subjectAltName(obsolete)",
    "2.5.29.8": "issuerAltName(obsolete)",
    "2.5.29.9": "subjectDirectoryAttributes",
    "********0": "basicConstraints(obsolete)",
    "********4": "subjectKeyIdentifier",
    "********5": "keyUsage",
    "********6": "privateKeyUsagePeriod",
    "********7": "subjectAltName",
    "********8": "issuerAltName",
    "********9": "basicConstraints",
    "********0": "cRLNumber",
    "********1": "reasonCode",
    "********2": "expirationDate",
    "********3": "instructionCode",
    "********4": "invalidityDate",
    "********5": "cRLDistributionPoints(obsolete)",
    "********6": "issuingDistributionPoint(obsolete)",
    "********7": "deltaCRLIndicator",
    "********8": "issuingDistributionPoint",
    "********9": "certificateIssuer",
    "********0": "nameConstraints",
    "********1": "cRLDistributionPoints",
    "********2": "certificatePolicies",
    "********3": "policyMappings",
    "********4": "policyConstraints(obsolete)",
    "********5": "authorityKeyIdentifier",
    "********6": "policyConstraints",
    "********7": "extKeyUsage",
    "********8": "authorityAttributeIdentifier",
    "********9": "roleSpecCertIdentifier",
    "*********": "cRLStreamIdentifier",
    "*********": "basicAttConstraints",
    "*********": "delegatedNameConstraints",
    "*********": "timeSpecification",
    "*********": "cRLScope",
    "*********": "statusReferrals",
    "*********": "freshestCRL",
    "*********": "orderedList",
    "*********": "attributeDescriptor",
    "*********": "userNotice",
    "*********": "sOAIdentifier",
    "*********": "baseUpdateTime",
    "*********": "acceptableCertPolicies",
    "*********": "deltaInfo",
    "*********": "inhibitAnyPolicy",
    "*********": "targetInformation",
    "*********": "noRevAvail",
    "*********": "acceptablePrivilegePolicies",
    "*********": "id-ce-toBeRevoked",
    "*********": "id-ce-RevokedGroups",
    "*********": "id-ce-expiredCertsOnCRL",
    "*********": "indirectIssuer",
    "*********": "id-ce-noAssertion",
    "*********": "id-ce-aAissuingDistributionPoint",
    "*********": "id-ce-issuedOnBehaIFOF",
    "*********": "id-ce-singleUse",
    "*********": "id-ce-groupAC",
    "*********": "id-ce-allowedAttAss",
    "*********": "id-ce-attributeMappings",
    "*********": "id-ce-holderNameConstraints",
    # [MS-WCCE]
    "*******.4.1.311.2.1.14": "CERT_EXTENSIONS",
    "*******.4.1.311.20.2": "ENROLL_CERTTYPE",
    "*******.4.1.311.25.1": "NTDS_REPLICATION",
    "*******.4.1.311.25.2": "NTDS_CA_SECURITY_EXT",
    "*******.4.1.311.25.2.1": "NTDS_OBJECTSID",
}

certExt_oids = {
    "2.16.840.1.113730.1.1": "cert-type",
    "2.16.840.1.113730.1.2": "base-url",
    "2.16.840.1.113730.1.3": "revocation-url",
    "2.16.840.1.113730.1.4": "ca-revocation-url",
    "2.16.840.1.113730.1.5": "ca-crl-url",
    "2.16.840.1.113730.1.6": "ca-cert-url",
    "2.16.840.1.113730.1.7": "renewal-url",
    "2.16.840.1.113730.1.8": "ca-policy-url",
    "2.16.840.1.113730.1.9": "homepage-url",
    "2.16.840.1.113730.1.10": "entity-logo",
    "2.16.840.1.113730.1.11": "user-picture",
    "2.16.840.1.113730.1.12": "ssl-server-name",
    "2.16.840.1.113730.1.13": "comment",
    "2.16.840.1.113730.1.14": "lost-password-url",
    "2.16.840.1.113730.1.15": "cert-renewal-time",
    "2.16.840.1.113730.1.16": "aia",
    "2.16.840.1.113730.1.17": "cert-scope-of-use",
}

certPkixPe_oids = {
    "*******.*******.1": "authorityInfoAccess",
    "*******.*******.2": "biometricInfo",
    "*******.*******.3": "qcStatements",
    "*******.*******.4": "auditIdentity",
    "*******.*******.6": "aaControls",
    "*******.*******.10": "proxying",
    "*******.*******.11": "subjectInfoAccess"
}

certPkixQt_oids = {
    "*******.*******.1": "cps",
    "*******.*******.2": "unotice"
}

certPkixKp_oids = {
    "*******.*******.1": "serverAuth",
    "*******.*******.2": "clientAuth",
    "*******.*******.3": "codeSigning",
    "*******.*******.4": "emailProtection",
    "*******.*******.5": "ipsecEndSystem",
    "*******.*******.6": "ipsecTunnel",
    "*******.*******.7": "ipsecUser",
    "*******.*******.8": "timeStamping",
    "*******.*******.9": "ocspSigning",
    "*******.*******.10": "dvcs",
    "*******.*******.21": "secureShellClient",
    "*******.*******.22": "secureShellServer"
}

certPkixAd_oids = {
    "*******.********.1": "ocsp",
    "*******.********.2": "caIssuers",
    "*******.********.3": "timestamping",
    "*******.********.4": "id-ad-dvcs",
    "*******.********.5": "id-ad-caRepository",
    "*******.********.6": "id-pkix-ocsp-archive-cutoff",
    "*******.********.7": "id-pkix-ocsp-service-locator",
    "*******.********.12": "id-ad-cmc",
    "*******.********.1.1": "basic-response"
}

certTransp_oids = {
    '*******.4.1.11129.2.4.2': "SignedCertificateTimestampList",
}

#       ansi-x962       #

x962KeyType_oids = {
    "1.2.840.10045.1.1": "prime-field",
    "1.2.840.10045.1.2": "characteristic-two-field",
    "1.2.840.10045.2.1": "ecPublicKey",
}

x962Signature_oids = {
    "1.2.840.10045.4.1": "ecdsa-with-SHA1",
    "1.2.840.10045.4.2": "ecdsa-with-Recommended",
    "1.2.840.10045.4.3.1": "ecdsa-with-SHA224",
    "1.2.840.10045.4.3.2": "ecdsa-with-SHA256",
    "1.2.840.10045.4.3.3": "ecdsa-with-SHA384",
    "1.2.840.10045.4.3.4": "ecdsa-with-SHA512"
}

#       elliptic curves       #

ansiX962Curve_oids = {
    "1.2.840.10045.3.1.1": "prime192v1",
    "1.2.840.10045.3.1.2": "prime192v2",
    "1.2.840.10045.3.1.3": "prime192v3",
    "1.2.840.10045.3.1.4": "prime239v1",
    "1.2.840.10045.3.1.5": "prime239v2",
    "1.2.840.10045.3.1.6": "prime239v3",
    "1.2.840.10045.3.1.7": "prime256v1"
}

certicomCurve_oids = {
    "*********.1": "ansit163k1",
    "*********.2": "ansit163r1",
    "*********.3": "ansit239k1",
    "*********.4": "sect113r1",
    "*********.5": "sect113r2",
    "*********.6": "secp112r1",
    "*********.7": "secp112r2",
    "*********.8": "ansip160r1",
    "*********.9": "ansip160k1",
    "*********.10": "ansip256k1",
    "*********.15": "ansit163r2",
    "*********.16": "ansit283k1",
    "*********.17": "ansit283r1",
    "*********.22": "sect131r1",
    "*********.24": "ansit193r1",
    "*********.25": "ansit193r2",
    "*********.26": "ansit233k1",
    "*********.27": "ansit233r1",
    "*********.28": "secp128r1",
    "*********.29": "secp128r2",
    "*********.30": "ansip160r2",
    "*********.31": "ansip192k1",
    "*********.32": "ansip224k1",
    "*********.33": "ansip224r1",
    "*********.34": "ansip384r1",
    "*********.35": "ansip521r1",
    "*********.36": "ansit409k1",
    "*********.37": "ansit409r1",
    "*********.38": "ansit571k1",
    "*********.39": "ansit571r1"
}

#       policies       #

certPolicy_oids = {
    "********2.0": "anyPolicy"
}

# from Chromium source code (ev_root_ca_metadata.cc)
evPolicy_oids = {
    '1.2.392.200091.100.721.1': 'EV Security Communication RootCA1',
    '1.2.616.1.113527.*******': 'EV Certum Trusted Network CA',
    '*********.17.1': 'EV Actualis Authentication Root CA',
    '*******.4.1.13177.*********': 'EV Autoridad de Certificacion Firmaprofesional CIF A62634068',
    '*******.4.1.14370.1.6': 'EV GeoTrust Primary Certification Authority',
    '*******.4.1.14777.6.1.1': 'EV Izenpe.com roots Business',
    '*******.4.1.14777.6.1.2': 'EV Izenpe.com roots Government',
    '*******.4.1.17326.*********.2': 'EV AC Camerfirma S.A. Chambers of Commerce Root - 2008',
    '*******.4.1.17326.*********.2': 'EV AC Camerfirma S.A. Chambers of Commerce Root - 2008',
    '*******.4.1.17326.10.8.12.1.2': 'EV AC Camerfirma S.A. Global Chambersign Root - 2008',
    '*******.4.1.17326.10.8.12.2.2': 'EV AC Camerfirma S.A. Global Chambersign Root - 2008',
    '*******.4.1.22234.2.5.2.3.1': 'EV CertPlus Class 2 Primary CA (KEYNECTIS)',
    '*******.4.1.23223.1.1.1': 'EV StartCom Certification Authority',
    '*******.4.1.29836.1.10': 'EV China Internet Network Information Center EV Certificates Root',
    '*******.4.1.311.60.2.1.1': 'jurisdictionOfIncorporationLocalityName',
    '*******.4.1.311.60.2.1.2': 'jurisdictionOfIncorporationStateOrProvinceName',
    '*******.4.1.311.60.2.1.3': 'jurisdictionOfIncorporationCountryName',
    '*******.4.1.34697.2.1': 'EV AffirmTrust Commercial',
    '*******.4.1.34697.2.2': 'EV AffirmTrust Networking',
    '*******.4.1.34697.2.3': 'EV AffirmTrust Premium',
    '*******.4.1.34697.2.4': 'EV AffirmTrust Premium ECC',
    '*******.4.1.36305.2': 'EV Certificate Authority of WoSign',
    '*******.4.1.40869.1.1.22.3': 'EV TWCA Roots',
    '*******.4.1.4146.1.1': 'EV GlobalSign Root CAs',
    '*******.4.1.4788.2.202.1': 'EV D-TRUST Root Class 3 CA 2 EV 2009',
    '*******.4.1.6334.1.100.1': 'EV Cybertrust Global Root',
    '*******.4.1.6449.1.2.1.5.1': 'EV USERTrust Certification Authorities',
    '*******.4.1.781.1.2.1.8.1': 'EV Network Solutions Certificate Authority',
    '*******.4.1.782.1.2.1.8.1': 'EV AddTrust External CA Root',
    '*******.4.1.7879.13.24.1': 'EV T-Telessec GlobalRoot Class 3',
    '*******.4.1.8024.0.2.100.1.2': 'EV QuoVadis Roots',
    '2.16.528.1.1003.1.2.7': 'EV Staat der Nederlanden EV Root CA',
    '2.16.578.1.26.1.3.3': 'EV Buypass Class 3',
    '2.16.756.1.83.21.0': 'EV Swisscom Root EV CA 2',
    '2.16.756.1.89.1.2.1.1': 'EV SwissSign Gold CA - G2',
    '2.16.792.3.0.4.1.1.4': 'EV E-Tugra Certification Authority',
    '2.16.840.1.113733.1.7.23.6': 'EV VeriSign Certification Authorities',
    '2.16.840.1.113733.1.7.48.1': 'EV thawte CAs',
    '2.16.840.1.114028.10.1.2': 'EV Entrust Certification Authority',
    '2.16.840.1.114171.500.9': 'EV Wells Fargo WellsSecure Public Root Certification Authority',
    '2.16.840.1.114404.1.1.2.4.1': 'EV XRamp Global Certification Authority',
    '2.16.840.1.114412.2.1': 'EV DigiCert High Assurance EV Root CA',
    '2.16.840.1.114413.1.7.23.3': 'EV ValiCert Class 2 Policy Validation Authority',
    '2.16.840.1.114414.1.7.23.3': 'EV Starfield Certificate Authority',
    '2.16.840.1.114414.1.7.24.3': 'EV Starfield Service Certificate Authority'
}

#       gssapi       #

gssapi_oids = {
    '1.2.840.48018.1.2.2': 'MS KRB5 - Microsoft Kerberos 5',
    '1.2.840.113554.1.2.2': 'Kerberos 5',
    '1.2.840.113554.1.2.2.3': 'Kerberos 5 - User to User',
    '*******.5.2.5': 'Kerberos 5 - IAKERB',
    '*******.5.5.2': 'SPNEGO - Simple Protected Negotiation',
    '*******.4.1.311.2.2.10': 'NTLMSSP - Microsoft NTLM Security Support Provider',
    '*******.4.1.311.2.2.30': 'NEGOEX - SPNEGO Extended Negotiation Security Mechanism',
}


x509_oids_sets = [
    pkcs1_oids,
    secsig_oids,
    thawte_oids,
    pkcs9_oids,
    attributeType_oids,
    certificateExtension_oids,
    certExt_oids,
    certPkixAd_oids,
    certPkixKp_oids,
    certPkixPe_oids,
    certPkixQt_oids,
    certPolicy_oids,
    certTransp_oids,
    evPolicy_oids,
    x962KeyType_oids,
    x962Signature_oids,
    ansiX962Curve_oids,
    certicomCurve_oids,
    gssapi_oids,
]

x509_oids = {}

for oids_set in x509_oids_sets:
    x509_oids.update(oids_set)

conf.mib = MIBDict(_name="MIB", **x509_oids)


#########################
#  Hash mapping helper  #
#########################

# This dict enables static access to string references to the hash functions
# of some algorithms from pkcs1_oids and x962Signature_oids.

hash_by_oid = {
    "1.2.840.113549.1.1.2": "md2",
    "1.2.840.113549.1.1.3": "md4",
    "1.2.840.113549.1.1.4": "md5",
    "1.2.840.113549.1.1.5": "sha1",
    "1.2.840.113549.1.1.11": "sha256",
    "1.2.840.113549.1.1.12": "sha384",
    "1.2.840.113549.1.1.13": "sha512",
    "1.2.840.113549.1.1.14": "sha224",
    "1.2.840.10045.4.1": "sha1",
    "1.2.840.10045.4.3.1": "sha224",
    "1.2.840.10045.4.3.2": "sha256",
    "1.2.840.10045.4.3.3": "sha384",
    "1.2.840.10045.4.3.4": "sha512"
}
