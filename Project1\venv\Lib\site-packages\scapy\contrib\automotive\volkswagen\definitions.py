# SPDX-License-Identifier: GPL-2.0-only
# This file is part of Scapy
# See https://scapy.net/ for more information
# Copyright (C) <PERSON><PERSON> <<EMAIL>>
# Copyright (C) <PERSON> <<EMAIL>>

# scapy.contrib.description = Volkswagen specific definitions for UDS
# scapy.contrib.status = skip


from scapy.contrib.automotive.uds import UDS_RDBI, UDS_RC, UDS_RD


UDS_RDBI.dataIdentifiers[0x00bd] = "Theft Protection - Download GFA-Key"
UDS_RDBI.dataIdentifiers[0x00be] = "Theft Protection - Download IKA-Key"
UDS_RDBI.dataIdentifiers[0x00fd] = "IUMPR-ID3"
UDS_RDBI.dataIdentifiers[0x00fe] = "IUMPR-ID2"
UDS_RDBI.dataIdentifiers[0x00ff] = "IUMPR-ID1"
UDS_RDBI.dataIdentifiers[0x02cc] = "Vehicle_identification_number_provisional"
UDS_RDBI.dataIdentifiers[0x02e0] = "Immobilizer - Challenge"
UDS_RDBI.dataIdentifiers[0x02e1] = "Immobilizer - Login"
UDS_RDBI.dataIdentifiers[0x02e2] = "Immobilizer - Download Powertrain"
UDS_RDBI.dataIdentifiers[0x02e3] = "Immobilizer - Download IMS"
UDS_RDBI.dataIdentifiers[0x02e4] = "Transponder ID current Key"
UDS_RDBI.dataIdentifiers[0x02e5] = "Transponder ID Key 1"
UDS_RDBI.dataIdentifiers[0x02e6] = "Transponder ID Key 2"
UDS_RDBI.dataIdentifiers[0x02e7] = "Transponder ID Key 3"
UDS_RDBI.dataIdentifiers[0x02e8] = "Transponder ID Key 4"
UDS_RDBI.dataIdentifiers[0x02e9] = "Transponder ID Key 5"
UDS_RDBI.dataIdentifiers[0x02ea] = "Transponder ID Key 6"
UDS_RDBI.dataIdentifiers[0x02eb] = "Transponder ID Key 7"
UDS_RDBI.dataIdentifiers[0x02ec] = "Transponder ID Key 8"
UDS_RDBI.dataIdentifiers[0x02ed] = "State of Immobilizer"
UDS_RDBI.dataIdentifiers[0x02ee] = "State of Immobilizer Slaves"
UDS_RDBI.dataIdentifiers[0x02ef] = "State Blocking Time"
UDS_RDBI.dataIdentifiers[0x02f1] = "Immobilizer - Slave Login"
UDS_RDBI.dataIdentifiers[0x02f6] = "Download WFS SHE"
UDS_RDBI.dataIdentifiers[0x02f9] = "CRC32 Checksum of FAZIT Identification String"
UDS_RDBI.dataIdentifiers[0x02fa] = "Adapted_transponders_checksum"
UDS_RDBI.dataIdentifiers[0x02fb] = "Immobilizer - Download WFS 4"
UDS_RDBI.dataIdentifiers[0x02ff] = "Immobilizer_snapshot"
UDS_RDBI.dataIdentifiers[0x0407] = "VW Logical Software Block Counter Of Programming Attempts"
UDS_RDBI.dataIdentifiers[0x040f] = "VW Logical Software Block Lock Value"
UDS_RDBI.dataIdentifiers[0x0410] = "Bootloader TP Blocksize"
UDS_RDBI.dataIdentifiers[0x04a3] = "Gateway Component List"
UDS_RDBI.dataIdentifiers[0x0600] = "VW Coding Value"
UDS_RDBI.dataIdentifiers[0x0610] = "Control_unit_for_wiper_motor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0611] = "Slave_list_VW_spare_part_number"
UDS_RDBI.dataIdentifiers[0x0612] = "Slave_list_VW_software_version_number"
UDS_RDBI.dataIdentifiers[0x0613] = "Slave_list_VW_ecu_hardware_version_number"
UDS_RDBI.dataIdentifiers[0x0614] = "Slave_list_VW_hardware_number"
UDS_RDBI.dataIdentifiers[0x0615] = "Slave_list_ecu_serial_number"
UDS_RDBI.dataIdentifiers[0x0616] = "Slave_list_VW_FAZIT_identification_string"
UDS_RDBI.dataIdentifiers[0x0617] = "Slave_list_VW_system_name_or_engine_type"
UDS_RDBI.dataIdentifiers[0x0618] = "Left_rear_seat_ventilation_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0619] = "Right_rear_seat_ventilation_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x061a] = "Slave_component_list"
UDS_RDBI.dataIdentifiers[0x061b] = "Slave_component_list_databus_identification"
UDS_RDBI.dataIdentifiers[0x061c] = "Slave_component_list_ecu_identification"
UDS_RDBI.dataIdentifiers[0x061d] = "Slave_component_list_present"
UDS_RDBI.dataIdentifiers[0x061e] = "Right_headlamp_power_output_stage_Coding_Values"
UDS_RDBI.dataIdentifiers[0x061f] = "Sensor_for_anti_theft_alarm_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0620] = "Rear_lid_control_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0621] = "Alarm_horn_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0622] = "Automatic_day_night_interior_mirror_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0623] = "Sun_roof_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0624] = "Steering_column_lock_actuator_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0625] = "Anti_theft_tilt_system_control_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0626] = "Tire_pressure_monitor_antenna_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0627] = "Heated_windshield_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0628] = "Rear_light_left_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0629] = "Ceiling_light_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x062a] = "Left_front_massage_seat_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x062b] = "Right_front_massage_seat_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x062c] = "Control_module_for_auxiliary_air_heater_Coding_Values"
UDS_RDBI.dataIdentifiers[0x062d] = "Ioniser_Coding_Values"
UDS_RDBI.dataIdentifiers[0x062e] = "Multi_function_steering_wheel_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x062f] = "Left_rear_door_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0630] = "Right_rear_door_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0631] = "Left_rear_massage_seat_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0632] = "Right_rear_massage_seat_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0633] = "Display_unit_1_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0634] = "Battery_monitoring_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0635] = "Roof_blind_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0636] = "Sun_roof_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0637] = "Display_unit_2_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0638] = "Telephone_handset_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0639] = "Traffic_data_aerial_Coding_Values"
UDS_RDBI.dataIdentifiers[0x063a] = "Chip_card_reader_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x063b] = "Hands_free_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x063c] = "Telephone_handset_Coding_Values"
UDS_RDBI.dataIdentifiers[0x063d] = "Display_unit_front_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x063e] = "Multimedia_operating_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x063f] = "Digital_sound_system_control_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x0640] = "Control_unit_for_wiper_motor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0641] = "Rain_light_recognition_sensor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0642] = "Light_switch_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0643] = "Garage_door_opener_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0644] = "Garage_door_opener_operating_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0645] = "Ignition_key_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0646] = "Left_front_seat_ventilation_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0647] = "Right_front_seat_ventilation_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0648] = "Left_rear_seat_ventilation_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0649] = "Right_rear_seat_ventilation_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x064a] = "Data_medium_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x064b] = "Drivers_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x064c] = "Front_passengers_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x064d] = "Left_headlamp_power_output_stage_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x064e] = "Right_headlamp_power_output_stage_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x064f] = "Sensor_for_anti_theft_alarm_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0650] = "Rear_lid_control_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0651] = "Alarm_horn_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0652] = "Automatic_day_night_interior_mirror_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0653] = "Sun_roof_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0654] = "Steering_column_lock_actuator_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0655] = "Anti_theft_tilt_system_control_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0656] = "Tire_pressure_monitor_antenna_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0657] = "Heated_windshield_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0658] = "Rear_light_left_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0659] = "Ceiling_light_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x065a] = "Left_front_massage_seat_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x065b] = "Right_front_massage_seat_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x065c] = "Control_module_for_auxiliary_air_heater_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x065d] = "Ioniser_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x065e] = "Multi_function_steering_wheel_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x065f] = "Left_rear_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0660] = "Right_rear_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0661] = "Left_rear_massage_seat_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0662] = "Right_rear_massage_seat_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0663] = "Display_unit_1_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0664] = "Battery_monitoring_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0665] = "Roof_blind_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0666] = "Sun_roof_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0667] = "Display_unit_2_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0668] = "Telephone_handset_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0669] = "Traffic_data_aerial_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x066a] = "Chip_card_reader_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x066b] = "Hands_free_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x066c] = "Telephone_handset_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x066d] = "Display_unit_front_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x066e] = "Multimedia_operating_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x066f] = "Digital_sound_system_control_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x0670] = "Control_unit_for_wiper_motor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0671] = "Rain_light_recognition_sensor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0672] = "Light_switch_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0673] = "Garage_door_opener_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0674] = "Garage_door_opener_operating_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0675] = "Ignition_key_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0676] = "Left_front_seat_ventilation_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0677] = "Right_front_seat_ventilation_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0678] = "Left_rear_seat_ventilation_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0679] = "Right_rear_seat_ventilation_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x067a] = "Data_medium_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x067b] = "Drivers_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x067c] = "Front_passengers_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x067d] = "Left_headlamp_power_output_stage_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x067e] = "Right_headlamp_power_output_stage_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x067f] = "Sensor_for_anti_theft_alarm_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0680] = "Rear_lid_control_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0681] = "Alarm_horn_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0682] = "Automatic_day_night_interior_mirror_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0683] = "Sun_roof_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0684] = "Steering_column_lock_actuator_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0685] = "Anti_theft_tilt_system_control_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0686] = "Tire_pressure_monitor_antenna_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0687] = "Heated_windshield_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0688] = "Rear_light_left_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0689] = "Ceiling_light_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x068a] = "Left_front_massage_seat_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x068b] = "Right_front_massage_seat_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x068c] = "Control_module_for_auxiliary_air_heater_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x068d] = "Ioniser_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x068e] = "Multi_function_steering_wheel_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x068f] = "Left_rear_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0690] = "Right_rear_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0691] = "Left_rear_massage_seat_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0692] = "Right_rear_massage_seat_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0693] = "Display_unit_1_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0694] = "Battery_monitoring_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0695] = "Roof_blind_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0696] = "Sun_roof_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0697] = "Display_unit_2_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0698] = "Telephone_handset_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x0699] = "Traffic_data_aerial_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x069a] = "Chip_card_reader_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x069b] = "Hands_free_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x069c] = "Telephone_handset_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x069d] = "Display_unit_front_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x069e] = "Multimedia_operating_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x069f] = "Digital_sound_system_control_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x06a0] = "Control_unit_for_wiper_motor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a1] = "Rain_light_recognition_sensor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a2] = "Light_switch_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a3] = "Garage_door_opener_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a4] = "Garage_door_opener_operating_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a5] = "Ignition_key_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a6] = "Left_front_seat_ventilation_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a7] = "Right_front_seat_ventilation_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a8] = "Left_rear_seat_ventilation_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06a9] = "Right_rear_seat_ventilation_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06aa] = "Data_medium_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06ab] = "Drivers_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06ac] = "Front_passengers_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06ad] = "Left_headlamp_power_output_stage_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06ae] = "Right_headlamp_power_output_stage_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06af] = "Sensor_for_anti_theft_alarm_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b0] = "Rear_lid_control_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b1] = "Alarm_horn_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b2] = "Automatic_day_night_interior_mirror_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b3] = "Sun_roof_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b4] = "Steering_column_lock_actuator_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b5] = "Anti_theft_tilt_system_control_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b6] = "Tire_pressure_monitor_antenna_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b7] = "Heated_windshield_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b8] = "Rear_light_left_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06b9] = "Ceiling_light_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06ba] = "Left_front_massage_seat_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06bb] = "Right_front_massage_seat_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06bc] = "Control_module_for_auxiliary_air_heater_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06bd] = "Ioniser_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06be] = "Multi_function_steering_wheel_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06bf] = "Left_rear_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c0] = "Right_rear_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c1] = "Left_rear_massage_seat_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c2] = "Right_rear_massage_seat_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c3] = "Display_unit_1_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c4] = "Battery_monitoring_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c5] = "Roof_blind_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c6] = "Sun_roof_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c7] = "Display_unit_2_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c8] = "Telephone_handset_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06c9] = "Traffic_data_aerial_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06ca] = "Chip_card_reader_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06cb] = "Hands_free_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06cc] = "Telephone_handset_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06cd] = "Display_unit_front_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06ce] = "Multimedia_operating_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06cf] = "Digital_sound_system_control_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x06d0] = "Control_unit_for_wiper_motor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d1] = "Rain_light_recognition_sensor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d2] = "Light_switch_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d3] = "Garage_door_opener_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d4] = "Garage_door_opener_operating_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d5] = "Ignition_key_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d6] = "Left_front_seat_ventilation_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d7] = "Right_front_seat_ventilation_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d8] = "Left_rear_seat_ventilation_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06d9] = "Right_rear_seat_ventilation_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06da] = "Data_medium_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06db] = "Drivers_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06dc] = "Front_passengers_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06dd] = "Left_headlamp_power_output_stage_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06de] = "Right_headlamp_power_output_stage_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06df] = "Sensor_for_anti_theft_alarm_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e0] = "Rear_lid_control_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e1] = "Alarm_horn_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e2] = "Automatic_day_night_interior_mirror_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e3] = "Sun_roof_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e4] = "Steering_column_lock_actuator_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e5] = "Anti_theft_tilt_system_control_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e6] = "Tire_pressure_monitor_antenna_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e7] = "Heated_windshield_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e8] = "Rear_light_left_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06e9] = "Ceiling_light_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06ea] = "Left_front_massage_seat_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06eb] = "Right_front_massage_seat_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06ec] = "Control_module_for_auxiliary_air_heater_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06ed] = "Ioniser_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06ee] = "Multi_function_steering_wheel_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06ef] = "Left_rear_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f0] = "Right_rear_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f1] = "Left_rear_massage_seat_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f2] = "Right_rear_massage_seat_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f3] = "Display_unit_1_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f4] = "Battery_monitoring_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f5] = "Roof_blind_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f6] = "Sun_roof_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f7] = "Display_unit_2_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f8] = "Telephone_handset_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06f9] = "Traffic_data_aerial_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06fa] = "Chip_card_reader_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06fb] = "Hands_free_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06fc] = "Telephone_handset_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06fd] = "Display_unit_front_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06fe] = "Multimedia_operating_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x06ff] = "Digital_sound_system_control_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x0700] = "Control_unit_for_wiper_motor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0701] = "Rain_light_recognition_sensor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0702] = "Light_switch_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0703] = "Garage_door_opener_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0704] = "Garage_door_opener_operating_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0705] = "Ignition_key_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0706] = "Left_front_seat_ventilation_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0707] = "Right_front_seat_ventilation_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0708] = "Left_rear_seat_ventilation_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0709] = "Right_rear_seat_ventilation_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x070a] = "Data_medium_Serial_Number"
UDS_RDBI.dataIdentifiers[0x070b] = "Drivers_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x070c] = "Front_passengers_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x070d] = "Left_headlamp_power_output_stage_Serial_Number"
UDS_RDBI.dataIdentifiers[0x070e] = "Right_headlamp_power_output_stage_Serial_Number"
UDS_RDBI.dataIdentifiers[0x070f] = "Sensor_for_anti_theft_alarm_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0710] = "Rear_lid_control_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0711] = "Alarm_horn_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0712] = "Automatic_day_night_interior_mirror_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0713] = "Sun_roof_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0714] = "Steering_column_lock_actuator_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0715] = "Anti_theft_tilt_system_control_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0716] = "Tire_pressure_monitor_antenna_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0717] = "Heated_windshield_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0718] = "Rear_light_left_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0719] = "Ceiling_light_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x071a] = "Left_front_massage_seat_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x071b] = "Right_front_massage_seat_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x071c] = "Control_module_for_auxiliary_air_heater_Serial_Number"
UDS_RDBI.dataIdentifiers[0x071d] = "Ioniser_Serial_Number"
UDS_RDBI.dataIdentifiers[0x071e] = "Multi_function_steering_wheel_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x071f] = "Left_rear_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0720] = "Right_rear_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0721] = "Left_rear_massage_seat_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0722] = "Right_rear_massage_seat_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0723] = "Display_unit_1_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0724] = "Battery_monitoring_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0725] = "Roof_blind_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0726] = "Sun_roof_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0727] = "Display_unit_2_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0728] = "Telephone_handset_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0729] = "Traffic_data_aerial_Serial_Number"
UDS_RDBI.dataIdentifiers[0x072a] = "Chip_card_reader_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x072b] = "Hands_free_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x072c] = "Telephone_handset_Serial_Number"
UDS_RDBI.dataIdentifiers[0x072d] = "Display_unit_front_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x072e] = "Multimedia_operating_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x072f] = "Digital_sound_system_control_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x0730] = "Control_unit_for_wiper_motor_System_Name"
UDS_RDBI.dataIdentifiers[0x0731] = "Rain_light_recognition_sensor_System_Name"
UDS_RDBI.dataIdentifiers[0x0732] = "Light_switch_System_Name"
UDS_RDBI.dataIdentifiers[0x0733] = "Garage_door_opener_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0734] = "Garage_door_opener_operating_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x0735] = "Ignition_key_System_Name"
UDS_RDBI.dataIdentifiers[0x0736] = "Left_front_seat_ventilation_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0737] = "Right_front_seat_ventilation_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0738] = "Left_rear_seat_ventilation_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0739] = "Right_rear_seat_ventilation_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x073a] = "Data_medium_System_Name"
UDS_RDBI.dataIdentifiers[0x073b] = "Drivers_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x073c] = "Front_passengers_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x073d] = "Left_headlamp_power_output_stage_System_Name"
UDS_RDBI.dataIdentifiers[0x073e] = "Right_headlamp_power_output_stage_System_Name"
UDS_RDBI.dataIdentifiers[0x073f] = "Sensor_for_anti_theft_alarm_system_System_Name"
UDS_RDBI.dataIdentifiers[0x0740] = "Rear_lid_control_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x0741] = "Alarm_horn_System_Name"
UDS_RDBI.dataIdentifiers[0x0742] = "Automatic_day_night_interior_mirror_System_Name"
UDS_RDBI.dataIdentifiers[0x0743] = "Sun_roof_System_Name"
UDS_RDBI.dataIdentifiers[0x0744] = "Steering_column_lock_actuator_System_Name"
UDS_RDBI.dataIdentifiers[0x0745] = "Anti_theft_tilt_system_control_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x0746] = "Tire_pressure_monitor_antenna_System_Name"
UDS_RDBI.dataIdentifiers[0x0747] = "Heated_windshield_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0748] = "Rear_light_left_1_System_Name"
UDS_RDBI.dataIdentifiers[0x0749] = "Ceiling_light_module_System_Name"
UDS_RDBI.dataIdentifiers[0x074a] = "Left_front_massage_seat_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x074b] = "Right_front_massage_seat_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x074c] = "Control_module_for_auxiliary_air_heater_System_Name"
UDS_RDBI.dataIdentifiers[0x074d] = "Ioniser_System_Name"
UDS_RDBI.dataIdentifiers[0x074e] = "Multi_function_steering_wheel_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x074f] = "Left_rear_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0750] = "Right_rear_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0751] = "Left_rear_massage_seat_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0752] = "Right_rear_massage_seat_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0753] = "Display_unit_1_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x0754] = "Battery_monitoring_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x0755] = "Roof_blind_System_Name"
UDS_RDBI.dataIdentifiers[0x0756] = "Sun_roof_2_System_Name"
UDS_RDBI.dataIdentifiers[0x0757] = "Display_unit_2_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x0758] = "Telephone_handset_2_System_Name"
UDS_RDBI.dataIdentifiers[0x0759] = "Traffic_data_aerial_System_Name"
UDS_RDBI.dataIdentifiers[0x075a] = "Chip_card_reader_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x075b] = "Hands_free_system_System_Name"
UDS_RDBI.dataIdentifiers[0x075c] = "Telephone_handset_System_Name"
UDS_RDBI.dataIdentifiers[0x075d] = "Display_unit_front_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x075e] = "Multimedia_operating_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x075f] = "Digital_sound_system_control_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x07a0] = "Control_unit_for_wiper_motor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a1] = "Rain_light_recognition_sensor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a2] = "Light_switch_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a3] = "Garage_door_opener_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a4] = "Garage_door_opener_operating_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a5] = "Ignition_key_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a6] = "Left_front_seat_ventilation_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a7] = "Right_front_seat_ventilation_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a8] = "Left_rear_seat_ventilation_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07a9] = "Right_rear_seat_ventilation_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07aa] = "Data_medium_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07ab] = "Drivers_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07ac] = "Front_passengers_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07ad] = "Left_headlamp_power_output_stage_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07ae] = "Right_headlamp_power_output_stage_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07af] = "Sensor_for_anti_theft_alarm_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b0] = "Rear_lid_control_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b1] = "Alarm_horn_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b2] = "Automatic_day_night_interior_mirror_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b3] = "Sun_roof_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b4] = "Steering_column_lock_actuator_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b5] = "Anti_theft_tilt_system_control_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b6] = "Tire_pressure_monitor_antenna_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b7] = "Heated_windshield_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b8] = "Rear_light_left_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07b9] = "Ceiling_light_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07ba] = "Left_front_massage_seat_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07bb] = "Right_front_massage_seat_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07bc] = "Control_module_for_auxiliary_air_heater_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07bd] = "Ioniser_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07be] = "Multi_function_steering_wheel_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07bf] = "Left_rear_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c0] = "Right_rear_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c1] = "Left_rear_massage_seat_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c2] = "Right_rear_massage_seat_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c3] = "Display_unit_1_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c4] = "Battery_monitoring_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c5] = "Roof_blind_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c6] = "Sun_roof_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c7] = "Display_unit_2_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c8] = "Telephone_handset_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07c9] = "Traffic_data_aerial_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07ca] = "Chip_card_reader_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07cb] = "Hands_free_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07cc] = "Telephone_handset_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07cd] = "Display_unit_front_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07ce] = "Multimedia_operating_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x07cf] = "Digital_sound_system_control_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x0902] = "Activation of Development CAN-Messages"
UDS_RDBI.dataIdentifiers[0x2a26] = "Gateway Component List present"
UDS_RDBI.dataIdentifiers[0x2a27] = "Gateway_Component_List_Sleepindication"
UDS_RDBI.dataIdentifiers[0x2a28] = "Gateway Component List dtc"
UDS_RDBI.dataIdentifiers[0x2a29] = "Gateway Component List DiagProt"
UDS_RDBI.dataIdentifiers[0x2a2d] = "Gateway_component_list_databus_identification"
UDS_RDBI.dataIdentifiers[0x2ee0] = "Gateway_component_list_diag_path"
UDS_RDBI.dataIdentifiers[0x2ee1] = "Gateway_component_list_ecu_authentication"
UDS_RDBI.dataIdentifiers[0x3610] = "Electrically_adjustable_steering_column_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3611] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3612] = "Rear_spoiler_adjustment_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3613] = "Roof_blind_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3614] = "Motor_for_wind_deflector_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3615] = "Voltage_stabilizer_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3616] = "Switch_module_for_driver_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3617] = "Switch_module_for_front_passenger_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3618] = "Switch_module_for_rear_seat_driver_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3619] = "Switch_module_for_rear_seat_front_passenger_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x361a] = "Switch_module_2_for_driver_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x361b] = "Switch_module_2_for_front_passenger_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x361c] = "Switch_module_2_for_rear_seat_front_passenger_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x361d] = "Compact_disc_database_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3629] = "LED_headlamp_powermodule_2_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x362a] = "LED_headlamp_powermodule_2_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x362c] = "Multimedia_operating_unit_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x362e] = "Data_medium_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x362f] = "Analog_clock_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3630] = "Relative_Air_Humidity_Interior_Sender_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3631] = "Sensor_controlled_power_rear_lid_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3632] = "Battery_monitoring_control_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3633] = "Air_conditioning_compressor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3634] = "Control_module_for_auxiliary_blower_motors_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3635] = "High_beam_powermodule_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3636] = "High_beam_powermodule_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3637] = "Coolant_heater_Coding_Values"
UDS_RDBI.dataIdentifiers[0x3640] = "Electrically_adjustable_steering_column_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3641] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3642] = "Rear_spoiler_adjustment_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3643] = "Roof_blind_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3644] = "Motor_for_wind_deflector_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3645] = "Voltage_stabilizer_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3646] = "Switch_module_for_driver_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3647] = "Switch_module_for_front_passenger_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3648] = "Switch_module_for_rear_seat_driver_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3649] = "Switch_module_for_rear_seat_front_passenger_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x364a] = "Switch_module_2_for_driver_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x364b] = "Switch_module_2_for_front_passenger_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x364c] = "Switch_module_2_for_rear_seat_front_passenger_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x364d] = "Compact_disc_database_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3659] = "LED_headlamp_powermodule_2_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x365a] = "LED_headlamp_powermodule_2_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x365c] = "Multimedia_operating_unit_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x365e] = "Data_medium_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x365f] = "Analog_clock_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3660] = "Relative_Air_Humidity_Interior_Sender_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3661] = "Sensor_controlled_power_rear_lid_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3662] = "Battery_monitoring_control_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3663] = "Air_conditioning_compressor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3664] = "Control_module_for_auxiliary_blower_motors_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3665] = "High_beam_powermodule_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3666] = "High_beam_powermodule_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3667] = "Coolant_heater_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x3670] = "Electrically_adjustable_steering_column_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3671] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3672] = "Rear_spoiler_adjustment_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3673] = "Roof_blind_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3674] = "Motor_for_wind_deflector_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3675] = "Voltage_stabilizer_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3676] = "Switch_module_for_driver_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3677] = "Switch_module_for_front_passenger_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3678] = "Switch_module_for_rear_seat_driver_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3679] = "Switch_module_for_rear_seat_front_passenger_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x367a] = "Switch_module_2_for_driver_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x367b] = "Switch_module_2_for_front_passenger_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x367c] = "Switch_module_2_for_rear_seat_front_passenger_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x367d] = "Compact_disc_database_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3689] = "LED_headlamp_powermodule_2_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x368a] = "LED_headlamp_powermodule_2_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x368c] = "Multimedia_operating_unit_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x368e] = "Data_medium_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x368f] = "Analog_clock_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3690] = "Relative_Air_Humidity_Interior_Sender_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3691] = "Sensor_controlled_power_rear_lid_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3692] = "Battery_monitoring_control_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3693] = "Air_conditioning_compressor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3694] = "Control_module_for_auxiliary_blower_motors_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3695] = "High_beam_powermodule_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3696] = "High_beam_powermodule_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x3697] = "Coolant_heater_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x36a0] = "Electrically_adjustable_steering_column_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a1] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a2] = "Rear_spoiler_adjustment_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a3] = "Roof_blind_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a4] = "Motor_for_wind_deflector_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a5] = "Voltage_stabilizer_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a6] = "Switch_module_for_driver_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a7] = "Switch_module_for_front_passenger_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a8] = "Switch_module_for_rear_seat_driver_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36a9] = "Switch_module_for_rear_seat_front_passenger_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36aa] = "Switch_module_2_for_driver_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36ab] = "Switch_module_2_for_front_passenger_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36ac] = "Switch_module_2_for_rear_seat_front_passenger_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36ad] = "Compact_disc_database_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36b9] = "LED_headlamp_powermodule_2_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36ba] = "LED_headlamp_powermodule_2_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36bc] = "Multimedia_operating_unit_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36be] = "Data_medium_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36bf] = "Analog_clock_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36c0] = "Relative_Air_Humidity_Interior_Sender_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36c1] = "Sensor_controlled_power_rear_lid_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36c2] = "Battery_monitoring_control_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36c3] = "Air_conditioning_compressor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36c4] = "Control_module_for_auxiliary_blower_motors_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36c5] = "High_beam_powermodule_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36c6] = "High_beam_powermodule_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36c7] = "Coolant_heater_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x36d0] = "Electrically_adjustable_steering_column_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d1] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d2] = "Rear_spoiler_adjustment_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d3] = "Roof_blind_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d4] = "Motor_for_wind_deflector_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d5] = "Voltage_stabilizer_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d6] = "Switch_module_for_driver_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d7] = "Switch_module_for_front_passenger_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d8] = "Switch_module_for_rear_seat_driver_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36d9] = "Switch_module_for_rear_seat_front_passenger_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36da] = "Switch_module_2_for_driver_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36db] = "Switch_module_2_for_front_passenger_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36dc] = "Switch_module_2_for_rear_seat_front_passenger_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36dd] = "Compact_disc_database_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36e9] = "LED_headlamp_powermodule_2_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36ea] = "LED_headlamp_powermodule_2_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36ec] = "Multimedia_operating_unit_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36ee] = "Data_medium_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36ef] = "Analog_clock_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36f0] = "Relative_Air_Humidity_Interior_Sender_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36f1] = "Sensor_controlled_power_rear_lid_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36f2] = "Battery_monitoring_control_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36f3] = "Air_conditioning_compressor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36f4] = "Control_module_for_auxiliary_blower_motors_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36f5] = "High_beam_powermodule_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36f6] = "High_beam_powermodule_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x36f7] = "Coolant_heater_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x3700] = "Electrically_adjustable_steering_column_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3701] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3702] = "Rear_spoiler_adjustment_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3703] = "Roof_blind_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3704] = "Motor_for_wind_deflector_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3705] = "Voltage_stabilizer_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3706] = "Switch_module_for_driver_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3707] = "Switch_module_for_front_passenger_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3708] = "Switch_module_for_rear_seat_driver_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3709] = "Switch_module_for_rear_seat_front_passenger_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x370a] = "Switch_module_2_for_driver_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x370b] = "Switch_module_2_for_front_passenger_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x370c] = "Switch_module_2_for_rear_seat_front_passenger_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x370d] = "Compact_disc_database_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3719] = "LED_headlamp_powermodule_2_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x371a] = "LED_headlamp_powermodule_2_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x371c] = "Multimedia_operating_unit_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x371e] = "Data_medium_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x371f] = "Analog_clock_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3720] = "Relative_Air_Humidity_Interior_Sender_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3721] = "Sensor_controlled_power_rear_lid_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3722] = "Battery_monitoring_control_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3723] = "Air_conditioning_compressor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3724] = "Control_module_for_auxiliary_blower_motors_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3725] = "High_beam_powermodule_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3726] = "High_beam_powermodule_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3727] = "Coolant_heater_Serial_Number"
UDS_RDBI.dataIdentifiers[0x3730] = "Electrically_adjustable_steering_column_System_Name"
UDS_RDBI.dataIdentifiers[0x3731] = "Relative_humidity_sensor_in_fresh_air_intake_duct_System_Name"
UDS_RDBI.dataIdentifiers[0x3732] = "Rear_spoiler_adjustment_System_Name"
UDS_RDBI.dataIdentifiers[0x3733] = "Roof_blind_2_System_Name"
UDS_RDBI.dataIdentifiers[0x3734] = "Motor_for_wind_deflector_System_Name"
UDS_RDBI.dataIdentifiers[0x3735] = "Voltage_stabilizer_System_Name"
UDS_RDBI.dataIdentifiers[0x3736] = "Switch_module_for_driver_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x3737] = "Switch_module_for_front_passenger_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x3738] = "Switch_module_for_rear_seat_driver_side_System_Name"
UDS_RDBI.dataIdentifiers[0x3739] = "Switch_module_for_rear_seat_front_passenger_side_System_Name"
UDS_RDBI.dataIdentifiers[0x373a] = "Switch_module_2_for_driver_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x373b] = "Switch_module_2_for_front_passenger_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x373c] = "Switch_module_2_for_rear_seat_front_passenger_side_System_Name"
UDS_RDBI.dataIdentifiers[0x373d] = "Compact_disc_database_System_Name"
UDS_RDBI.dataIdentifiers[0x3749] = "LED_headlamp_powermodule_2_left_System_Name"
UDS_RDBI.dataIdentifiers[0x374a] = "LED_headlamp_powermodule_2_right_System_Name"
UDS_RDBI.dataIdentifiers[0x374c] = "Multimedia_operating_unit_2_System_Name"
UDS_RDBI.dataIdentifiers[0x374e] = "Data_medium_2_System_Name"
UDS_RDBI.dataIdentifiers[0x374f] = "Analog_clock_System_Name"
UDS_RDBI.dataIdentifiers[0x3750] = "Relative_Air_Humidity_Interior_Sender_System_Name"
UDS_RDBI.dataIdentifiers[0x3751] = "Sensor_controlled_power_rear_lid_System_Name"
UDS_RDBI.dataIdentifiers[0x3752] = "Battery_monitoring_control_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x3753] = "Air_conditioning_compressor_System_Name"
UDS_RDBI.dataIdentifiers[0x3754] = "Control_module_for_auxiliary_blower_motors_System_Name"
UDS_RDBI.dataIdentifiers[0x3755] = "High_beam_powermodule_left_System_Name"
UDS_RDBI.dataIdentifiers[0x3756] = "High_beam_powermodule_right_System_Name"
UDS_RDBI.dataIdentifiers[0x3757] = "Coolant_heater_System_Name"
UDS_RDBI.dataIdentifiers[0x37a0] = "Electrically_adjustable_steering_column_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a1] = "Relative_humidity_sensor_in_fresh_air_intake_duct_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a2] = "Rear_spoiler_adjustment_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a3] = "Roof_blind_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a4] = "Motor_for_wind_deflector_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a5] = "Voltage_stabilizer_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a6] = "Switch_module_for_driver_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a7] = "Switch_module_for_front_passenger_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a8] = "Switch_module_for_rear_seat_driver_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37a9] = "Switch_module_for_rear_seat_front_passenger_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37aa] = "Switch_module_2_for_driver_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37ab] = "Switch_module_2_for_front_passenger_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37ac] = "Switch_module_2_for_rear_seat_front_passenger_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37ad] = "Compact_disc_database_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37b9] = "LED_headlamp_powermodule_2_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37ba] = "LED_headlamp_powermodule_2_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37bc] = "Multimedia_operating_unit_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37be] = "Data_medium_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37bf] = "Analog_clock_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37c0] = "Relative_Air_Humidity_Interior_Sender_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37c1] = "Sensor_controlled_power_rear_lid_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37c2] = "Battery_monitoring_control_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37c3] = "Air_conditioning_compressor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37c4] = "Control_module_for_auxiliary_blower_motors_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37c5] = "High_beam_powermodule_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37c6] = "High_beam_powermodule_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x37c7] = "Coolant_heater_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x5867] = "In_use_monitor_performance_ratio_1"
UDS_RDBI.dataIdentifiers[0x5868] = "In_use_monitor_performance_ratio_2"
UDS_RDBI.dataIdentifiers[0x5869] = "In_use_monitor_performance_ratio_3"
UDS_RDBI.dataIdentifiers[0x6001] = "Control_unit_for_wiper_motor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6002] = "Rain_light_recognition_sensor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6003] = "Light_switch_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6004] = "Garage_door_opener_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6005] = "Garage_door_opener_operating_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6006] = "Ignition_key_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6007] = "Left_front_seat_ventilation_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6008] = "Right_front_seat_ventilation_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6009] = "Left_rear_seat_ventilation_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x600a] = "LED_headlamp_powermodule_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x600b] = "LED_headlamp_powermodule_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x600c] = "LED_headlamp_powermodule_2_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x600d] = "LED_headlamp_powermodule_2_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x600e] = "Operating_and_display_unit_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x600f] = "Operating_and_display_unit_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6010] = "Right_rear_seat_ventilation_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6011] = "Data_medium_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6012] = "Drivers_door_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6013] = "Front_passengers_door_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6014] = "Left_headlamp_power_output_stage_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6015] = "Right_headlamp_power_output_stage_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6016] = "Sensor_for_anti_theft_alarm_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6017] = "Rear_lid_control_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6018] = "Alarm_horn_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6019] = "Automatic_day_night_interior_mirror_Coding_Values"
UDS_RDBI.dataIdentifiers[0x601a] = "Remote_control_auxiliary_heater_Coding_Values"
UDS_RDBI.dataIdentifiers[0x601b] = "Fresh_air_blower_front_Coding_Values"
UDS_RDBI.dataIdentifiers[0x601c] = "Fresh_air_blower_back_Coding_Values"
UDS_RDBI.dataIdentifiers[0x601d] = "Alternator_Coding_Values"
UDS_RDBI.dataIdentifiers[0x601e] = "Interior_light_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x601f] = "Refrigerant_pressure_and_temperature_sender_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6020] = "Sun_roof_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6021] = "Steering_column_lock_actuator_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6022] = "Anti_theft_tilt_system_control_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6023] = "Tire_pressure_monitor_antenna_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6024] = "Heated_windshield_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6025] = "Rear_light_left_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6026] = "Ceiling_light_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6027] = "Left_front_massage_seat_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6028] = "Right_front_massage_seat_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6029] = "Control_module_for_auxiliary_air_heater_Coding_Values"
UDS_RDBI.dataIdentifiers[0x602a] = "Belt Pretensioner left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x602b] = "Belt Pretensioner right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x602c] = "Occupant Detection_Coding_Values"
UDS_RDBI.dataIdentifiers[0x602d] = "Selector_lever_Coding_Values"
UDS_RDBI.dataIdentifiers[0x602e] = "NOx_sensor_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x602f] = "NOx_sensor_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6030] = "Ioniser_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6031] = "Multi_function_steering_wheel_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6032] = "Left_rear_door_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6033] = "Right_rear_door_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6034] = "Left_rear_massage_seat_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6035] = "Right_rear_massage_seat_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6036] = "Display_unit_1_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6037] = "Battery_monitoring_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6038] = "Roof_blind_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6039] = "Sun_roof_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x603a] = "Steering_angle_sender_Coding_Values"
UDS_RDBI.dataIdentifiers[0x603b] = "Lane_change_assistant 2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x603c] = "Pitch_rate_sender_Coding_Values"
UDS_RDBI.dataIdentifiers[0x603d] = "ESP_sensor_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x603e] = "Electronic_ignition_lock_Coding_Values"
UDS_RDBI.dataIdentifiers[0x603f] = "Air_quality_sensor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6040] = "Display_unit_2_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6041] = "Telephone_handset_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6042] = "Chip_card_reader_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6043] = "Traffic_data_aerial_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6044] = "Hands_free_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6045] = "Telephone_handset_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6046] = "Display_unit_front_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6047] = "Multimedia_operating_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6048] = "Digital_sound_system_control_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6049] = "Electrically_adjustable_steering_column_Coding_Values"
UDS_RDBI.dataIdentifiers[0x604a] = "Interface_for_external_multimedia_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x604b] = "Relative_Air_Humidity_Interior_Sender_Coding_Values"
UDS_RDBI.dataIdentifiers[0x604c] = "Drivers_door_rear_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x604d] = "Passengers_rear_door_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x604e] = "Sensor_controlled_power_rear_lid_Coding_Values"
UDS_RDBI.dataIdentifiers[0x604f] = "Camera_for_night_vision_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6050] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6051] = "Rear_spoiler_adjustment_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6052] = "Roof_blind_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6053] = "Motor_for_wind_deflector_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6054] = "Voltage_stabilizer_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6055] = "Switch_module_for_driver_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6056] = "Switch_module_for_front_passenger_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6057] = "Switch_module_for_rear_seat_driver_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6058] = "Switch_module_for_rear_seat_front_passenger_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6059] = "Switch_module_2_for_driver_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x605a] = "Battery_charger_unit_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x605b] = "Battery_charger_unit_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x605c] = "Battery_charger_unit_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x605d] = "Air_conditioning_compressor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x605e] = "Neck_heating_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x605f] = "Neck_heating_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6060] = "Switch_module_2_for_front_passenger_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6061] = "Switch_module_2_for_rear_seat_front_passenger_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6062] = "Compact_disc_database_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6063] = "Rear_climatronic_operating_and_display_unit_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6064] = "Rear_climatronic_operating_and_display_unit_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6065] = "Door_handle_front_left_Kessy_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6066] = "Door_handle_front_right_Kessy_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6067] = "Door_handle_rear_left_Kessy_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6068] = "Door_handle_rear_right_Kessy_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6069] = "Power_converter_DC_AC_Coding_Values"
UDS_RDBI.dataIdentifiers[0x606a] = "Battery_monitoring_control_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x606b] = "Matrix_headlamp_powermodule_1_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x606c] = "Matrix_headlamp_powermodule_1_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x606d] = "High_beam_powermodule_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x606e] = "High_beam_powermodule_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x606f] = "Air_suspension_compressor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6070] = "Rear_brake_actuator_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6071] = "Rear_brake_actuator_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6072] = "Analog_clock_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6073] = "Rear_door_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6079] = "Data_medium_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x607a] = "Operating_unit_center_console_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x607b] = "Operating_unit_center_console_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x607c] = "Operating_unit_center_console_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x607d] = "Operating_unit_center_console_4_Coding_Values"
UDS_RDBI.dataIdentifiers[0x607e] = "Interface_for_radiodisplay_Coding_Values"
UDS_RDBI.dataIdentifiers[0x607f] = "Parkassist_entry_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6086] = "Belt_pretensioner_3rd_row_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6087] = "Belt_pretensioner_3rd_row_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6088] = "Injection_valve_heater_control_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6089] = "Steering_column_switch_Coding_Values"
UDS_RDBI.dataIdentifiers[0x608a] = "Brake_assistance_Coding_Values"
UDS_RDBI.dataIdentifiers[0x608b] = "Trailer_articulation_angle_sensor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x608c] = "Cup_holder_with_heater_and_cooling_element_Coding_Values"
UDS_RDBI.dataIdentifiers[0x608d] = "Range_of_vision_sensing_Coding_Values"
UDS_RDBI.dataIdentifiers[0x608e] = "Convenience_and_driver_assist_operating_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x608f] = "Cradle_rear_climatronic_operating_and_display_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6090] = "Trailer_weight_nose_weight_detection_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6091] = "Sensor_carbon_dioxide_concentration_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6092] = "Sensor_fine_dust_concentration_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6093] = "Volume_control_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6094] = "Belt_buckle_presenter_2nd_row_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6095] = "Belt_buckle_presenter_2nd_row_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6096] = "Operating_and_display_unit_6_for_air_conditioning_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6097] = "Active_accelerator_pedal_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6098] = "Multimedia_operating_unit_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6099] = "Display_unit_3_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x609a] = "Display_unit_4_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x609b] = "Display_unit_5_for_multimedia_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x609c] = "Control_module_for_auxiliary_blower_motors_Coding_Values"
UDS_RDBI.dataIdentifiers[0x609d] = "Operating_and_display_unit_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x609e] = "Operating_and_display_unit_4_Coding_Values"
UDS_RDBI.dataIdentifiers[0x609f] = "Operating_and_display_unit_5_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a0] = "Side Sensor Driver Front_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a1] = "Side Sensor Passenger Front_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a2] = "Side Sensor Driver Rear_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a3] = "Side Sensor Passenger Rear_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a4] = "Front Sensor Driver_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a5] = "Front Sensor Passenger_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a6] = "Pedestrian Protection Driver_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a7] = "Pedestrian Protection Passenger_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a8] = "Rear Sensor Center_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60a9] = "Pedestrian Protection Center_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60aa] = "Pedestrian Protection Contact_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ab] = "Pedestrian_protection_driver_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ac] = "Pedestrian_protection_passenger_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ad] = "Central_sensor_XY_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ae] = "Refrigerant_pressure_and_temperature_sender_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60af] = "Refrigerant_pressure_and_temperature_sender_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b0] = "Switch_for_rear_multicontour_seat_driver_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b1] = "Valve_block_1_in_driver_side_rear_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b2] = "Valve_block_2_in_driver_side_rear_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b3] = "Valve_block_3_in_driver_side_rear_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b4] = "Switch_for_rear_multicontour_seat_passenger_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b5] = "Valve_block_1_in_passenger_side_rear_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b6] = "Valve_block_2_in_passenger_side_rear_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b7] = "Valve_block_3_in_passenger_side_rear_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b8] = "Switch_for_front_multicontour_seat_driver_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60b9] = "Valve_block_1_in_driver_side_front_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ba] = "Valve_block_2_in_driver_side_front_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60bb] = "Valve_block_3_in_driver_side_front_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60bc] = "Switch_for_front_multicontour_seat_passenger_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60bd] = "Valve_block_1_in_passenger_side_front_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60be] = "Valve_block_2_in_passenger_side_front_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60bf] = "Valve_block_3_in_passenger_side_front_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c0] = "Coolant_heater_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c1] = "Seat_backrest_fan_1_front_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c2] = "Seat_backrest_fan_2_front_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c3] = "Seat_cushion_fan_1_front_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c4] = "Seat_cushion_fan_2_front_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c5] = "Seat_backrest_fan_1_front_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c6] = "Seat_backrest_fan_2_front_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c7] = "Seat_cushion_fan_1_front_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c8] = "Seat_cushion_fan_2_front_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60c9] = "Operating_and_display_unit_1_for_air_conditioning_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ca] = "Operating_and_display_unit_2_for_air_conditioning_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60cb] = "Operating_and_display_unit_3_for_air_conditioning_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60cc] = "Operating_and_display_unit_4_for_air_conditioning_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60cd] = "Operating_and_display_unit_5_for_air_conditioning_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ce] = "Pedestrian_protection_left_hand_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60cf] = "Pedestrian_protection_right_hand_side_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d0] = "Battery_junction_box_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d1] = "Cell_module_controller_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d2] = "Cell_module_controller_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d3] = "Cell_module_controller_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d4] = "Cell_module_controller_4_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d5] = "Cell_module_controller_5_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d6] = "Cell_module_controller_6_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d7] = "Cell_module_controller_7_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d8] = "Cell_module_controller_8_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60d9] = "Cell_module_controller_9_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60da] = "Cell_module_controller_10_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60db] = "Cell_module_controller_11_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60dc] = "Cell_module_controller_12_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60dd] = "Seat_backrest_fan_1_rear_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60de] = "Seat_backrest_fan_2_rear_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60df] = "Seat_cushion_fan_1_rear_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e0] = "Seat_cushion_fan_2_rear_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e1] = "Seat_backrest_fan_1_rear_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e2] = "Seat_backrest_fan_2_rear_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e3] = "Seat_cushion_fan_1_rear_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e4] = "Seat_cushion_fan_2_rear_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e5] = "Auxiliary_blower_motor_control_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e6] = "Auxiliary_blower_motor_control_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e7] = "Infrared_sender_for_front_observation_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e8] = "Starter_generator_control_module_sub_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60e9] = "Media_player_1_sub_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ea] = "Media_player_2_sub_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60eb] = "Dedicated_short_range_communication_aerial_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ec] = "Refrigerant_pressure_and_temperature_sender_4_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ed] = "Refrigerant_pressure_and_temperature_sender_5_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ee] = "Refrigerant_pressure_and_temperature_sender_6_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ef] = "Air_coolant_actuator_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f0] = "Air_coolant_actuator_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f1] = "Cell_module_controller_13_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f2] = "Cell_module_controller_14_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f3] = "Cell_module_controller_15_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f5] = "Seat_heating_rear_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f6] = "LED_warning_indicator_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f7] = "Automatic_transmission_fluid_pump_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f8] = "Manual_transmission_fluid_pump_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60f9] = "Convenience_and_driver_assist_operating_unit_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60fb] = "Air_coolant_actuator_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60fc] = "Valve_block_4_in_driver_side_rear_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60fd] = "Valve_block_4_in_passenger_side_rear_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60fe] = "Valve_block_4_in_driver_side_front_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x60ff] = "Valve_block_4_in_passenger_side_front_seat_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6101] = "Rear_climatronic_operating_and_display_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6102] = "Refrigerant_expansion_valve_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6103] = "Refrigerant_expansion_valve_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6104] = "Refrigerant_expansion_valve_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6105] = "Refrigerant_shut_off_valve_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6106] = "Refrigerant_shut_off_valve_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6107] = "Refrigerant_shut_off_valve_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6108] = "Refrigerant_shut_off_valve_4_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6109] = "Refrigerant_shut_off_valve_5_Coding_Values"
UDS_RDBI.dataIdentifiers[0x610a] = "Sunlight_sensor_Coding_Values"
UDS_RDBI.dataIdentifiers[0x610b] = "Near_field_communication_control_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x610c] = "Clutch_control_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x610d] = "Electrical_charger_Coding_Values"
UDS_RDBI.dataIdentifiers[0x610e] = "Rear_light_left_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x610f] = "Rear_light_right_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6110] = "Rear_light_right_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6111] = "Sunlight_sensor_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6112] = "Radiator_shutter_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6113] = "Radiator_shutter_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6114] = "Radiator_shutter_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6115] = "Radiator_shutter_4_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6118] = "Special_key_operating_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6119] = "Radio_interface_Coding_Values"
UDS_RDBI.dataIdentifiers[0x611a] = "Video_self_protection_recorder_Coding_Values"
UDS_RDBI.dataIdentifiers[0x611b] = "Special_vehicle_assist_interface_Coding_Values"
UDS_RDBI.dataIdentifiers[0x611c] = "Electric_system_disconnection_diode_Coding_Values"
UDS_RDBI.dataIdentifiers[0x611d] = "Cradle_rear_climatronic_operating_and_display_unit_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x611e] = "Belt_pretensioner_2nd_row_left_Coding_Values"
UDS_RDBI.dataIdentifiers[0x611f] = "Belt_pretensioner_2nd_row_right_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6120] = "Electrical_variable_camshaft_phasing_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6121] = "Electrical_variable_camshaft_phasing_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6122] = "Wireless_operating_unit_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6123] = "Wireless_operating_unit_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6124] = "Front_windshield_washer_pump_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6125] = "Air_quality_sensor_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6126] = "Fragrancing_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6127] = "Coolant_valve_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6128] = "Near_field_communication_control_module_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6129] = "Interior_monitoring_rear_Coding_Values"
UDS_RDBI.dataIdentifiers[0x612a] = "Cooler_fan_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x612b] = "Control_unit_heating_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x612c] = "Control_unit_heating_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x612d] = "Control_unit_heating_3_Coding_Values"
UDS_RDBI.dataIdentifiers[0x612e] = "Control_unit_heating_4_Coding_Values"
UDS_RDBI.dataIdentifiers[0x612f] = "Operating_unit_drive_mode_selection_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6130] = "Side_sensor_a-pillar_driver_front_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6131] = "Side_sensor_a-pillar_passenger_front_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6132] = "Sensor_high_voltage_system_1_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6133] = "Side_sensor_b-pillar_driver_front_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6134] = "Side_sensor_b-pillar_passenger_front_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6135] = "Multi_function_steering_wheel_control_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6136] = "Gear_selection_display_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6137] = "Cooler_fan_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6138] = "Gear_selector_control_module_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6139] = "Interior_light_module_2_Coding_Values"
UDS_RDBI.dataIdentifiers[0x613a] = "Radio_control_center_Coding_Values"
UDS_RDBI.dataIdentifiers[0x613b] = "Multimedia_extension_Coding_Values"
UDS_RDBI.dataIdentifiers[0x613c] = "Control_unit_differential_lock_Coding_Values"
UDS_RDBI.dataIdentifiers[0x613d] = "Control_unit_ride_control_system_Coding_Values"
UDS_RDBI.dataIdentifiers[0x613e] = "Control_unit_hands_on_detection_steering_wheel_Coding_Values"
UDS_RDBI.dataIdentifiers[0x613f] = "Front_climatronic_operating_and_display_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6140] = "Auxiliary_display_unit_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6141] = "Card_reader_tv_tuner_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6142] = "Park_lock_actuator_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6143] = "Media_connector_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6144] = "Catalyst_heating_Coding_Values"
UDS_RDBI.dataIdentifiers[0x6201] = "Control_unit_for_wiper_motor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6202] = "Rain_light_recognition_sensor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6203] = "Light_switch_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6204] = "Garage_door_opener_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6205] = "Garage_door_opener_operating_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6206] = "Ignition_key_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6207] = "Left_front_seat_ventilation_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6208] = "Right_front_seat_ventilation_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6209] = "Left_rear_seat_ventilation_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x620a] = "LED_headlamp_powermodule_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x620b] = "LED_headlamp_powermodule_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x620c] = "LED_headlamp_powermodule_2_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x620d] = "LED_headlamp_powermodule_2_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x620e] = "Operating_and_display_unit_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x620f] = "Operating_and_display_unit_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6210] = "Right_rear_seat_ventilation_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6211] = "Data_medium_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6212] = "Drivers_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6213] = "Front_passengers_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6214] = "Left_headlamp_power_output_stage_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6215] = "Right_headlamp_power_output_stage_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6216] = "Sensor_for_anti_theft_alarm_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6217] = "Rear_lid_control_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6218] = "Alarm_horn_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6219] = "Automatic_day_night_interior_mirror_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x621a] = "Remote_control_auxiliary_heater_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x621b] = "Fresh_air_blower_front_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x621c] = "Fresh_air_blower_back_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x621d] = "Alternator_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x621e] = "Interior_light_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x621f] = "Refrigerant_pressure_and_temperature_sender_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6220] = "Sun_roof_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6221] = "Steering_column_lock_actuator_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6222] = "Anti_theft_tilt_system_control_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6223] = "Tire_pressure_monitor_antenna_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6224] = "Heated_windshield_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6225] = "Rear_light_left_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6226] = "Ceiling_light_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6227] = "Left_front_massage_seat_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6228] = "Right_front_massage_seat_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6229] = "Control_module_for_auxiliary_air_heater_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x622a] = "Belt Pretensioner left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x622b] = "Belt Pretensioner right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x622c] = "Occupant Detection_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x622d] = "Selector_lever_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x622e] = "NOx_sensor_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x622f] = "NOx_sensor_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6230] = "Ioniser_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6231] = "Multi_function_steering_wheel_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6232] = "Left_rear_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6233] = "Right_rear_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6234] = "Left_rear_massage_seat_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6235] = "Right_rear_massage_seat_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6236] = "Display_unit_1_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6237] = "Battery_monitoring_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6238] = "Roof_blind_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6239] = "Sun_roof_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x623a] = "Steering_angle_sender_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x623b] = "Lane_change_assistant 2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x623c] = "Pitch_rate_sender_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x623d] = "ESP_sensor_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x623e] = "Electronic_ignition_lock_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x623f] = "Air_quality_sensor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6240] = "Display_unit_2_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6241] = "Telephone_handset_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6242] = "Chip_card_reader_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6243] = "Traffic_data_aerial_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6244] = "Hands_free_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6245] = "Telephone_handset_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6246] = "Display_unit_front_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6247] = "Multimedia_operating_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6248] = "Digital_sound_system_control_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6249] = "Electrically_adjustable_steering_column_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x624a] = "Interface_for_external_multimedia_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x624b] = "Relative_Air_Humidity_Interior_Sender_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x624c] = "Drivers_door_rear_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x624d] = "Passengers_rear_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x624e] = "Sensor_controlled_power_rear_lid_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x624f] = "Camera_for_night_vision_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6250] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6251] = "Rear_spoiler_adjustment_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6252] = "Roof_blind_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6253] = "Motor_for_wind_deflector_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6254] = "Voltage_stabilizer_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6255] = "Switch_module_for_driver_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6256] = "Switch_module_for_front_passenger_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6257] = "Switch_module_for_rear_seat_driver_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6258] = "Switch_module_for_rear_seat_front_passenger_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6259] = "Switch_module_2_for_driver_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x625a] = "Battery_charger_unit_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x625b] = "Battery_charger_unit_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x625c] = "Battery_charger_unit_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x625d] = "Air_conditioning_compressor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x625e] = "Neck_heating_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x625f] = "Neck_heating_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6260] = "Switch_module_2_for_front_passenger_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6261] = "Switch_module_2_for_rear_seat_front_passenger_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6262] = "Compact_disc_database_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6263] = "Rear_climatronic_operating_and_display_unit_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6264] = "Rear_climatronic_operating_and_display_unit_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6265] = "Door_handle_front_left_Kessy_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6266] = "Door_handle_front_right_Kessy_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6267] = "Door_handle_rear_left_Kessy_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6268] = "Door_handle_rear_right_Kessy_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6269] = "Power_converter_DC_AC_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x626a] = "Battery_monitoring_control_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x626b] = "Matrix_headlamp_powermodule_1_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x626c] = "Matrix_headlamp_powermodule_1_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x626d] = "High_beam_powermodule_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x626e] = "High_beam_powermodule_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x626f] = "Air_suspension_compressor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6270] = "Rear_brake_actuator_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6271] = "Rear_brake_actuator_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6272] = "Analog_clock_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6273] = "Rear_door_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6279] = "Data_medium_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x627a] = "Operating_unit_center_console_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x627b] = "Operating_unit_center_console_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x627c] = "Operating_unit_center_console_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x627d] = "Operating_unit_center_console_4_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x627e] = "Interface_for_radiodisplay_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x627f] = "Parkassist_entry_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6286] = "Belt_pretensioner_3rd_row_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6287] = "Belt_pretensioner_3rd_row_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6288] = "Injection_valve_heater_control_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6289] = "Steering_column_switch_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x628a] = "Brake_assistance_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x628b] = "Trailer_articulation_angle_sensor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x628c] = "Cup_holder_with_heater_and_cooling_element_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x628d] = "Range_of_vision_sensing_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x628e] = "Convenience_and_driver_assist_operating_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x628f] = "Cradle_rear_climatronic_operating_and_display_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6290] = "Trailer_weight_nose_weight_detection_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6291] = "Sensor_carbon_dioxide_concentration_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6292] = "Sensor_fine_dust_concentration_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6293] = "Volume_control_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6294] = "Belt_buckle_presenter_2nd_row_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6295] = "Belt_buckle_presenter_2nd_row_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6296] = "Operating_and_display_unit_6_for_air_conditioning_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6297] = "Active_accelerator_pedal_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6298] = "Multimedia_operating_unit_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6299] = "Display_unit_3_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x629a] = "Display_unit_4_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x629b] = "Display_unit_5_for_multimedia_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x629c] = "Control_module_for_auxiliary_blower_motors_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x629d] = "Operating_and_display_unit_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x629e] = "Operating_and_display_unit_4_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x629f] = "Operating_and_display_unit_5_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a0] = "Side Sensor Driver Front_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a1] = "Side Sensor Passenger Front_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a2] = "Side Sensor Driver Rear_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a3] = "Side Sensor Passenger Rear_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a4] = "Front Sensor Driver_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a5] = "Front Sensor Passenger_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a6] = "Pedestrian Protection Driver_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a7] = "Pedestrian Protection Passenger_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a8] = "Rear Sensor Center_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62a9] = "Pedestrian Protection Center_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62aa] = "Pedestrian Protection Contact_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ab] = "Pedestrian_protection_driver_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ac] = "Pedestrian_protection_passenger_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ad] = "Central_sensor_XY_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ae] = "Refrigerant_pressure_and_temperature_sender_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62af] = "Refrigerant_pressure_and_temperature_sender_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b0] = "Switch_for_rear_multicontour_seat_driver_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b1] = "Valve_block_1_in_driver_side_rear_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b2] = "Valve_block_2_in_driver_side_rear_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b3] = "Valve_block_3_in_driver_side_rear_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b4] = "Switch_for_rear_multicontour_seat_passenger_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b5] = "Valve_block_1_in_passenger_side_rear_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b6] = "Valve_block_2_in_passenger_side_rear_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b7] = "Valve_block_3_in_passenger_side_rear_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b8] = "Switch_for_front_multicontour_seat_driver_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62b9] = "Valve_block_1_in_driver_side_front_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ba] = "Valve_block_2_in_driver_side_front_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62bb] = "Valve_block_3_in_driver_side_front_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62bc] = "Switch_for_front_multicontour_seat_passenger_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62bd] = "Valve_block_1_in_passenger_side_front_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62be] = "Valve_block_2_in_passenger_side_front_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62bf] = "Valve_block_3_in_passenger_side_front_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c0] = "Coolant_heater_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c1] = "Seat_backrest_fan_1_front_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c2] = "Seat_backrest_fan_2_front_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c3] = "Seat_cushion_fan_1_front_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c4] = "Seat_cushion_fan_2_front_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c5] = "Seat_backrest_fan_1_front_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c6] = "Seat_backrest_fan_2_front_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c7] = "Seat_cushion_fan_1_front_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c8] = "Seat_cushion_fan_2_front_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62c9] = "Operating_and_display_unit_1_for_air_conditioning_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ca] = "Operating_and_display_unit_2_for_air_conditioning_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62cb] = "Operating_and_display_unit_3_for_air_conditioning_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62cc] = "Operating_and_display_unit_4_for_air_conditioning_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62cd] = "Operating_and_display_unit_5_for_air_conditioning_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ce] = "Pedestrian_protection_left_hand_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62cf] = "Pedestrian_protection_right_hand_side_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d0] = "Battery_junction_box_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d1] = "Cell_module_controller_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d2] = "Cell_module_controller_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d3] = "Cell_module_controller_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d4] = "Cell_module_controller_4_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d5] = "Cell_module_controller_5_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d6] = "Cell_module_controller_6_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d7] = "Cell_module_controller_7_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d8] = "Cell_module_controller_8_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62d9] = "Cell_module_controller_9_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62da] = "Cell_module_controller_10_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62db] = "Cell_module_controller_11_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62dc] = "Cell_module_controller_12_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62dd] = "Seat_backrest_fan_1_rear_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62de] = "Seat_backrest_fan_2_rear_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62df] = "Seat_cushion_fan_1_rear_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e0] = "Seat_cushion_fan_2_rear_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e1] = "Seat_backrest_fan_1_rear_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e2] = "Seat_backrest_fan_2_rear_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e3] = "Seat_cushion_fan_1_rear_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e4] = "Seat_cushion_fan_2_rear_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e5] = "Auxiliary_blower_motor_control_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e6] = "Auxiliary_blower_motor_control_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e7] = "Infrared_sender_for_front_observation_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e8] = "Starter_generator_control_module_sub_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62e9] = "Media_player_1_sub_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ea] = "Media_player_2_sub_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62eb] = "Dedicated_short_range_communication_aerial_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ec] = "Refrigerant_pressure_and_temperature_sender_4_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ed] = "Refrigerant_pressure_and_temperature_sender_5_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ee] = "Refrigerant_pressure_and_temperature_sender_6_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ef] = "Air_coolant_actuator_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f0] = "Air_coolant_actuator_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f1] = "Cell_module_controller_13_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f2] = "Cell_module_controller_14_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f3] = "Cell_module_controller_15_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f5] = "Seat_heating_rear_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f6] = "LED_warning_indicator_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f7] = "Automatic_transmission_fluid_pump_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f8] = "Manual_transmission_fluid_pump_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62f9] = "Convenience_and_driver_assist_operating_unit_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62fb] = "Air_coolant_actuator_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62fc] = "Valve_block_4_in_driver_side_rear_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62fd] = "Valve_block_4_in_passenger_side_rear_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62fe] = "Valve_block_4_in_driver_side_front_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x62ff] = "Valve_block_4_in_passenger_side_front_seat_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6301] = "Rear_climatronic_operating_and_display_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6302] = "Refrigerant_expansion_valve_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6303] = "Refrigerant_expansion_valve_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6304] = "Refrigerant_expansion_valve_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6305] = "Refrigerant_shut_off_valve_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6306] = "Refrigerant_shut_off_valve_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6307] = "Refrigerant_shut_off_valve_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6308] = "Refrigerant_shut_off_valve_4_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6309] = "Refrigerant_shut_off_valve_5_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x630a] = "Sunlight_sensor_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x630b] = "Near_field_communication_control_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x630c] = "Clutch_control_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x630d] = "Electrical_charger_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x630e] = "Rear_light_left_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x630f] = "Rear_light_right_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6310] = "Rear_light_right_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6311] = "Sunlight_sensor_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6312] = "Radiator_shutter_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6313] = "Radiator_shutter_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6314] = "Radiator_shutter_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6315] = "Radiator_shutter_4_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6318] = "Special_key_operating_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6319] = "Radio_interface_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x631a] = "Video_self_protection_recorder_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x631b] = "Special_vehicle_assist_interface_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x631c] = "Electric_system_disconnection_diode_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x631d] = "Cradle_rear_climatronic_operating_and_display_unit_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x631e] = "Belt_pretensioner_2nd_row_left_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x631f] = "Belt_pretensioner_2nd_row_right_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6320] = "Electrical_variable_camshaft_phasing_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6321] = "Electrical_variable_camshaft_phasing_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6322] = "Wireless_operating_unit_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6323] = "Wireless_operating_unit_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6324] = "Front_windshield_washer_pump_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6325] = "Air_quality_sensor_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6326] = "Fragrancing_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6327] = "Coolant_valve_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6328] = "Near_field_communication_control_module_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6329] = "Interior_monitoring_rear_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x632a] = "Cooler_fan_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x632b] = "Control_unit_heating_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x632c] = "Control_unit_heating_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x632d] = "Control_unit_heating_3_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x632e] = "Control_unit_heating_4_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x632f] = "Operating_unit_drive_mode_selection_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6330] = "Side_sensor_a-pillar_driver_front_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6331] = "Side_sensor_a-pillar_passenger_front_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6332] = "Sensor_high_voltage_system_1_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6333] = "Side_sensor_b-pillar_driver_front_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6334] = "Side_sensor_b-pillar_passenger_front_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6335] = "Multi_function_steering_wheel_control_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6336] = "Gear_selection_display_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6337] = "Cooler_fan_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6338] = "Gear_selector_control_module_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6339] = "Interior_light_module_2_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x633a] = "Radio_control_center_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x633b] = "Multimedia_extension_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x633c] = "Control_unit_differential_lock_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x633d] = "Control_unit_ride_control_system_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x633e] = "Control_unit_hands_on_detection_steering_wheel_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x633f] = "Front_climatronic_operating_and_display_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6340] = "Auxiliary_display_unit_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6341] = "Card_reader_tv_tuner_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6342] = "Park_lock_actuator_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6343] = "Media_connector_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6344] = "Catalyst_heating_Spare_Part_Number"
UDS_RDBI.dataIdentifiers[0x6401] = "Control_unit_for_wiper_motor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6402] = "Rain_light_recognition_sensor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6403] = "Light_switch_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6404] = "Garage_door_opener_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6405] = "Garage_door_opener_operating_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6406] = "Ignition_key_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6407] = "Left_front_seat_ventilation_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6408] = "Right_front_seat_ventilation_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6409] = "Left_rear_seat_ventilation_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x640a] = "LED_headlamp_powermodule_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x640b] = "LED_headlamp_powermodule_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x640c] = "LED_headlamp_powermodule_2_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x640d] = "LED_headlamp_powermodule_2_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x640e] = "Operating_and_display_unit_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x640f] = "Operating_and_display_unit_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6410] = "Right_rear_seat_ventilation_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6411] = "Data_medium_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6412] = "Drivers_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6413] = "Front_passengers_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6414] = "Left_headlamp_power_output_stage_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6415] = "Right_headlamp_power_output_stage_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6416] = "Sensor_for_anti_theft_alarm_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6417] = "Rear_lid_control_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6418] = "Alarm_horn_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6419] = "Automatic_day_night_interior_mirror_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x641a] = "Remote_control_auxiliary_heater_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x641b] = "Fresh_air_blower_front_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x641c] = "Fresh_air_blower_back_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x641d] = "Alternator_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x641e] = "Interior_light_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x641f] = "Refrigerant_pressure_and_temperature_sender_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6420] = "Sun_roof_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6421] = "Steering_column_lock_actuator_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6422] = "Anti_theft_tilt_system_control_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6423] = "Tire_pressure_monitor_antenna_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6424] = "Heated_windshield_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6425] = "Rear_light_left_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6426] = "Ceiling_light_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6427] = "Left_front_massage_seat_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6428] = "Right_front_massage_seat_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6429] = "Control_module_for_auxiliary_air_heater_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x642a] = "Belt Pretensioner left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x642b] = "Belt Pretensioner right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x642c] = "Occupant Detection_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x642d] = "Selector_lever_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x642e] = "NOx_sensor_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x642f] = "NOx_sensor_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6430] = "Ioniser_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6431] = "Multi_function_steering_wheel_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6432] = "Left_rear_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6433] = "Right_rear_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6434] = "Left_rear_massage_seat_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6435] = "Right_rear_massage_seat_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6436] = "Display_unit_1_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6437] = "Battery_monitoring_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6438] = "Roof_blind_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6439] = "Sun_roof_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x643a] = "Steering_angle_sender_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x643b] = "Lane_change_assistant 2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x643c] = "Pitch_rate_sender_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x643d] = "ESP_sensor_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x643e] = "Electronic_ignition_lock_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x643f] = "Air_quality_sensor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6440] = "Display_unit_2_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6441] = "Telephone_handset_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6442] = "Chip_card_reader_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6443] = "Traffic_data_aerial_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6444] = "Hands_free_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6445] = "Telephone_handset_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6446] = "Display_unit_front_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6447] = "Multimedia_operating_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6448] = "Digital_sound_system_control_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6449] = "Electrically_adjustable_steering_column_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x644a] = "Interface_for_external_multimedia_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x644b] = "Relative_Air_Humidity_Interior_Sender_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x644c] = "Drivers_door_rear_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x644d] = "Passengers_rear_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x644e] = "Sensor_controlled_power_rear_lid_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x644f] = "Camera_for_night_vision_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6450] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6451] = "Rear_spoiler_adjustment_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6452] = "Roof_blind_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6453] = "Motor_for_wind_deflector_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6454] = "Voltage_stabilizer_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6455] = "Switch_module_for_driver_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6456] = "Switch_module_for_front_passenger_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6457] = "Switch_module_for_rear_seat_driver_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6458] = "Switch_module_for_rear_seat_front_passenger_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6459] = "Switch_module_2_for_driver_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x645a] = "Battery_charger_unit_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x645b] = "Battery_charger_unit_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x645c] = "Battery_charger_unit_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x645d] = "Air_conditioning_compressor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x645e] = "Neck_heating_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x645f] = "Neck_heating_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6460] = "Switch_module_2_for_front_passenger_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6461] = "Switch_module_2_for_rear_seat_front_passenger_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6462] = "Compact_disc_database_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6463] = "Rear_climatronic_operating_and_display_unit_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6464] = "Rear_climatronic_operating_and_display_unit_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6465] = "Door_handle_front_left_Kessy_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6466] = "Door_handle_front_right_Kessy_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6467] = "Door_handle_rear_left_Kessy_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6468] = "Door_handle_rear_right_Kessy_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6469] = "Power_converter_DC_AC_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x646a] = "Battery_monitoring_control_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x646b] = "Matrix_headlamp_powermodule_1_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x646c] = "Matrix_headlamp_powermodule_1_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x646d] = "High_beam_powermodule_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x646e] = "High_beam_powermodule_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x646f] = "Air_suspension_compressor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6470] = "Rear_brake_actuator_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6471] = "Rear_brake_actuator_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6472] = "Analog_clock_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6473] = "Rear_door_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6479] = "Data_medium_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x647a] = "Operating_unit_center_console_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x647b] = "Operating_unit_center_console_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x647c] = "Operating_unit_center_console_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x647d] = "Operating_unit_center_console_4_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x647e] = "Interface_for_radiodisplay_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x647f] = "Parkassist_entry_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6486] = "Belt_pretensioner_3rd_row_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6487] = "Belt_pretensioner_3rd_row_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6488] = "Injection_valve_heater_control_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6489] = "Steering_column_switch_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x648a] = "Brake_assistance_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x648b] = "Trailer_articulation_angle_sensor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x648c] = "Cup_holder_with_heater_and_cooling_element_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x648d] = "Range_of_vision_sensing_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x648e] = "Convenience_and_driver_assist_operating_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x648f] = "Cradle_rear_climatronic_operating_and_display_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6490] = "Trailer_weight_nose_weight_detection_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6491] = "Sensor_carbon_dioxide_concentration_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6492] = "Sensor_fine_dust_concentration_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6493] = "Volume_control_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6494] = "Belt_buckle_presenter_2nd_row_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6495] = "Belt_buckle_presenter_2nd_row_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6496] = "Operating_and_display_unit_6_for_air_conditioning_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6497] = "Active_accelerator_pedal_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6498] = "Multimedia_operating_unit_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6499] = "Display_unit_3_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x649a] = "Display_unit_4_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x649b] = "Display_unit_5_for_multimedia_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x649c] = "Control_module_for_auxiliary_blower_motors_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x649d] = "Operating_and_display_unit_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x649e] = "Operating_and_display_unit_4_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x649f] = "Operating_and_display_unit_5_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a0] = "Side Sensor Driver Front_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a1] = "Side Sensor Passenger Front_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a2] = "Side Sensor Driver Rear_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a3] = "Side Sensor Passenger Rear_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a4] = "Front Sensor Driver_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a5] = "Front Sensor Passenger_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a6] = "Pedestrian Protection Driver_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a7] = "Pedestrian Protection Passenger_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a8] = "Rear Sensor Center_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64a9] = "Pedestrian Protection Center_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64aa] = "Pedestrian Protection Contact_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ab] = "Pedestrian_protection_driver_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ac] = "Pedestrian_protection_passenger_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ad] = "Central_sensor_XY_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ae] = "Refrigerant_pressure_and_temperature_sender_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64af] = "Refrigerant_pressure_and_temperature_sender_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b0] = "Switch_for_rear_multicontour_seat_driver_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b1] = "Valve_block_1_in_driver_side_rear_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b2] = "Valve_block_2_in_driver_side_rear_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b3] = "Valve_block_3_in_driver_side_rear_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b4] = "Switch_for_rear_multicontour_seat_passenger_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b5] = "Valve_block_1_in_passenger_side_rear_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b6] = "Valve_block_2_in_passenger_side_rear_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b7] = "Valve_block_3_in_passenger_side_rear_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b8] = "Switch_for_front_multicontour_seat_driver_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64b9] = "Valve_block_1_in_driver_side_front_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ba] = "Valve_block_2_in_driver_side_front_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64bb] = "Valve_block_3_in_driver_side_front_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64bc] = "Switch_for_front_multicontour_seat_passenger_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64bd] = "Valve_block_1_in_passenger_side_front_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64be] = "Valve_block_2_in_passenger_side_front_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64bf] = "Valve_block_3_in_passenger_side_front_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c0] = "Coolant_heater_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c1] = "Seat_backrest_fan_1_front_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c2] = "Seat_backrest_fan_2_front_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c3] = "Seat_cushion_fan_1_front_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c4] = "Seat_cushion_fan_2_front_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c5] = "Seat_backrest_fan_1_front_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c6] = "Seat_backrest_fan_2_front_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c7] = "Seat_cushion_fan_1_front_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c8] = "Seat_cushion_fan_2_front_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64c9] = "Operating_and_display_unit_1_for_air_conditioning_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ca] = "Operating_and_display_unit_2_for_air_conditioning_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64cb] = "Operating_and_display_unit_3_for_air_conditioning_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64cc] = "Operating_and_display_unit_4_for_air_conditioning_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64cd] = "Operating_and_display_unit_5_for_air_conditioning_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ce] = "Pedestrian_protection_left_hand_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64cf] = "Pedestrian_protection_right_hand_side_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d0] = "Battery_junction_box_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d1] = "Cell_module_controller_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d2] = "Cell_module_controller_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d3] = "Cell_module_controller_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d4] = "Cell_module_controller_4_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d5] = "Cell_module_controller_5_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d6] = "Cell_module_controller_6_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d7] = "Cell_module_controller_7_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d8] = "Cell_module_controller_8_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64d9] = "Cell_module_controller_9_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64da] = "Cell_module_controller_10_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64db] = "Cell_module_controller_11_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64dc] = "Cell_module_controller_12_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64dd] = "Seat_backrest_fan_1_rear_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64de] = "Seat_backrest_fan_2_rear_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64df] = "Seat_cushion_fan_1_rear_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e0] = "Seat_cushion_fan_2_rear_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e1] = "Seat_backrest_fan_1_rear_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e2] = "Seat_backrest_fan_2_rear_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e3] = "Seat_cushion_fan_1_rear_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e4] = "Seat_cushion_fan_2_rear_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e5] = "Auxiliary_blower_motor_control_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e6] = "Auxiliary_blower_motor_control_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e7] = "Infrared_sender_for_front_observation_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e8] = "Starter_generator_control_module_sub_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64e9] = "Media_player_1_sub_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ea] = "Media_player_2_sub_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64eb] = "Dedicated_short_range_communication_aerial_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ec] = "Refrigerant_pressure_and_temperature_sender_4_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ed] = "Refrigerant_pressure_and_temperature_sender_5_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ee] = "Refrigerant_pressure_and_temperature_sender_6_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ef] = "Air_coolant_actuator_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f0] = "Air_coolant_actuator_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f1] = "Cell_module_controller_13_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f2] = "Cell_module_controller_14_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f3] = "Cell_module_controller_15_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f5] = "Seat_heating_rear_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f6] = "LED_warning_indicator_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f7] = "Automatic_transmission_fluid_pump_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f8] = "Manual_transmission_fluid_pump_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64f9] = "Convenience_and_driver_assist_operating_unit_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64fb] = "Air_coolant_actuator_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64fc] = "Valve_block_4_in_driver_side_rear_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64fd] = "Valve_block_4_in_passenger_side_rear_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64fe] = "Valve_block_4_in_driver_side_front_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x64ff] = "Valve_block_4_in_passenger_side_front_seat_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6501] = "Rear_climatronic_operating_and_display_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6502] = "Refrigerant_expansion_valve_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6503] = "Refrigerant_expansion_valve_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6504] = "Refrigerant_expansion_valve_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6505] = "Refrigerant_shut_off_valve_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6506] = "Refrigerant_shut_off_valve_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6507] = "Refrigerant_shut_off_valve_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6508] = "Refrigerant_shut_off_valve_4_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6509] = "Refrigerant_shut_off_valve_5_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x650a] = "Sunlight_sensor_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x650b] = "Near_field_communication_control_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x650c] = "Clutch_control_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x650d] = "Electrical_charger_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x650e] = "Rear_light_left_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x650f] = "Rear_light_right_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6510] = "Rear_light_right_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6511] = "Sunlight_sensor_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6512] = "Radiator_shutter_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6513] = "Radiator_shutter_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6514] = "Radiator_shutter_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6515] = "Radiator_shutter_4_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6518] = "Special_key_operating_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6519] = "Radio_interface_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x651a] = "Video_self_protection_recorder_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x651b] = "Special_vehicle_assist_interface_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x651c] = "Electric_system_disconnection_diode_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x651d] = "Cradle_rear_climatronic_operating_and_display_unit_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x651e] = "Belt_pretensioner_2nd_row_left_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x651f] = "Belt_pretensioner_2nd_row_right_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6520] = "Electrical_variable_camshaft_phasing_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6521] = "Electrical_variable_camshaft_phasing_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6522] = "Wireless_operating_unit_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6523] = "Wireless_operating_unit_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6524] = "Front_windshield_washer_pump_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6525] = "Air_quality_sensor_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6526] = "Fragrancing_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6527] = "Coolant_valve_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6528] = "Near_field_communication_control_module_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6529] = "Interior_monitoring_rear_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x652a] = "Cooler_fan_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x652b] = "Control_unit_heating_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x652c] = "Control_unit_heating_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x652d] = "Control_unit_heating_3_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x652e] = "Control_unit_heating_4_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x652f] = "Operating_unit_drive_mode_selection_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6530] = "Side_sensor_a-pillar_driver_front_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6531] = "Side_sensor_a-pillar_passenger_front_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6532] = "Sensor_high_voltage_system_1_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6533] = "Side_sensor_b-pillar_driver_front_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6534] = "Side_sensor_b-pillar_passenger_front_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6535] = "Multi_function_steering_wheel_control_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6536] = "Gear_selection_display_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6537] = "Cooler_fan_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6538] = "Gear_selector_control_module_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6539] = "Interior_light_module_2_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x653a] = "Radio_control_center_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x653b] = "Multimedia_extension_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x653c] = "Control_unit_differential_lock_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x653d] = "Control_unit_ride_control_system_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x653e] = "Control_unit_hands_on_detection_steering_wheel_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x653f] = "Front_climatronic_operating_and_display_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6540] = "Auxiliary_display_unit_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6541] = "Card_reader_tv_tuner_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6542] = "Park_lock_actuator_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6543] = "Media_connector_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6544] = "Catalyst_heating_Application_Software_Version_Number"
UDS_RDBI.dataIdentifiers[0x6601] = "Control_unit_for_wiper_motor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6602] = "Rain_light_recognition_sensor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6603] = "Light_switch_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6604] = "Garage_door_opener_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6605] = "Garage_door_opener_operating_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6606] = "Ignition_key_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6607] = "Left_front_seat_ventilation_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6608] = "Right_front_seat_ventilation_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6609] = "Left_rear_seat_ventilation_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x660a] = "LED_headlamp_powermodule_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x660b] = "LED_headlamp_powermodule_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x660c] = "LED_headlamp_powermodule_2_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x660d] = "LED_headlamp_powermodule_2_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x660e] = "Operating_and_display_unit_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x660f] = "Operating_and_display_unit_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6610] = "Right_rear_seat_ventilation_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6611] = "Data_medium_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6612] = "Drivers_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6613] = "Front_passengers_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6614] = "Left_headlamp_power_output_stage_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6615] = "Right_headlamp_power_output_stage_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6616] = "Sensor_for_anti_theft_alarm_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6617] = "Rear_lid_control_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6618] = "Alarm_horn_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6619] = "Automatic_day_night_interior_mirror_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x661a] = "Remote_control_auxiliary_heater_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x661b] = "Fresh_air_blower_front_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x661c] = "Fresh_air_blower_back_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x661d] = "Alternator_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x661e] = "Interior_light_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x661f] = "Refrigerant_pressure_and_temperature_sender_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6620] = "Sun_roof_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6621] = "Steering_column_lock_actuator_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6622] = "Anti_theft_tilt_system_control_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6623] = "Tire_pressure_monitor_antenna_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6624] = "Heated_windshield_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6625] = "Rear_light_left_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6626] = "Ceiling_light_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6627] = "Left_front_massage_seat_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6628] = "Right_front_massage_seat_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6629] = "Control_module_for_auxiliary_air_heater_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x662a] = "Belt Pretensioner left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x662b] = "Belt Pretensioner right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x662c] = "Occupant Detection_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x662d] = "Selector_lever_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x662e] = "NOx_sensor_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x662f] = "NOx_sensor_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6630] = "Ioniser_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6631] = "Multi_function_steering_wheel_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6632] = "Left_rear_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6633] = "Right_rear_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6634] = "Left_rear_massage_seat_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6635] = "Right_rear_massage_seat_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6636] = "Display_unit_1_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6637] = "Battery_monitoring_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6638] = "Roof_blind_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6639] = "Sun_roof_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x663a] = "Steering_angle_sender_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x663b] = "Lane_change_assistant 2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x663c] = "Pitch_rate_sender_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x663d] = "ESP_sensor_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x663e] = "Electronic_ignition_lock_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x663f] = "Air_quality_sensor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6640] = "Display_unit_2_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6641] = "Telephone_handset_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6642] = "Chip_card_reader_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6643] = "Traffic_data_aerial_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6644] = "Hands_free_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6645] = "Telephone_handset_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6646] = "Display_unit_front_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6647] = "Multimedia_operating_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6648] = "Digital_sound_system_control_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6649] = "Electrically_adjustable_steering_column_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x664a] = "Interface_for_external_multimedia_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x664b] = "Relative_Air_Humidity_Interior_Sender_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x664c] = "Drivers_door_rear_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x664d] = "Passengers_rear_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x664e] = "Sensor_controlled_power_rear_lid_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x664f] = "Camera_for_night_vision_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6650] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6651] = "Rear_spoiler_adjustment_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6652] = "Roof_blind_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6653] = "Motor_for_wind_deflector_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6654] = "Voltage_stabilizer_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6655] = "Switch_module_for_driver_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6656] = "Switch_module_for_front_passenger_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6657] = "Switch_module_for_rear_seat_driver_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6658] = "Switch_module_for_rear_seat_front_passenger_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6659] = "Switch_module_2_for_driver_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x665a] = "Battery_charger_unit_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x665b] = "Battery_charger_unit_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x665c] = "Battery_charger_unit_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x665d] = "Air_conditioning_compressor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x665e] = "Neck_heating_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x665f] = "Neck_heating_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6660] = "Switch_module_2_for_front_passenger_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6661] = "Switch_module_2_for_rear_seat_front_passenger_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6662] = "Compact_disc_database_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6663] = "Rear_climatronic_operating_and_display_unit_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6664] = "Rear_climatronic_operating_and_display_unit_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6665] = "Door_handle_front_left_Kessy_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6666] = "Door_handle_front_right_Kessy_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6667] = "Door_handle_rear_left_Kessy_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6668] = "Door_handle_rear_right_Kessy_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6669] = "Power_converter_DC_AC_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x666a] = "Battery_monitoring_control_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x666b] = "Matrix_headlamp_powermodule_1_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x666c] = "Matrix_headlamp_powermodule_1_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x666d] = "High_beam_powermodule_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x666e] = "High_beam_powermodule_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x666f] = "Air_suspension_compressor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6670] = "Rear_brake_actuator_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6671] = "Rear_brake_actuator_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6672] = "Analog_clock_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6673] = "Rear_door_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6679] = "Data_medium_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x667a] = "Operating_unit_center_console_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x667b] = "Operating_unit_center_console_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x667c] = "Operating_unit_center_console_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x667d] = "Operating_unit_center_console_4_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x667e] = "Interface_for_radiodisplay_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x667f] = "Parkassist_entry_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6686] = "Belt_pretensioner_3rd_row_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6687] = "Belt_pretensioner_3rd_row_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6688] = "Injection_valve_heater_control_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6689] = "Steering_column_switch_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x668a] = "Brake_assistance_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x668b] = "Trailer_articulation_angle_sensor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x668c] = "Cup_holder_with_heater_and_cooling_element_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x668d] = "Range_of_vision_sensing_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x668e] = "Convenience_and_driver_assist_operating_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x668f] = "Cradle_rear_climatronic_operating_and_display_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6690] = "Trailer_weight_nose_weight_detection_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6691] = "Sensor_carbon_dioxide_concentration_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6692] = "Sensor_fine_dust_concentration_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6693] = "Volume_control_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6694] = "Belt_buckle_presenter_2nd_row_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6695] = "Belt_buckle_presenter_2nd_row_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6696] = "Operating_and_display_unit_6_for_air_conditioning_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6697] = "Active_accelerator_pedal_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6698] = "Multimedia_operating_unit_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6699] = "Display_unit_3_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x669a] = "Display_unit_4_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x669b] = "Display_unit_5_for_multimedia_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x669c] = "Control_module_for_auxiliary_blower_motors_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x669d] = "Operating_and_display_unit_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x669e] = "Operating_and_display_unit_4_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x669f] = "Operating_and_display_unit_5_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a0] = "Side Sensor Driver Front_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a1] = "Side Sensor Passenger Front_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a2] = "Side Sensor Driver Rear_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a3] = "Side Sensor Passenger Rear_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a4] = "Front Sensor Driver_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a5] = "Front Sensor Passenger_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a6] = "Pedestrian Protection Driver_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a7] = "Pedestrian Protection Passenger_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a8] = "Rear Sensor Center_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66a9] = "Pedestrian Protection Center_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66aa] = "Pedestrian Protection Contact_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ab] = "Pedestrian_protection_driver_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ac] = "Pedestrian_protection_passenger_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ad] = "Central_sensor_XY_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ae] = "Refrigerant_pressure_and_temperature_sender_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66af] = "Refrigerant_pressure_and_temperature_sender_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b0] = "Switch_for_rear_multicontour_seat_driver_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b1] = "Valve_block_1_in_driver_side_rear_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b2] = "Valve_block_2_in_driver_side_rear_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b3] = "Valve_block_3_in_driver_side_rear_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b4] = "Switch_for_rear_multicontour_seat_passenger_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b5] = "Valve_block_1_in_passenger_side_rear_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b6] = "Valve_block_2_in_passenger_side_rear_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b7] = "Valve_block_3_in_passenger_side_rear_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b8] = "Switch_for_front_multicontour_seat_driver_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66b9] = "Valve_block_1_in_driver_side_front_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ba] = "Valve_block_2_in_driver_side_front_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66bb] = "Valve_block_3_in_driver_side_front_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66bc] = "Switch_for_front_multicontour_seat_passenger_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66bd] = "Valve_block_1_in_passenger_side_front_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66be] = "Valve_block_2_in_passenger_side_front_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66bf] = "Valve_block_3_in_passenger_side_front_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c0] = "Coolant_heater_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c1] = "Seat_backrest_fan_1_front_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c2] = "Seat_backrest_fan_2_front_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c3] = "Seat_cushion_fan_1_front_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c4] = "Seat_cushion_fan_2_front_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c5] = "Seat_backrest_fan_1_front_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c6] = "Seat_backrest_fan_2_front_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c7] = "Seat_cushion_fan_1_front_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c8] = "Seat_cushion_fan_2_front_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66c9] = "Operating_and_display_unit_1_for_air_conditioning_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ca] = "Operating_and_display_unit_2_for_air_conditioning_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66cb] = "Operating_and_display_unit_3_for_air_conditioning_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66cc] = "Operating_and_display_unit_4_for_air_conditioning_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66cd] = "Operating_and_display_unit_5_for_air_conditioning_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ce] = "Pedestrian_protection_left_hand_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66cf] = "Pedestrian_protection_right_hand_side_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d0] = "Battery_junction_box_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d1] = "Cell_module_controller_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d2] = "Cell_module_controller_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d3] = "Cell_module_controller_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d4] = "Cell_module_controller_4_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d5] = "Cell_module_controller_5_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d6] = "Cell_module_controller_6_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d7] = "Cell_module_controller_7_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d8] = "Cell_module_controller_8_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66d9] = "Cell_module_controller_9_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66da] = "Cell_module_controller_10_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66db] = "Cell_module_controller_11_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66dc] = "Cell_module_controller_12_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66dd] = "Seat_backrest_fan_1_rear_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66de] = "Seat_backrest_fan_2_rear_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66df] = "Seat_cushion_fan_1_rear_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e0] = "Seat_cushion_fan_2_rear_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e1] = "Seat_backrest_fan_1_rear_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e2] = "Seat_backrest_fan_2_rear_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e3] = "Seat_cushion_fan_1_rear_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e4] = "Seat_cushion_fan_2_rear_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e5] = "Auxiliary_blower_motor_control_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e6] = "Auxiliary_blower_motor_control_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e7] = "Infrared_sender_for_front_observation_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e8] = "Starter_generator_control_module_sub_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66e9] = "Media_player_1_sub_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ea] = "Media_player_2_sub_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66eb] = "Dedicated_short_range_communication_aerial_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ec] = "Refrigerant_pressure_and_temperature_sender_4_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ed] = "Refrigerant_pressure_and_temperature_sender_5_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ee] = "Refrigerant_pressure_and_temperature_sender_6_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ef] = "Air_coolant_actuator_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f0] = "Air_coolant_actuator_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f1] = "Cell_module_controller_13_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f2] = "Cell_module_controller_14_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f3] = "Cell_module_controller_15_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f5] = "Seat_heating_rear_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f6] = "LED_warning_indicator_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f7] = "Automatic_transmission_fluid_pump_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f8] = "Manual_transmission_fluid_pump_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66f9] = "Convenience_and_driver_assist_operating_unit_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66fb] = "Air_coolant_actuator_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66fc] = "Valve_block_4_in_driver_side_rear_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66fd] = "Valve_block_4_in_passenger_side_rear_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66fe] = "Valve_block_4_in_driver_side_front_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x66ff] = "Valve_block_4_in_passenger_side_front_seat_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6701] = "Rear_climatronic_operating_and_display_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6702] = "Refrigerant_expansion_valve_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6703] = "Refrigerant_expansion_valve_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6704] = "Refrigerant_expansion_valve_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6705] = "Refrigerant_shut_off_valve_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6706] = "Refrigerant_shut_off_valve_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6707] = "Refrigerant_shut_off_valve_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6708] = "Refrigerant_shut_off_valve_4_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6709] = "Refrigerant_shut_off_valve_5_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x670a] = "Sunlight_sensor_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x670b] = "Near_field_communication_control_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x670c] = "Clutch_control_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x670d] = "Electrical_charger_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x670e] = "Rear_light_left_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x670f] = "Rear_light_right_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6710] = "Rear_light_right_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6711] = "Sunlight_sensor_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6712] = "Radiator_shutter_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6713] = "Radiator_shutter_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6714] = "Radiator_shutter_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6715] = "Radiator_shutter_4_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6718] = "Special_key_operating_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6719] = "Radio_interface_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x671a] = "Video_self_protection_recorder_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x671b] = "Special_vehicle_assist_interface_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x671c] = "Electric_system_disconnection_diode_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x671d] = "Cradle_rear_climatronic_operating_and_display_unit_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x671e] = "Belt_pretensioner_2nd_row_left_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x671f] = "Belt_pretensioner_2nd_row_right_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6720] = "Electrical_variable_camshaft_phasing_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6721] = "Electrical_variable_camshaft_phasing_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6722] = "Wireless_operating_unit_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6723] = "Wireless_operating_unit_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6724] = "Front_windshield_washer_pump_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6725] = "Air_quality_sensor_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6726] = "Fragrancing_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6727] = "Coolant_valve_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6728] = "Near_field_communication_control_module_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6729] = "Interior_monitoring_rear_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x672a] = "Cooler_fan_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x672b] = "Control_unit_heating_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x672c] = "Control_unit_heating_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x672d] = "Control_unit_heating_3_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x672e] = "Control_unit_heating_4_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x672f] = "Operating_unit_drive_mode_selection_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6730] = "Side_sensor_a-pillar_driver_front_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6731] = "Side_sensor_a-pillar_passenger_front_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6732] = "Sensor_high_voltage_system_1_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6733] = "Side_sensor_b-pillar_driver_front_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6734] = "Side_sensor_b-pillar_passenger_front_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6735] = "Multi_function_steering_wheel_control_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6736] = "Gear_selection_display_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6737] = "Cooler_fan_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6738] = "Gear_selector_control_module_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6739] = "Interior_light_module_2_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x673a] = "Radio_control_center_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x673b] = "Multimedia_extension_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x673c] = "Control_unit_differential_lock_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x673d] = "Control_unit_ride_control_system_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x673e] = "Control_unit_hands_on_detection_steering_wheel_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x673f] = "Front_climatronic_operating_and_display_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6740] = "Auxiliary_display_unit_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6741] = "Card_reader_tv_tuner_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6742] = "Park_lock_actuator_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6743] = "Media_connector_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6744] = "Catalyst_heating_Hardware_Number"
UDS_RDBI.dataIdentifiers[0x6801] = "Control_unit_for_wiper_motor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6802] = "Rain_light_recognition_sensor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6803] = "Light_switch_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6804] = "Garage_door_opener_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6805] = "Garage_door_opener_operating_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6806] = "Ignition_key_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6807] = "Left_front_seat_ventilation_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6808] = "Right_front_seat_ventilation_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6809] = "Left_rear_seat_ventilation_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x680a] = "LED_headlamp_powermodule_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x680b] = "LED_headlamp_powermodule_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x680c] = "LED_headlamp_powermodule_2_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x680d] = "LED_headlamp_powermodule_2_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x680e] = "Operating_and_display_unit_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x680f] = "Operating_and_display_unit_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6810] = "Right_rear_seat_ventilation_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6811] = "Data_medium_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6812] = "Drivers_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6813] = "Front_passengers_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6814] = "Left_headlamp_power_output_stage_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6815] = "Right_headlamp_power_output_stage_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6816] = "Sensor_for_anti_theft_alarm_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6817] = "Rear_lid_control_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6818] = "Alarm_horn_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6819] = "Automatic_day_night_interior_mirror_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x681a] = "Remote_control_auxiliary_heater_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x681b] = "Fresh_air_blower_front_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x681c] = "Fresh_air_blower_back_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x681d] = "Alternator_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x681e] = "Interior_light_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x681f] = "Refrigerant_pressure_and_temperature_sender_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6820] = "Sun_roof_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6821] = "Steering_column_lock_actuator_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6822] = "Anti_theft_tilt_system_control_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6823] = "Tire_pressure_monitor_antenna_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6824] = "Heated_windshield_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6825] = "Rear_light_left_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6826] = "Ceiling_light_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6827] = "Left_front_massage_seat_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6828] = "Right_front_massage_seat_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6829] = "Control_module_for_auxiliary_air_heater_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x682a] = "Belt Pretensioner left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x682b] = "Belt Pretensioner right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x682c] = "Occupant Detection_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x682d] = "Selector_lever_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x682e] = "NOx_sensor_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x682f] = "NOx_sensor_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6830] = "Ioniser_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6831] = "Multi_function_steering_wheel_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6832] = "Left_rear_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6833] = "Right_rear_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6834] = "Left_rear_massage_seat_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6835] = "Right_rear_massage_seat_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6836] = "Display_unit_1_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6837] = "Battery_monitoring_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6838] = "Roof_blind_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6839] = "Sun_roof_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x683a] = "Steering_angle_sender_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x683b] = "Lane_change_assistant 2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x683c] = "Pitch_rate_sender_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x683d] = "ESP_sensor_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x683e] = "Electronic_ignition_lock_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x683f] = "Air_quality_sensor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6840] = "Display_unit_2_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6841] = "Telephone_handset_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6842] = "Chip_card_reader_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6843] = "Traffic_data_aerial_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6844] = "Hands_free_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6845] = "Telephone_handset_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6846] = "Display_unit_front_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6847] = "Multimedia_operating_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6848] = "Digital_sound_system_control_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6849] = "Electrically_adjustable_steering_column_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x684a] = "Interface_for_external_multimedia_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x684b] = "Relative_Air_Humidity_Interior_Sender_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x684c] = "Drivers_door_rear_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x684d] = "Passengers_rear_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x684e] = "Sensor_controlled_power_rear_lid_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x684f] = "Camera_for_night_vision_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6850] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6851] = "Rear_spoiler_adjustment_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6852] = "Roof_blind_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6853] = "Motor_for_wind_deflector_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6854] = "Voltage_stabilizer_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6855] = "Switch_module_for_driver_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6856] = "Switch_module_for_front_passenger_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6857] = "Switch_module_for_rear_seat_driver_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6858] = "Switch_module_for_rear_seat_front_passenger_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6859] = "Switch_module_2_for_driver_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x685a] = "Battery_charger_unit_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x685b] = "Battery_charger_unit_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x685c] = "Battery_charger_unit_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x685d] = "Air_conditioning_compressor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x685e] = "Neck_heating_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x685f] = "Neck_heating_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6860] = "Switch_module_2_for_front_passenger_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6861] = "Switch_module_2_for_rear_seat_front_passenger_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6862] = "Compact_disc_database_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6863] = "Rear_climatronic_operating_and_display_unit_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6864] = "Rear_climatronic_operating_and_display_unit_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6865] = "Door_handle_front_left_Kessy_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6866] = "Door_handle_front_right_Kessy_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6867] = "Door_handle_rear_left_Kessy_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6868] = "Door_handle_rear_right_Kessy_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6869] = "Power_converter_DC_AC_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x686a] = "Battery_monitoring_control_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x686b] = "Matrix_headlamp_powermodule_1_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x686c] = "Matrix_headlamp_powermodule_1_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x686d] = "High_beam_powermodule_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x686e] = "High_beam_powermodule_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x686f] = "Air_suspension_compressor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6870] = "Rear_brake_actuator_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6871] = "Rear_brake_actuator_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6872] = "Analog_clock_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6873] = "Rear_door_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6879] = "Data_medium_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x687a] = "Operating_unit_center_console_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x687b] = "Operating_unit_center_console_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x687c] = "Operating_unit_center_console_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x687d] = "Operating_unit_center_console_4_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x687e] = "Interface_for_radiodisplay_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x687f] = "Parkassist_entry_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6886] = "Belt_pretensioner_3rd_row_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6887] = "Belt_pretensioner_3rd_row_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6888] = "Injection_valve_heater_control_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6889] = "Steering_column_switch_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x688a] = "Brake_assistance_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x688b] = "Trailer_articulation_angle_sensor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x688c] = "Cup_holder_with_heater_and_cooling_element_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x688d] = "Range_of_vision_sensing_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x688e] = "Convenience_and_driver_assist_operating_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x688f] = "Cradle_rear_climatronic_operating_and_display_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6890] = "Trailer_weight_nose_weight_detection_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6891] = "Sensor_carbon_dioxide_concentration_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6892] = "Sensor_fine_dust_concentration_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6893] = "Volume_control_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6894] = "Belt_buckle_presenter_2nd_row_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6895] = "Belt_buckle_presenter_2nd_row_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6896] = "Operating_and_display_unit_6_for_air_conditioning_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6897] = "Active_accelerator_pedal_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6898] = "Multimedia_operating_unit_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6899] = "Display_unit_3_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x689a] = "Display_unit_4_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x689b] = "Display_unit_5_for_multimedia_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x689c] = "Control_module_for_auxiliary_blower_motors_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x689d] = "Operating_and_display_unit_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x689e] = "Operating_and_display_unit_4_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x689f] = "Operating_and_display_unit_5_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a0] = "Side Sensor Driver Front_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a1] = "Side Sensor Passenger Front_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a2] = "Side Sensor Driver Rear_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a3] = "Side Sensor Passenger Rear_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a4] = "Front Sensor Driver_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a5] = "Front Sensor Passenger_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a6] = "Pedestrian Protection Driver_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a7] = "Pedestrian Protection Passenger_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a8] = "Rear Sensor Center_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68a9] = "Pedestrian Protection Center_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68aa] = "Pedestrian Protection Contact_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ab] = "Pedestrian_protection_driver_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ac] = "Pedestrian_protection_passenger_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ad] = "Central_sensor_XY_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ae] = "Refrigerant_pressure_and_temperature_sender_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68af] = "Refrigerant_pressure_and_temperature_sender_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b0] = "Switch_for_rear_multicontour_seat_driver_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b1] = "Valve_block_1_in_driver_side_rear_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b2] = "Valve_block_2_in_driver_side_rear_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b3] = "Valve_block_3_in_driver_side_rear_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b4] = "Switch_for_rear_multicontour_seat_passenger_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b5] = "Valve_block_1_in_passenger_side_rear_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b6] = "Valve_block_2_in_passenger_side_rear_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b7] = "Valve_block_3_in_passenger_side_rear_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b8] = "Switch_for_front_multicontour_seat_driver_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68b9] = "Valve_block_1_in_driver_side_front_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ba] = "Valve_block_2_in_driver_side_front_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68bb] = "Valve_block_3_in_driver_side_front_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68bc] = "Switch_for_front_multicontour_seat_passenger_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68bd] = "Valve_block_1_in_passenger_side_front_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68be] = "Valve_block_2_in_passenger_side_front_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68bf] = "Valve_block_3_in_passenger_side_front_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c0] = "Coolant_heater_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c1] = "Seat_backrest_fan_1_front_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c2] = "Seat_backrest_fan_2_front_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c3] = "Seat_cushion_fan_1_front_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c4] = "Seat_cushion_fan_2_front_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c5] = "Seat_backrest_fan_1_front_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c6] = "Seat_backrest_fan_2_front_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c7] = "Seat_cushion_fan_1_front_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c8] = "Seat_cushion_fan_2_front_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68c9] = "Operating_and_display_unit_1_for_air_conditioning_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ca] = "Operating_and_display_unit_2_for_air_conditioning_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68cb] = "Operating_and_display_unit_3_for_air_conditioning_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68cc] = "Operating_and_display_unit_4_for_air_conditioning_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68cd] = "Operating_and_display_unit_5_for_air_conditioning_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ce] = "Pedestrian_protection_left_hand_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68cf] = "Pedestrian_protection_right_hand_side_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d0] = "Battery_junction_box_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d1] = "Cell_module_controller_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d2] = "Cell_module_controller_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d3] = "Cell_module_controller_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d4] = "Cell_module_controller_4_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d5] = "Cell_module_controller_5_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d6] = "Cell_module_controller_6_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d7] = "Cell_module_controller_7_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d8] = "Cell_module_controller_8_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68d9] = "Cell_module_controller_9_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68da] = "Cell_module_controller_10_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68db] = "Cell_module_controller_11_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68dc] = "Cell_module_controller_12_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68dd] = "Seat_backrest_fan_1_rear_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68de] = "Seat_backrest_fan_2_rear_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68df] = "Seat_cushion_fan_1_rear_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e0] = "Seat_cushion_fan_2_rear_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e1] = "Seat_backrest_fan_1_rear_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e2] = "Seat_backrest_fan_2_rear_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e3] = "Seat_cushion_fan_1_rear_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e4] = "Seat_cushion_fan_2_rear_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e5] = "Auxiliary_blower_motor_control_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e6] = "Auxiliary_blower_motor_control_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e7] = "Infrared_sender_for_front_observation_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e8] = "Starter_generator_control_module_sub_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68e9] = "Media_player_1_sub_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ea] = "Media_player_2_sub_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68eb] = "Dedicated_short_range_communication_aerial_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ec] = "Refrigerant_pressure_and_temperature_sender_4_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ed] = "Refrigerant_pressure_and_temperature_sender_5_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ee] = "Refrigerant_pressure_and_temperature_sender_6_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ef] = "Air_coolant_actuator_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f0] = "Air_coolant_actuator_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f1] = "Cell_module_controller_13_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f2] = "Cell_module_controller_14_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f3] = "Cell_module_controller_15_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f5] = "Seat_heating_rear_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f6] = "LED_warning_indicator_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f7] = "Automatic_transmission_fluid_pump_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f8] = "Manual_transmission_fluid_pump_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68f9] = "Convenience_and_driver_assist_operating_unit_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68fb] = "Air_coolant_actuator_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68fc] = "Valve_block_4_in_driver_side_rear_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68fd] = "Valve_block_4_in_passenger_side_rear_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68fe] = "Valve_block_4_in_driver_side_front_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x68ff] = "Valve_block_4_in_passenger_side_front_seat_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6901] = "Rear_climatronic_operating_and_display_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6902] = "Refrigerant_expansion_valve_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6903] = "Refrigerant_expansion_valve_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6904] = "Refrigerant_expansion_valve_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6905] = "Refrigerant_shut_off_valve_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6906] = "Refrigerant_shut_off_valve_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6907] = "Refrigerant_shut_off_valve_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6908] = "Refrigerant_shut_off_valve_4_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6909] = "Refrigerant_shut_off_valve_5_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x690a] = "Sunlight_sensor_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x690b] = "Near_field_communication_control_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x690c] = "Clutch_control_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x690d] = "Electrical_charger_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x690e] = "Rear_light_left_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x690f] = "Rear_light_right_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6910] = "Rear_light_right_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6911] = "Sunlight_sensor_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6912] = "Radiator_shutter_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6913] = "Radiator_shutter_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6914] = "Radiator_shutter_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6915] = "Radiator_shutter_4_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6918] = "Special_key_operating_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6919] = "Radio_interface_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x691a] = "Video_self_protection_recorder_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x691b] = "Special_vehicle_assist_interface_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x691c] = "Electric_system_disconnection_diode_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x691d] = "Cradle_rear_climatronic_operating_and_display_unit_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x691e] = "Belt_pretensioner_2nd_row_left_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x691f] = "Belt_pretensioner_2nd_row_right_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6920] = "Electrical_variable_camshaft_phasing_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6921] = "Electrical_variable_camshaft_phasing_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6922] = "Wireless_operating_unit_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6923] = "Wireless_operating_unit_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6924] = "Front_windshield_washer_pump_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6925] = "Air_quality_sensor_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6926] = "Fragrancing_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6927] = "Coolant_valve_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6928] = "Near_field_communication_control_module_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6929] = "Interior_monitoring_rear_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x692a] = "Cooler_fan_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x692b] = "Control_unit_heating_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x692c] = "Control_unit_heating_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x692d] = "Control_unit_heating_3_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x692e] = "Control_unit_heating_4_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x692f] = "Operating_unit_drive_mode_selection_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6930] = "Side_sensor_a-pillar_driver_front_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6931] = "Side_sensor_a-pillar_passenger_front_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6932] = "Sensor_high_voltage_system_1_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6933] = "Side_sensor_b-pillar_driver_front_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6934] = "Side_sensor_b-pillar_passenger_front_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6935] = "Multi_function_steering_wheel_control_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6936] = "Gear_selection_display_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6937] = "Cooler_fan_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6938] = "Gear_selector_control_module_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6939] = "Interior_light_module_2_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x693a] = "Radio_control_center_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x693b] = "Multimedia_extension_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x693c] = "Control_unit_differential_lock_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x693d] = "Control_unit_ride_control_system_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x693e] = "Control_unit_hands_on_detection_steering_wheel_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x693f] = "Front_climatronic_operating_and_display_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6940] = "Auxiliary_display_unit_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6941] = "Card_reader_tv_tuner_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6942] = "Park_lock_actuator_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6943] = "Media_connector_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6944] = "Catalyst_heating_Hardware_Version_Number"
UDS_RDBI.dataIdentifiers[0x6a01] = "Control_unit_for_wiper_motor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a02] = "Rain_light_recognition_sensor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a03] = "Light_switch_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a04] = "Garage_door_opener_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a05] = "Garage_door_opener_operating_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a06] = "Ignition_key_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a07] = "Left_front_seat_ventilation_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a08] = "Right_front_seat_ventilation_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a09] = "Left_rear_seat_ventilation_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a0a] = "LED_headlamp_powermodule_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a0b] = "LED_headlamp_powermodule_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a0c] = "LED_headlamp_powermodule_2_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a0d] = "LED_headlamp_powermodule_2_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a0e] = "Operating_and_display_unit_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a0f] = "Operating_and_display_unit_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a10] = "Right_rear_seat_ventilation_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a11] = "Data_medium_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a12] = "Drivers_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a13] = "Front_passengers_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a14] = "Left_headlamp_power_output_stage_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a15] = "Right_headlamp_power_output_stage_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a16] = "Sensor_for_anti_theft_alarm_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a17] = "Rear_lid_control_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a18] = "Alarm_horn_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a19] = "Automatic_day_night_interior_mirror_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a1a] = "Remote_control_auxiliary_heater_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a1b] = "Fresh_air_blower_front_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a1c] = "Fresh_air_blower_back_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a1d] = "Alternator_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a1e] = "Interior_light_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a1f] = "Refrigerant_pressure_and_temperature_sender_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a20] = "Sun_roof_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a21] = "Steering_column_lock_actuator_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a22] = "Anti_theft_tilt_system_control_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a23] = "Tire_pressure_monitor_antenna_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a24] = "Heated_windshield_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a25] = "Rear_light_left_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a26] = "Ceiling_light_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a27] = "Left_front_massage_seat_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a28] = "Right_front_massage_seat_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a29] = "Control_module_for_auxiliary_air_heater_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a2a] = "Belt Pretensioner left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a2b] = "Belt Pretensioner right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a2c] = "Occupant Detection_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a2d] = "Selector_lever_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a2e] = "NOx_sensor_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a2f] = "NOx_sensor_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a30] = "Ioniser_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a31] = "Multi_function_steering_wheel_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a32] = "Left_rear_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a33] = "Right_rear_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a34] = "Left_rear_massage_seat_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a35] = "Right_rear_massage_seat_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a36] = "Display_unit_1_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a37] = "Battery_monitoring_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a38] = "Roof_blind_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a39] = "Sun_roof_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a3a] = "Steering_angle_sender_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a3b] = "Lane_change_assistant 2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a3c] = "Pitch_rate_sender_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a3d] = "ESP_sensor_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a3e] = "Electronic_ignition_lock_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a3f] = "Air_quality_sensor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a40] = "Display_unit_2_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a41] = "Telephone_handset_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a42] = "Chip_card_reader_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a43] = "Traffic_data_aerial_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a44] = "Hands_free_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a45] = "Telephone_handset_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a46] = "Display_unit_front_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a47] = "Multimedia_operating_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a48] = "Digital_sound_system_control_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a49] = "Electrically_adjustable_steering_column_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a4a] = "Interface_for_external_multimedia_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a4b] = "Relative_Air_Humidity_Interior_Sender_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a4c] = "Drivers_door_rear_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a4d] = "Passengers_rear_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a4e] = "Sensor_controlled_power_rear_lid_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a4f] = "Camera_for_night_vision_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a50] = "Relative_humidity_sensor_in_fresh_air_intake_duct_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a51] = "Rear_spoiler_adjustment_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a52] = "Roof_blind_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a53] = "Motor_for_wind_deflector_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a54] = "Voltage_stabilizer_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a55] = "Switch_module_for_driver_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a56] = "Switch_module_for_front_passenger_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a57] = "Switch_module_for_rear_seat_driver_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a58] = "Switch_module_for_rear_seat_front_passenger_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a59] = "Switch_module_2_for_driver_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a5a] = "Battery_charger_unit_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a5b] = "Battery_charger_unit_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a5c] = "Battery_charger_unit_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a5d] = "Air_conditioning_compressor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a5e] = "Neck_heating_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a5f] = "Neck_heating_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a60] = "Switch_module_2_for_front_passenger_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a61] = "Switch_module_2_for_rear_seat_front_passenger_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a62] = "Compact_disc_database_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a63] = "Rear_climatronic_operating_and_display_unit_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a64] = "Rear_climatronic_operating_and_display_unit_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a65] = "Door_handle_front_left_Kessy_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a66] = "Door_handle_front_right_Kessy_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a67] = "Door_handle_rear_left_Kessy_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a68] = "Door_handle_rear_right_Kessy_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a69] = "Power_converter_DC_AC_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a6a] = "Battery_monitoring_control_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a6b] = "Matrix_headlamp_powermodule_1_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a6c] = "Matrix_headlamp_powermodule_1_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a6d] = "High_beam_powermodule_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a6e] = "High_beam_powermodule_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a6f] = "Air_suspension_compressor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a70] = "Rear_brake_actuator_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a71] = "Rear_brake_actuator_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a72] = "Analog_clock_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a73] = "Rear_door_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a79] = "Data_medium_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a7a] = "Operating_unit_center_console_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a7b] = "Operating_unit_center_console_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a7c] = "Operating_unit_center_console_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a7d] = "Operating_unit_center_console_4_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a7e] = "Interface_for_radiodisplay_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a7f] = "Parkassist_entry_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a86] = "Belt_pretensioner_3rd_row_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a87] = "Belt_pretensioner_3rd_row_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a88] = "Injection_valve_heater_control_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a89] = "Steering_column_switch_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a8a] = "Brake_assistance_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a8b] = "Trailer_articulation_angle_sensor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a8c] = "Cup_holder_with_heater_and_cooling_element_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a8d] = "Range_of_vision_sensing_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a8e] = "Convenience_and_driver_assist_operating_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a8f] = "Cradle_rear_climatronic_operating_and_display_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a90] = "Trailer_weight_nose_weight_detection_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a91] = "Sensor_carbon_dioxide_concentration_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a92] = "Sensor_fine_dust_concentration_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a93] = "Volume_control_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a94] = "Belt_buckle_presenter_2nd_row_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a95] = "Belt_buckle_presenter_2nd_row_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a96] = "Operating_and_display_unit_6_for_air_conditioning_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a97] = "Active_accelerator_pedal_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a98] = "Multimedia_operating_unit_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a99] = "Display_unit_3_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a9a] = "Display_unit_4_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a9b] = "Display_unit_5_for_multimedia_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a9c] = "Control_module_for_auxiliary_blower_motors_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a9d] = "Operating_and_display_unit_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a9e] = "Operating_and_display_unit_4_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6a9f] = "Operating_and_display_unit_5_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa0] = "Side Sensor Driver Front_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa1] = "Side Sensor Passenger Front_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa2] = "Side Sensor Driver Rear_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa3] = "Side Sensor Passenger Rear_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa4] = "Front Sensor Driver_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa5] = "Front Sensor Passenger_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa6] = "Pedestrian Protection Driver_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa7] = "Pedestrian Protection Passenger_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa8] = "Rear Sensor Center_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aa9] = "Pedestrian Protection Center_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aaa] = "Pedestrian Protection Contact_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aab] = "Pedestrian_protection_driver_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aac] = "Pedestrian_protection_passenger_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aad] = "Central_sensor_XY_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aae] = "Refrigerant_pressure_and_temperature_sender_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aaf] = "Refrigerant_pressure_and_temperature_sender_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab0] = "Switch_for_rear_multicontour_seat_driver_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab1] = "Valve_block_1_in_driver_side_rear_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab2] = "Valve_block_2_in_driver_side_rear_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab3] = "Valve_block_3_in_driver_side_rear_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab4] = "Switch_for_rear_multicontour_seat_passenger_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab5] = "Valve_block_1_in_passenger_side_rear_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab6] = "Valve_block_2_in_passenger_side_rear_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab7] = "Valve_block_3_in_passenger_side_rear_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab8] = "Switch_for_front_multicontour_seat_driver_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ab9] = "Valve_block_1_in_driver_side_front_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aba] = "Valve_block_2_in_driver_side_front_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6abb] = "Valve_block_3_in_driver_side_front_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6abc] = "Switch_for_front_multicontour_seat_passenger_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6abd] = "Valve_block_1_in_passenger_side_front_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6abe] = "Valve_block_2_in_passenger_side_front_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6abf] = "Valve_block_3_in_passenger_side_front_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac0] = "Coolant_heater_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac1] = "Seat_backrest_fan_1_front_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac2] = "Seat_backrest_fan_2_front_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac3] = "Seat_cushion_fan_1_front_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac4] = "Seat_cushion_fan_2_front_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac5] = "Seat_backrest_fan_1_front_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac6] = "Seat_backrest_fan_2_front_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac7] = "Seat_cushion_fan_1_front_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac8] = "Seat_cushion_fan_2_front_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ac9] = "Operating_and_display_unit_1_for_air_conditioning_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aca] = "Operating_and_display_unit_2_for_air_conditioning_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6acb] = "Operating_and_display_unit_3_for_air_conditioning_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6acc] = "Operating_and_display_unit_4_for_air_conditioning_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6acd] = "Operating_and_display_unit_5_for_air_conditioning_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ace] = "Pedestrian_protection_left_hand_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6acf] = "Pedestrian_protection_right_hand_side_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad0] = "Battery_junction_box_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad1] = "Cell_module_controller_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad2] = "Cell_module_controller_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad3] = "Cell_module_controller_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad4] = "Cell_module_controller_4_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad5] = "Cell_module_controller_5_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad6] = "Cell_module_controller_6_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad7] = "Cell_module_controller_7_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad8] = "Cell_module_controller_8_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ad9] = "Cell_module_controller_9_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ada] = "Cell_module_controller_10_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6adb] = "Cell_module_controller_11_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6adc] = "Cell_module_controller_12_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6add] = "Seat_backrest_fan_1_rear_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ade] = "Seat_backrest_fan_2_rear_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6adf] = "Seat_cushion_fan_1_rear_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae0] = "Seat_cushion_fan_2_rear_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae1] = "Seat_backrest_fan_1_rear_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae2] = "Seat_backrest_fan_2_rear_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae3] = "Seat_cushion_fan_1_rear_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae4] = "Seat_cushion_fan_2_rear_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae5] = "Auxiliary_blower_motor_control_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae6] = "Auxiliary_blower_motor_control_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae7] = "Infrared_sender_for_front_observation_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae8] = "Starter_generator_control_module_sub_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6ae9] = "Media_player_1_sub_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aea] = "Media_player_2_sub_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aeb] = "Dedicated_short_range_communication_aerial_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aec] = "Refrigerant_pressure_and_temperature_sender_4_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aed] = "Refrigerant_pressure_and_temperature_sender_5_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aee] = "Refrigerant_pressure_and_temperature_sender_6_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aef] = "Air_coolant_actuator_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af0] = "Air_coolant_actuator_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af1] = "Cell_module_controller_13_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af2] = "Cell_module_controller_14_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af3] = "Cell_module_controller_15_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af5] = "Seat_heating_rear_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af6] = "LED_warning_indicator_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af7] = "Automatic_transmission_fluid_pump_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af8] = "Manual_transmission_fluid_pump_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6af9] = "Convenience_and_driver_assist_operating_unit_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6afb] = "Air_coolant_actuator_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6afc] = "Valve_block_4_in_driver_side_rear_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6afd] = "Valve_block_4_in_passenger_side_rear_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6afe] = "Valve_block_4_in_driver_side_front_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6aff] = "Valve_block_4_in_passenger_side_front_seat_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b01] = "Rear_climatronic_operating_and_display_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b02] = "Refrigerant_expansion_valve_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b03] = "Refrigerant_expansion_valve_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b04] = "Refrigerant_expansion_valve_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b05] = "Refrigerant_shut_off_valve_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b06] = "Refrigerant_shut_off_valve_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b07] = "Refrigerant_shut_off_valve_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b08] = "Refrigerant_shut_off_valve_4_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b09] = "Refrigerant_shut_off_valve_5_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b0a] = "Sunlight_sensor_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b0b] = "Near_field_communication_control_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b0c] = "Clutch_control_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b0d] = "Electrical_charger_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b0e] = "Rear_light_left_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b0f] = "Rear_light_right_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b10] = "Rear_light_right_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b11] = "Sunlight_sensor_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b12] = "Radiator_shutter_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b13] = "Radiator_shutter_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b14] = "Radiator_shutter_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b15] = "Radiator_shutter_4_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b18] = "Special_key_operating_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b19] = "Radio_interface_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b1a] = "Video_self_protection_recorder_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b1b] = "Special_vehicle_assist_interface_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b1c] = "Electric_system_disconnection_diode_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b1d] = "Cradle_rear_climatronic_operating_and_display_unit_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b1e] = "Belt_pretensioner_2nd_row_left_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b1f] = "Belt_pretensioner_2nd_row_right_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b20] = "Electrical_variable_camshaft_phasing_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b21] = "Electrical_variable_camshaft_phasing_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b22] = "Wireless_operating_unit_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b23] = "Wireless_operating_unit_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b24] = "Front_windshield_washer_pump_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b25] = "Air_quality_sensor_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b26] = "Fragrancing_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b27] = "Coolant_valve_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b28] = "Near_field_communication_control_module_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b29] = "Interior_monitoring_rear_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b2a] = "Cooler_fan_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b2b] = "Control_unit_heating_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b2c] = "Control_unit_heating_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b2d] = "Control_unit_heating_3_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b2e] = "Control_unit_heating_4_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b2f] = "Operating_unit_drive_mode_selection_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b30] = "Side_sensor_a-pillar_driver_front_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b31] = "Side_sensor_a-pillar_passenger_front_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b32] = "Sensor_high_voltage_system_1_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b33] = "Side_sensor_b-pillar_driver_front_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b34] = "Side_sensor_b-pillar_passenger_front_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b35] = "Multi_function_steering_wheel_control_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b36] = "Gear_selection_display_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b37] = "Cooler_fan_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b38] = "Gear_selector_control_module_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b39] = "Interior_light_module_2_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b3a] = "Radio_control_center_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b3b] = "Multimedia_extension_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b3c] = "Control_unit_differential_lock_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b3d] = "Control_unit_ride_control_system_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b3e] = "Control_unit_hands_on_detection_steering_wheel_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b3f] = "Front_climatronic_operating_and_display_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b40] = "Auxiliary_display_unit_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b41] = "Card_reader_tv_tuner_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b42] = "Park_lock_actuator_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b43] = "Media_connector_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6b44] = "Catalyst_heating_Serial_Number"
UDS_RDBI.dataIdentifiers[0x6c01] = "Control_unit_for_wiper_motor_System_Name"
UDS_RDBI.dataIdentifiers[0x6c02] = "Rain_light_recognition_sensor_System_Name"
UDS_RDBI.dataIdentifiers[0x6c03] = "Light_switch_System_Name"
UDS_RDBI.dataIdentifiers[0x6c04] = "Garage_door_opener_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c05] = "Garage_door_opener_operating_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6c06] = "Ignition_key_System_Name"
UDS_RDBI.dataIdentifiers[0x6c07] = "Left_front_seat_ventilation_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c08] = "Right_front_seat_ventilation_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c09] = "Left_rear_seat_ventilation_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c0a] = "LED_headlamp_powermodule_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c0b] = "LED_headlamp_powermodule_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c0c] = "LED_headlamp_powermodule_2_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c0d] = "LED_headlamp_powermodule_2_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c0e] = "Operating_and_display_unit_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6c0f] = "Operating_and_display_unit_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c10] = "Right_rear_seat_ventilation_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c11] = "Data_medium_System_Name"
UDS_RDBI.dataIdentifiers[0x6c12] = "Drivers_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c13] = "Front_passengers_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c14] = "Left_headlamp_power_output_stage_System_Name"
UDS_RDBI.dataIdentifiers[0x6c15] = "Right_headlamp_power_output_stage_System_Name"
UDS_RDBI.dataIdentifiers[0x6c16] = "Sensor_for_anti_theft_alarm_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c17] = "Rear_lid_control_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c18] = "Alarm_horn_System_Name"
UDS_RDBI.dataIdentifiers[0x6c19] = "Automatic_day_night_interior_mirror_System_Name"
UDS_RDBI.dataIdentifiers[0x6c1a] = "Remote_control_auxiliary_heater_System_Name"
UDS_RDBI.dataIdentifiers[0x6c1b] = "Fresh_air_blower_front_System_Name"
UDS_RDBI.dataIdentifiers[0x6c1c] = "Fresh_air_blower_back_System_Name"
UDS_RDBI.dataIdentifiers[0x6c1d] = "Alternator_System_Name"
UDS_RDBI.dataIdentifiers[0x6c1e] = "Interior_light_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c1f] = "Refrigerant_pressure_and_temperature_sender_System_Name"
UDS_RDBI.dataIdentifiers[0x6c20] = "Sun_roof_System_Name"
UDS_RDBI.dataIdentifiers[0x6c21] = "Steering_column_lock_actuator_System_Name"
UDS_RDBI.dataIdentifiers[0x6c22] = "Anti_theft_tilt_system_control_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6c23] = "Tire_pressure_monitor_antenna_System_Name"
UDS_RDBI.dataIdentifiers[0x6c24] = "Heated_windshield_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c25] = "Rear_light_left_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6c26] = "Ceiling_light_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c27] = "Left_front_massage_seat_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c28] = "Right_front_massage_seat_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c29] = "Control_module_for_auxiliary_air_heater_System_Name"
UDS_RDBI.dataIdentifiers[0x6c2a] = "Belt Pretensioner left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c2b] = "Belt Pretensioner right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c2c] = "Occupant Detection_System_Name"
UDS_RDBI.dataIdentifiers[0x6c2d] = "Selector_lever_System_Name"
UDS_RDBI.dataIdentifiers[0x6c2e] = "NOx_sensor_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6c2f] = "NOx_sensor_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c30] = "Ioniser_System_Name"
UDS_RDBI.dataIdentifiers[0x6c31] = "Multi_function_steering_wheel_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c32] = "Left_rear_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c33] = "Right_rear_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c34] = "Left_rear_massage_seat_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c35] = "Right_rear_massage_seat_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c36] = "Display_unit_1_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c37] = "Battery_monitoring_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c38] = "Roof_blind_System_Name"
UDS_RDBI.dataIdentifiers[0x6c39] = "Sun_roof_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c3a] = "Steering_angle_sender_System_Name"
UDS_RDBI.dataIdentifiers[0x6c3b] = "Lane_change_assistant 2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c3c] = "Pitch_rate_sender_System_Name"
UDS_RDBI.dataIdentifiers[0x6c3d] = "ESP_sensor_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6c3e] = "Electronic_ignition_lock_System_Name"
UDS_RDBI.dataIdentifiers[0x6c3f] = "Air_quality_sensor_System_Name"
UDS_RDBI.dataIdentifiers[0x6c40] = "Display_unit_2_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c41] = "Telephone_handset_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c42] = "Chip_card_reader_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c43] = "Traffic_data_aerial_System_Name"
UDS_RDBI.dataIdentifiers[0x6c44] = "Hands_free_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c45] = "Telephone_handset_System_Name"
UDS_RDBI.dataIdentifiers[0x6c46] = "Display_unit_front_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c47] = "Multimedia_operating_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6c48] = "Digital_sound_system_control_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c49] = "Electrically_adjustable_steering_column_System_Name"
UDS_RDBI.dataIdentifiers[0x6c4a] = "Interface_for_external_multimedia_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6c4b] = "Relative_Air_Humidity_Interior_Sender_System_Name"
UDS_RDBI.dataIdentifiers[0x6c4c] = "Drivers_door_rear_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c4d] = "Passengers_rear_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c4e] = "Sensor_controlled_power_rear_lid_System_Name"
UDS_RDBI.dataIdentifiers[0x6c4f] = "Camera_for_night_vision_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c50] = "Relative_humidity_sensor_in_fresh_air_intake_duct_System_Name"
UDS_RDBI.dataIdentifiers[0x6c51] = "Rear_spoiler_adjustment_System_Name"
UDS_RDBI.dataIdentifiers[0x6c52] = "Roof_blind_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c53] = "Motor_for_wind_deflector_System_Name"
UDS_RDBI.dataIdentifiers[0x6c54] = "Voltage_stabilizer_System_Name"
UDS_RDBI.dataIdentifiers[0x6c55] = "Switch_module_for_driver_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6c56] = "Switch_module_for_front_passenger_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6c57] = "Switch_module_for_rear_seat_driver_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6c58] = "Switch_module_for_rear_seat_front_passenger_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6c59] = "Switch_module_2_for_driver_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6c5a] = "Battery_charger_unit_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6c5b] = "Battery_charger_unit_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c5c] = "Battery_charger_unit_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6c5d] = "Air_conditioning_compressor_System_Name"
UDS_RDBI.dataIdentifiers[0x6c5e] = "Neck_heating_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c5f] = "Neck_heating_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c60] = "Switch_module_2_for_front_passenger_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6c61] = "Switch_module_2_for_rear_seat_front_passenger_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6c62] = "Compact_disc_database_System_Name"
UDS_RDBI.dataIdentifiers[0x6c63] = "Rear_climatronic_operating_and_display_unit_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c64] = "Rear_climatronic_operating_and_display_unit_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c65] = "Door_handle_front_left_Kessy_System_Name"
UDS_RDBI.dataIdentifiers[0x6c66] = "Door_handle_front_right_Kessy_System_Name"
UDS_RDBI.dataIdentifiers[0x6c67] = "Door_handle_rear_left_Kessy_System_Name"
UDS_RDBI.dataIdentifiers[0x6c68] = "Door_handle_rear_right_Kessy_System_Name"
UDS_RDBI.dataIdentifiers[0x6c69] = "Power_converter_DC_AC_System_Name"
UDS_RDBI.dataIdentifiers[0x6c6a] = "Battery_monitoring_control_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c6b] = "Matrix_headlamp_powermodule_1_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c6c] = "Matrix_headlamp_powermodule_1_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c6d] = "High_beam_powermodule_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c6e] = "High_beam_powermodule_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c6f] = "Air_suspension_compressor_System_Name"
UDS_RDBI.dataIdentifiers[0x6c70] = "Rear_brake_actuator_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6c71] = "Rear_brake_actuator_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c72] = "Analog_clock_System_Name"
UDS_RDBI.dataIdentifiers[0x6c73] = "Rear_door_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6c79] = "Data_medium_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c7a] = "Operating_unit_center_console_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6c7b] = "Operating_unit_center_console_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c7c] = "Operating_unit_center_console_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6c7d] = "Operating_unit_center_console_4_System_Name"
UDS_RDBI.dataIdentifiers[0x6c7e] = "Interface_for_radiodisplay_System_Name"
UDS_RDBI.dataIdentifiers[0x6c7f] = "Parkassist_entry_System_Name"
UDS_RDBI.dataIdentifiers[0x6c86] = "Belt_pretensioner_3rd_row_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c87] = "Belt_pretensioner_3rd_row_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c88] = "Injection_valve_heater_control_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6c89] = "Steering_column_switch_System_Name"
UDS_RDBI.dataIdentifiers[0x6c8a] = "Brake_assistance_System_Name"
UDS_RDBI.dataIdentifiers[0x6c8b] = "Trailer_articulation_angle_sensor_System_Name"
UDS_RDBI.dataIdentifiers[0x6c8c] = "Cup_holder_with_heater_and_cooling_element_System_Name"
UDS_RDBI.dataIdentifiers[0x6c8d] = "Range_of_vision_sensing_System_Name"
UDS_RDBI.dataIdentifiers[0x6c8e] = "Convenience_and_driver_assist_operating_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6c8f] = "Cradle_rear_climatronic_operating_and_display_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6c90] = "Trailer_weight_nose_weight_detection_System_Name"
UDS_RDBI.dataIdentifiers[0x6c91] = "Sensor_carbon_dioxide_concentration_System_Name"
UDS_RDBI.dataIdentifiers[0x6c92] = "Sensor_fine_dust_concentration_System_Name"
UDS_RDBI.dataIdentifiers[0x6c93] = "Volume_control_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6c94] = "Belt_buckle_presenter_2nd_row_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6c95] = "Belt_buckle_presenter_2nd_row_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6c96] = "Operating_and_display_unit_6_for_air_conditioning_System_Name"
UDS_RDBI.dataIdentifiers[0x6c97] = "Active_accelerator_pedal_System_Name"
UDS_RDBI.dataIdentifiers[0x6c98] = "Multimedia_operating_unit_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6c99] = "Display_unit_3_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c9a] = "Display_unit_4_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c9b] = "Display_unit_5_for_multimedia_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6c9c] = "Control_module_for_auxiliary_blower_motors_System_Name"
UDS_RDBI.dataIdentifiers[0x6c9d] = "Operating_and_display_unit_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6c9e] = "Operating_and_display_unit_4_System_Name"
UDS_RDBI.dataIdentifiers[0x6c9f] = "Operating_and_display_unit_5_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca0] = "Side Sensor Driver Front_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca1] = "Side Sensor Passenger Front_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca2] = "Side Sensor Driver Rear_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca3] = "Side Sensor Passenger Rear_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca4] = "Front Sensor Driver_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca5] = "Front Sensor Passenger_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca6] = "Pedestrian Protection Driver_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca7] = "Pedestrian Protection Passenger_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca8] = "Rear Sensor Center_System_Name"
UDS_RDBI.dataIdentifiers[0x6ca9] = "Pedestrian Protection Center_System_Name"
UDS_RDBI.dataIdentifiers[0x6caa] = "Pedestrian Protection Contact_System_Name"
UDS_RDBI.dataIdentifiers[0x6cab] = "Pedestrian_protection_driver_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6cac] = "Pedestrian_protection_passenger_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6cad] = "Central_sensor_XY_System_Name"
UDS_RDBI.dataIdentifiers[0x6cae] = "Refrigerant_pressure_and_temperature_sender_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6caf] = "Refrigerant_pressure_and_temperature_sender_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb0] = "Switch_for_rear_multicontour_seat_driver_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb1] = "Valve_block_1_in_driver_side_rear_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb2] = "Valve_block_2_in_driver_side_rear_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb3] = "Valve_block_3_in_driver_side_rear_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb4] = "Switch_for_rear_multicontour_seat_passenger_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb5] = "Valve_block_1_in_passenger_side_rear_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb6] = "Valve_block_2_in_passenger_side_rear_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb7] = "Valve_block_3_in_passenger_side_rear_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb8] = "Switch_for_front_multicontour_seat_driver_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6cb9] = "Valve_block_1_in_driver_side_front_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cba] = "Valve_block_2_in_driver_side_front_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cbb] = "Valve_block_3_in_driver_side_front_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cbc] = "Switch_for_front_multicontour_seat_passenger_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6cbd] = "Valve_block_1_in_passenger_side_front_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cbe] = "Valve_block_2_in_passenger_side_front_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cbf] = "Valve_block_3_in_passenger_side_front_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc0] = "Coolant_heater_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc1] = "Seat_backrest_fan_1_front_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc2] = "Seat_backrest_fan_2_front_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc3] = "Seat_cushion_fan_1_front_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc4] = "Seat_cushion_fan_2_front_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc5] = "Seat_backrest_fan_1_front_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc6] = "Seat_backrest_fan_2_front_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc7] = "Seat_cushion_fan_1_front_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc8] = "Seat_cushion_fan_2_front_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6cc9] = "Operating_and_display_unit_1_for_air_conditioning_System_Name"
UDS_RDBI.dataIdentifiers[0x6cca] = "Operating_and_display_unit_2_for_air_conditioning_System_Name"
UDS_RDBI.dataIdentifiers[0x6ccb] = "Operating_and_display_unit_3_for_air_conditioning_System_Name"
UDS_RDBI.dataIdentifiers[0x6ccc] = "Operating_and_display_unit_4_for_air_conditioning_System_Name"
UDS_RDBI.dataIdentifiers[0x6ccd] = "Operating_and_display_unit_5_for_air_conditioning_System_Name"
UDS_RDBI.dataIdentifiers[0x6cce] = "Pedestrian_protection_left_hand_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6ccf] = "Pedestrian_protection_right_hand_side_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd0] = "Battery_junction_box_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd1] = "Cell_module_controller_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd2] = "Cell_module_controller_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd3] = "Cell_module_controller_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd4] = "Cell_module_controller_4_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd5] = "Cell_module_controller_5_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd6] = "Cell_module_controller_6_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd7] = "Cell_module_controller_7_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd8] = "Cell_module_controller_8_System_Name"
UDS_RDBI.dataIdentifiers[0x6cd9] = "Cell_module_controller_9_System_Name"
UDS_RDBI.dataIdentifiers[0x6cda] = "Cell_module_controller_10_System_Name"
UDS_RDBI.dataIdentifiers[0x6cdb] = "Cell_module_controller_11_System_Name"
UDS_RDBI.dataIdentifiers[0x6cdc] = "Cell_module_controller_12_System_Name"
UDS_RDBI.dataIdentifiers[0x6cdd] = "Seat_backrest_fan_1_rear_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6cde] = "Seat_backrest_fan_2_rear_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6cdf] = "Seat_cushion_fan_1_rear_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce0] = "Seat_cushion_fan_2_rear_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce1] = "Seat_backrest_fan_1_rear_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce2] = "Seat_backrest_fan_2_rear_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce3] = "Seat_cushion_fan_1_rear_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce4] = "Seat_cushion_fan_2_rear_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce5] = "Auxiliary_blower_motor_control_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce6] = "Auxiliary_blower_motor_control_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce7] = "Infrared_sender_for_front_observation_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce8] = "Starter_generator_control_module_sub_System_Name"
UDS_RDBI.dataIdentifiers[0x6ce9] = "Media_player_1_sub_System_Name"
UDS_RDBI.dataIdentifiers[0x6cea] = "Media_player_2_sub_System_Name"
UDS_RDBI.dataIdentifiers[0x6ceb] = "Dedicated_short_range_communication_aerial_System_Name"
UDS_RDBI.dataIdentifiers[0x6cec] = "Refrigerant_pressure_and_temperature_sender_4_System_Name"
UDS_RDBI.dataIdentifiers[0x6ced] = "Refrigerant_pressure_and_temperature_sender_5_System_Name"
UDS_RDBI.dataIdentifiers[0x6cee] = "Refrigerant_pressure_and_temperature_sender_6_System_Name"
UDS_RDBI.dataIdentifiers[0x6cef] = "Air_coolant_actuator_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf0] = "Air_coolant_actuator_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf1] = "Cell_module_controller_13_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf2] = "Cell_module_controller_14_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf3] = "Cell_module_controller_15_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf5] = "Seat_heating_rear_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf6] = "LED_warning_indicator_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf7] = "Automatic_transmission_fluid_pump_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf8] = "Manual_transmission_fluid_pump_System_Name"
UDS_RDBI.dataIdentifiers[0x6cf9] = "Convenience_and_driver_assist_operating_unit_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6cfb] = "Air_coolant_actuator_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6cfc] = "Valve_block_4_in_driver_side_rear_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cfd] = "Valve_block_4_in_passenger_side_rear_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cfe] = "Valve_block_4_in_driver_side_front_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6cff] = "Valve_block_4_in_passenger_side_front_seat_System_Name"
UDS_RDBI.dataIdentifiers[0x6d01] = "Rear_climatronic_operating_and_display_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6d02] = "Refrigerant_expansion_valve_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6d03] = "Refrigerant_expansion_valve_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d04] = "Refrigerant_expansion_valve_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6d05] = "Refrigerant_shut_off_valve_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6d06] = "Refrigerant_shut_off_valve_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d07] = "Refrigerant_shut_off_valve_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6d08] = "Refrigerant_shut_off_valve_4_System_Name"
UDS_RDBI.dataIdentifiers[0x6d09] = "Refrigerant_shut_off_valve_5_System_Name"
UDS_RDBI.dataIdentifiers[0x6d0a] = "Sunlight_sensor_System_Name"
UDS_RDBI.dataIdentifiers[0x6d0b] = "Near_field_communication_control_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d0c] = "Clutch_control_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6d0d] = "Electrical_charger_System_Name"
UDS_RDBI.dataIdentifiers[0x6d0e] = "Rear_light_left_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d0f] = "Rear_light_right_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6d10] = "Rear_light_right_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d11] = "Sunlight_sensor_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d12] = "Radiator_shutter_System_Name"
UDS_RDBI.dataIdentifiers[0x6d13] = "Radiator_shutter_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d14] = "Radiator_shutter_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6d15] = "Radiator_shutter_4_System_Name"
UDS_RDBI.dataIdentifiers[0x6d18] = "Special_key_operating_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6d19] = "Radio_interface_System_Name"
UDS_RDBI.dataIdentifiers[0x6d1a] = "Video_self_protection_recorder_System_Name"
UDS_RDBI.dataIdentifiers[0x6d1b] = "Special_vehicle_assist_interface_System_Name"
UDS_RDBI.dataIdentifiers[0x6d1c] = "Electric_system_disconnection_diode_System_Name"
UDS_RDBI.dataIdentifiers[0x6d1d] = "Cradle_rear_climatronic_operating_and_display_unit_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d1e] = "Belt_pretensioner_2nd_row_left_System_Name"
UDS_RDBI.dataIdentifiers[0x6d1f] = "Belt_pretensioner_2nd_row_right_System_Name"
UDS_RDBI.dataIdentifiers[0x6d20] = "Electrical_variable_camshaft_phasing_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6d21] = "Electrical_variable_camshaft_phasing_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d22] = "Wireless_operating_unit_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6d23] = "Wireless_operating_unit_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d24] = "Front_windshield_washer_pump_System_Name"
UDS_RDBI.dataIdentifiers[0x6d25] = "Air_quality_sensor_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d26] = "Fragrancing_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6d27] = "Coolant_valve_System_Name"
UDS_RDBI.dataIdentifiers[0x6d28] = "Near_field_communication_control_module_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6d29] = "Interior_monitoring_rear_System_Name"
UDS_RDBI.dataIdentifiers[0x6d2a] = "Cooler_fan_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6d2b] = "Control_unit_heating_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6d2c] = "Control_unit_heating_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d2d] = "Control_unit_heating_3_System_Name"
UDS_RDBI.dataIdentifiers[0x6d2e] = "Control_unit_heating_4_System_Name"
UDS_RDBI.dataIdentifiers[0x6d2f] = "Operating_unit_drive_mode_selection_System_Name"
UDS_RDBI.dataIdentifiers[0x6d30] = "Side_sensor_a-pillar_driver_front_System_Name"
UDS_RDBI.dataIdentifiers[0x6d31] = "Side_sensor_a-pillar_passenger_front_System_Name"
UDS_RDBI.dataIdentifiers[0x6d32] = "Sensor_high_voltage_system_1_System_Name"
UDS_RDBI.dataIdentifiers[0x6d33] = "Side_sensor_b-pillar_driver_front_System_Name"
UDS_RDBI.dataIdentifiers[0x6d34] = "Side_sensor_b-pillar_passenger_front_System_Name"
UDS_RDBI.dataIdentifiers[0x6d35] = "Multi_function_steering_wheel_control_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d36] = "Gear_selection_display_System_Name"
UDS_RDBI.dataIdentifiers[0x6d37] = "Cooler_fan_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d38] = "Gear_selector_control_module_System_Name"
UDS_RDBI.dataIdentifiers[0x6d39] = "Interior_light_module_2_System_Name"
UDS_RDBI.dataIdentifiers[0x6d3a] = "Radio_control_center_System_Name"
UDS_RDBI.dataIdentifiers[0x6d3b] = "Multimedia_extension_System_Name"
UDS_RDBI.dataIdentifiers[0x6d3c] = "Control_unit_differential_lock_System_Name"
UDS_RDBI.dataIdentifiers[0x6d3d] = "Control_unit_ride_control_system_System_Name"
UDS_RDBI.dataIdentifiers[0x6d3e] = "Control_unit_hands_on_detection_steering_wheel_System_Name"
UDS_RDBI.dataIdentifiers[0x6d3f] = "Front_climatronic_operating_and_display_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6d40] = "Auxiliary_display_unit_System_Name"
UDS_RDBI.dataIdentifiers[0x6d41] = "Card_reader_tv_tuner_System_Name"
UDS_RDBI.dataIdentifiers[0x6d42] = "Park_lock_actuator_System_Name"
UDS_RDBI.dataIdentifiers[0x6d43] = "Media_connector_System_Name"
UDS_RDBI.dataIdentifiers[0x6d44] = "Catalyst_heating_System_Name"
UDS_RDBI.dataIdentifiers[0x6e01] = "Control_unit_for_wiper_motor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e02] = "Rain_light_recognition_sensor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e03] = "Light_switch_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e04] = "Garage_door_opener_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e05] = "Garage_door_opener_operating_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e06] = "Ignition_key_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e07] = "Left_front_seat_ventilation_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e08] = "Right_front_seat_ventilation_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e09] = "Left_rear_seat_ventilation_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e0a] = "LED_headlamp_powermodule_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e0b] = "LED_headlamp_powermodule_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e0c] = "LED_headlamp_powermodule_2_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e0d] = "LED_headlamp_powermodule_2_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e0e] = "Operating_and_display_unit_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e0f] = "Operating_and_display_unit_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e10] = "Right_rear_seat_ventilation_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e11] = "Data_medium_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e12] = "Drivers_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e13] = "Front_passengers_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e14] = "Left_headlamp_power_output_stage_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e15] = "Right_headlamp_power_output_stage_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e16] = "Sensor_for_anti_theft_alarm_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e17] = "Rear_lid_control_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e18] = "Alarm_horn_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e19] = "Automatic_day_night_interior_mirror_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e1a] = "Remote_control_auxiliary_heater_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e1b] = "Fresh_air_blower_front_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e1c] = "Fresh_air_blower_back_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e1d] = "Alternator_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e1e] = "Interior_light_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e1f] = "Refrigerant_pressure_and_temperature_sender_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e20] = "Sun_roof_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e21] = "Steering_column_lock_actuator_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e22] = "Anti_theft_tilt_system_control_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e23] = "Tire_pressure_monitor_antenna_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e24] = "Heated_windshield_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e25] = "Rear_light_left_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e26] = "Ceiling_light_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e27] = "Left_front_massage_seat_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e28] = "Right_front_massage_seat_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e29] = "Control_module_for_auxiliary_air_heater_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e2a] = "Belt Pretensioner left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e2b] = "Belt Pretensioner right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e2c] = "Occupant Detection_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e2d] = "Selector_lever_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e2e] = "NOx_sensor_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e2f] = "NOx_sensor_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e30] = "Ioniser_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e31] = "Multi_function_steering_wheel_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e32] = "Left_rear_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e33] = "Right_rear_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e34] = "Left_rear_massage_seat_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e35] = "Right_rear_massage_seat_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e36] = "Display_unit_1_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e37] = "Battery_monitoring_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e38] = "Roof_blind_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e39] = "Sun_roof_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e3a] = "Steering_angle_sender_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e3b] = "Lane_change_assistant 2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e3c] = "Pitch_rate_sender_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e3d] = "ESP_sensor_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e3e] = "Electronic_ignition_lock_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e3f] = "Air_quality_sensor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e40] = "Display_unit_2_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e41] = "Telephone_handset_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e42] = "Chip_card_reader_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e43] = "Traffic_data_aerial_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e44] = "Hands_free_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e45] = "Telephone_handset_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e46] = "Display_unit_front_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e47] = "Multimedia_operating_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e48] = "Digital_sound_system_control_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e49] = "Electrically_adjustable_steering_column_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e4a] = "Interface_for_external_multimedia_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e4b] = "Relative_Air_Humidity_Interior_Sender_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e4c] = "Drivers_door_rear_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e4d] = "Passengers_rear_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e4e] = "Sensor_controlled_power_rear_lid_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e4f] = "Camera_for_night_vision_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e50] = "Relative_humidity_sensor_in_fresh_air_intake_duct_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e51] = "Rear_spoiler_adjustment_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e52] = "Roof_blind_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e53] = "Motor_for_wind_deflector_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e54] = "Voltage_stabilizer_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e55] = "Switch_module_for_driver_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e56] = "Switch_module_for_front_passenger_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e57] = "Switch_module_for_rear_seat_driver_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e58] = "Switch_module_for_rear_seat_front_passenger_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e59] = "Switch_module_2_for_driver_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e5a] = "Battery_charger_unit_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e5b] = "Battery_charger_unit_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e5c] = "Battery_charger_unit_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e5d] = "Air_conditioning_compressor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e5e] = "Neck_heating_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e5f] = "Neck_heating_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e60] = "Switch_module_2_for_front_passenger_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e61] = "Switch_module_2_for_rear_seat_front_passenger_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e62] = "Compact_disc_database_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e63] = "Rear_climatronic_operating_and_display_unit_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e64] = "Rear_climatronic_operating_and_display_unit_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e65] = "Door_handle_front_left_Kessy_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e66] = "Door_handle_front_right_Kessy_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e67] = "Door_handle_rear_left_Kessy_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e68] = "Door_handle_rear_right_Kessy_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e69] = "Power_converter_DC_AC_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e6a] = "Battery_monitoring_control_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e6b] = "Matrix_headlamp_powermodule_1_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e6c] = "Matrix_headlamp_powermodule_1_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e6d] = "High_beam_powermodule_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e6e] = "High_beam_powermodule_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e6f] = "Air_suspension_compressor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e70] = "Rear_brake_actuator_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e71] = "Rear_brake_actuator_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e72] = "Analog_clock_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e73] = "Rear_door_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e79] = "Data_medium_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e7a] = "Operating_unit_center_console_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e7b] = "Operating_unit_center_console_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e7c] = "Operating_unit_center_console_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e7d] = "Operating_unit_center_console_4_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e7e] = "Interface_for_radiodisplay_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e7f] = "Parkassist_entry_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e86] = "Belt_pretensioner_3rd_row_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e87] = "Belt_pretensioner_3rd_row_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e88] = "Injection_valve_heater_control_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e89] = "Steering_column_switch_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e8a] = "Brake_assistance_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e8b] = "Trailer_articulation_angle_sensor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e8c] = "Cup_holder_with_heater_and_cooling_element_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e8d] = "Range_of_vision_sensing_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e8e] = "Convenience_and_driver_assist_operating_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e8f] = "Cradle_rear_climatronic_operating_and_display_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e90] = "Trailer_weight_nose_weight_detection_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e91] = "Sensor_carbon_dioxide_concentration_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e92] = "Sensor_fine_dust_concentration_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e93] = "Volume_control_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e94] = "Belt_buckle_presenter_2nd_row_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e95] = "Belt_buckle_presenter_2nd_row_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e96] = "Operating_and_display_unit_6_for_air_conditioning_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e97] = "Active_accelerator_pedal_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e98] = "Multimedia_operating_unit_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e99] = "Display_unit_3_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e9a] = "Display_unit_4_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e9b] = "Display_unit_5_for_multimedia_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e9c] = "Control_module_for_auxiliary_blower_motors_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e9d] = "Operating_and_display_unit_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e9e] = "Operating_and_display_unit_4_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6e9f] = "Operating_and_display_unit_5_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea0] = "Side Sensor Driver Front_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea1] = "Side Sensor Passenger Front_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea2] = "Side Sensor Driver Rear_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea3] = "Side Sensor Passenger Rear_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea4] = "Front Sensor Driver_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea5] = "Front Sensor Passenger_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea6] = "Pedestrian Protection Driver_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea7] = "Pedestrian Protection Passenger_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea8] = "Rear Sensor Center_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ea9] = "Pedestrian Protection Center_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eaa] = "Pedestrian Protection Contact_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eab] = "Pedestrian_protection_driver_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eac] = "Pedestrian_protection_passenger_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ead] = "Central_sensor_XY_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eae] = "Refrigerant_pressure_and_temperature_sender_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eaf] = "Refrigerant_pressure_and_temperature_sender_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb0] = "Switch_for_rear_multicontour_seat_driver_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb1] = "Valve_block_1_in_driver_side_rear_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb2] = "Valve_block_2_in_driver_side_rear_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb3] = "Valve_block_3_in_driver_side_rear_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb4] = "Switch_for_rear_multicontour_seat_passenger_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb5] = "Valve_block_1_in_passenger_side_rear_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb6] = "Valve_block_2_in_passenger_side_rear_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb7] = "Valve_block_3_in_passenger_side_rear_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb8] = "Switch_for_front_multicontour_seat_driver_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eb9] = "Valve_block_1_in_driver_side_front_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eba] = "Valve_block_2_in_driver_side_front_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ebb] = "Valve_block_3_in_driver_side_front_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ebc] = "Switch_for_front_multicontour_seat_passenger_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ebd] = "Valve_block_1_in_passenger_side_front_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ebe] = "Valve_block_2_in_passenger_side_front_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ebf] = "Valve_block_3_in_passenger_side_front_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec0] = "Coolant_heater_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec1] = "Seat_backrest_fan_1_front_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec2] = "Seat_backrest_fan_2_front_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec3] = "Seat_cushion_fan_1_front_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec4] = "Seat_cushion_fan_2_front_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec5] = "Seat_backrest_fan_1_front_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec6] = "Seat_backrest_fan_2_front_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec7] = "Seat_cushion_fan_1_front_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec8] = "Seat_cushion_fan_2_front_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ec9] = "Operating_and_display_unit_1_for_air_conditioning_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eca] = "Operating_and_display_unit_2_for_air_conditioning_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ecb] = "Operating_and_display_unit_3_for_air_conditioning_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ecc] = "Operating_and_display_unit_4_for_air_conditioning_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ecd] = "Operating_and_display_unit_5_for_air_conditioning_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ece] = "Pedestrian_protection_left_hand_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ecf] = "Pedestrian_protection_right_hand_side_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed0] = "Battery_junction_box_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed1] = "Cell_module_controller_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed2] = "Cell_module_controller_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed3] = "Cell_module_controller_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed4] = "Cell_module_controller_4_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed5] = "Cell_module_controller_5_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed6] = "Cell_module_controller_6_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed7] = "Cell_module_controller_7_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed8] = "Cell_module_controller_8_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ed9] = "Cell_module_controller_9_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eda] = "Cell_module_controller_10_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6edb] = "Cell_module_controller_11_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6edc] = "Cell_module_controller_12_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6edd] = "Seat_backrest_fan_1_rear_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ede] = "Seat_backrest_fan_2_rear_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6edf] = "Seat_cushion_fan_1_rear_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee0] = "Seat_cushion_fan_2_rear_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee1] = "Seat_backrest_fan_1_rear_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee2] = "Seat_backrest_fan_2_rear_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee3] = "Seat_cushion_fan_1_rear_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee4] = "Seat_cushion_fan_2_rear_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee5] = "Auxiliary_blower_motor_control_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee6] = "Auxiliary_blower_motor_control_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee7] = "Infrared_sender_for_front_observation_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee8] = "Starter_generator_control_module_sub_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ee9] = "Media_player_1_sub_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eea] = "Media_player_2_sub_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eeb] = "Dedicated_short_range_communication_aerial_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eec] = "Refrigerant_pressure_and_temperature_sender_4_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eed] = "Refrigerant_pressure_and_temperature_sender_5_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eee] = "Refrigerant_pressure_and_temperature_sender_6_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eef] = "Air_coolant_actuator_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef0] = "Air_coolant_actuator_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef1] = "Cell_module_controller_13_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef2] = "Cell_module_controller_14_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef3] = "Cell_module_controller_15_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef5] = "Seat_heating_rear_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef6] = "LED_warning_indicator_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef7] = "Automatic_transmission_fluid_pump_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef8] = "Manual_transmission_fluid_pump_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6ef9] = "Convenience_and_driver_assist_operating_unit_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6efb] = "Air_coolant_actuator_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6efc] = "Valve_block_4_in_driver_side_rear_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6efd] = "Valve_block_4_in_passenger_side_rear_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6efe] = "Valve_block_4_in_driver_side_front_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6eff] = "Valve_block_4_in_passenger_side_front_seat_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f01] = "Rear_climatronic_operating_and_display_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f02] = "Refrigerant_expansion_valve_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f03] = "Refrigerant_expansion_valve_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f04] = "Refrigerant_expansion_valve_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f05] = "Refrigerant_shut_off_valve_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f06] = "Refrigerant_shut_off_valve_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f07] = "Refrigerant_shut_off_valve_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f08] = "Refrigerant_shut_off_valve_4_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f09] = "Refrigerant_shut_off_valve_5_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f0a] = "Sunlight_sensor_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f0b] = "Near_field_communication_control_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f0c] = "Clutch_control_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f0d] = "Electrical_charger_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f0e] = "Rear_light_left_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f0f] = "Rear_light_right_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f10] = "Rear_light_right_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f11] = "Sunlight_sensor_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f12] = "Radiator_shutter_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f13] = "Radiator_shutter_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f14] = "Radiator_shutter_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f15] = "Radiator_shutter_4_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f18] = "Special_key_operating_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f19] = "Radio_interface_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f1a] = "Video_self_protection_recorder_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f1b] = "Special_vehicle_assist_interface_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f1c] = "Electric_system_disconnection_diode_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f1d] = "Cradle_rear_climatronic_operating_and_display_unit_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f1e] = "Belt_pretensioner_2nd_row_left_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f1f] = "Belt_pretensioner_2nd_row_right_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f20] = "Electrical_variable_camshaft_phasing_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f21] = "Electrical_variable_camshaft_phasing_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f22] = "Wireless_operating_unit_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f23] = "Wireless_operating_unit_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f24] = "Front_windshield_washer_pump_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f25] = "Air_quality_sensor_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f26] = "Fragrancing_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f27] = "Coolant_valve_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f28] = "Near_field_communication_control_module_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f29] = "Interior_monitoring_rear_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f2a] = "Cooler_fan_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f2b] = "Control_unit_heating_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f2c] = "Control_unit_heating_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f2d] = "Control_unit_heating_3_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f2e] = "Control_unit_heating_4_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f2f] = "Operating_unit_drive_mode_selection_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f30] = "Side_sensor_a-pillar_driver_front_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f31] = "Side_sensor_a-pillar_passenger_front_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f32] = "Sensor_high_voltage_system_1_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f33] = "Side_sensor_b-pillar_driver_front_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f34] = "Side_sensor_b-pillar_passenger_front_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f35] = "Multi_function_steering_wheel_control_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f36] = "Gear_selection_display_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f37] = "Cooler_fan_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f38] = "Gear_selector_control_module_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f39] = "Interior_light_module_2_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f3a] = "Radio_control_center_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f3b] = "Multimedia_extension_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f3c] = "Control_unit_differential_lock_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f3d] = "Control_unit_ride_control_system_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f3e] = "Control_unit_hands_on_detection_steering_wheel_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f3f] = "Front_climatronic_operating_and_display_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f40] = "Auxiliary_display_unit_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f41] = "Card_reader_tv_tuner_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f42] = "Park_lock_actuator_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f43] = "Media_connector_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0x6f44] = "Catalyst_heating_VW_Slave_FAZIT_string"
UDS_RDBI.dataIdentifiers[0xef90] = "Secure_hardware_extension_status"
UDS_RDBI.dataIdentifiers[0xf15a] = "Fingerprint"
UDS_RDBI.dataIdentifiers[0xf15b] = "Fingerprint And Programming Date Of Logical Software Blocks"
UDS_RDBI.dataIdentifiers[0xf17c] = "VW FAZIT Identification String"
UDS_RDBI.dataIdentifiers[0xf186] = "Active Diagnostic Session"
UDS_RDBI.dataIdentifiers[0xf187] = "VW Spare Part Number"
UDS_RDBI.dataIdentifiers[0xf189] = "VW Application Software Version Number"
UDS_RDBI.dataIdentifiers[0xf18a] = "System Supplier Identifier"
UDS_RDBI.dataIdentifiers[0xf18c] = "ECU Serial Number"
UDS_RDBI.dataIdentifiers[0xf190] = "Vehicle Identification Number"
UDS_RDBI.dataIdentifiers[0xf191] = "VW ECU Hardware Number"
UDS_RDBI.dataIdentifiers[0xf192] = "System Supplier ECU Hardware Number"
UDS_RDBI.dataIdentifiers[0xf193] = "System Supplier ECU Hardware Version Number"
UDS_RDBI.dataIdentifiers[0xf194] = "System Supplier ECU Software Number"
UDS_RDBI.dataIdentifiers[0xf195] = "System Supplier ECU Software Version Number"
UDS_RDBI.dataIdentifiers[0xf197] = "VW System Name Or Engine Type"
UDS_RDBI.dataIdentifiers[0xf19e] = "ASAM ODX File Identifier"
UDS_RDBI.dataIdentifiers[0xf1a0] = "VW Data Set Number Or ECU Data Container Number"
UDS_RDBI.dataIdentifiers[0xf1a1] = "VW Data Set Version Number"
UDS_RDBI.dataIdentifiers[0xf1a2] = "ASAM ODX File Version"
UDS_RDBI.dataIdentifiers[0xf1a3] = "VW ECU Hardware Version Number"
UDS_RDBI.dataIdentifiers[0xf1aa] = "VW Workshop System Name"
UDS_RDBI.dataIdentifiers[0xf1ab] = "VW Logical Software Block Version"
UDS_RDBI.dataIdentifiers[0xf1ad] = "Engine Code Letters"
UDS_RDBI.dataIdentifiers[0xf1af] = "AUTOSAR_standard_application_software_identification"
UDS_RDBI.dataIdentifiers[0xf1b0] = "VWClear_diagnostic_information_date_functional"
UDS_RDBI.dataIdentifiers[0xf1b1] = "VW_Application_data_set_identification"
UDS_RDBI.dataIdentifiers[0xf1b2] = "Function_software_identification"
UDS_RDBI.dataIdentifiers[0xf1b3] = "VW_Data_set_name"
UDS_RDBI.dataIdentifiers[0xf1b5] = "Busmaster_description"
UDS_RDBI.dataIdentifiers[0xf1b6] = "System_identification"
UDS_RDBI.dataIdentifiers[0xf1b7] = "Gateway_component_list_ECU_node_address"
UDS_RDBI.dataIdentifiers[0xf1d5] = "FDS_project_data"
UDS_RDBI.dataIdentifiers[0xf1df] = "ECU Programming Information"


UDS_RC.routineControlIdentifiers[0x0202] = "Check Memory"
UDS_RC.routineControlIdentifiers[0x0203] = "Check Programming Preconditions"
UDS_RC.routineControlIdentifiers[0x0317] = "Reset of Adaption Values"
UDS_RC.routineControlIdentifiers[0x0366] = "Reset of all Adaptions"
UDS_RC.routineControlIdentifiers[0x03e7] = "Reset to Factory Settings"
UDS_RC.routineControlIdentifiers[0x045a] = "Clear user defined DTC information"
UDS_RC.routineControlIdentifiers[0x0544] = "Verify partial software checksum"
UDS_RC.routineControlIdentifiers[0x0594] = "Check upload preconditions"
UDS_RC.routineControlIdentifiers[0xff00] = "Erase Memory"
UDS_RC.routineControlIdentifiers[0xff01] = "Check Programming Dependencies"


UDS_RD.dataFormatIdentifiers[0x0000] = "Uncompressed"
UDS_RD.dataFormatIdentifiers[0x0001] = "Compression Method 1"
UDS_RD.dataFormatIdentifiers[0x0002] = "Compression Method 2"
UDS_RD.dataFormatIdentifiers[0x0003] = "Compression Method 3"
UDS_RD.dataFormatIdentifiers[0x0004] = "Compression Method 4"
UDS_RD.dataFormatIdentifiers[0x0005] = "Compression Method 5"
UDS_RD.dataFormatIdentifiers[0x0006] = "Compression Method 6"
UDS_RD.dataFormatIdentifiers[0x0007] = "Compression Method 7"
UDS_RD.dataFormatIdentifiers[0x0008] = "Compression Method 8"
UDS_RD.dataFormatIdentifiers[0x0009] = "Compression Method 9"
UDS_RD.dataFormatIdentifiers[0x000a] = "Compression Method 10"
UDS_RD.dataFormatIdentifiers[0x000b] = "Compression Method 11"
UDS_RD.dataFormatIdentifiers[0x000c] = "Compression Method 12"
UDS_RD.dataFormatIdentifiers[0x000d] = "Compression Method 13"
UDS_RD.dataFormatIdentifiers[0x000e] = "Compression Method 14"
UDS_RD.dataFormatIdentifiers[0x000f] = "Compression Method 15"
