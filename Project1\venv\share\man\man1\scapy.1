\" SPDX-License-Identifier: GPL-2.0-only
.TH SCAPY 1 "March 24, 2024"
.SH NAME
scapy \- Interactive packet manipulation tool
.SH SYNOPSIS
.B scapy
.RI [ options ]
.SH DESCRIPTION
This manual page documents briefly the
.B Scapy
tool.
.PP
\fBScapy\fP is a powerful interactive packet manipulation tool, 
packet generator, network scanner, network discovery, packet sniffer,
etc. It can for the moment replace hping, parts of nmap, arpspoof, arp-sk,
arping, tcpdump, tshark, p0f, ...
.PP
\fBScapy\fP uses the Python interpreter as a command board. That means that 
you can use directly Python language (assign variables, use loops, 
define functions, etc.) If you give a file a parameter when you run 
\fBScapy\fP, your session (variables, functions, instances, ...) will be saved 
when you leave the interpreter and restored the next time you launch
\fBScapy\fP. 
.PP
The idea is simple. Those kinds of tools do two things : sending packets 
and receiving answers. That's what \fBScapy\fP does : you define a set of 
packets, it sends them, receives answers, matches requests with answers 
and returns a list of packet couples (request, answer) and a list of 
unmatched packets. This has the big advantage over tools like nmap or 
hping that an answer is not reduced to (open/closed/filtered), but is 
the whole packet. 
.PP
On top of this can be used to build more high-level functions, for example, one 
that does traceroutes and give as a result only the start TTL of the 
request and the source IP of the answer. One that pings a whole network 
and gives the list of machines answering. One that does a portscan and 
returns a LaTeX report. 

.SH OPTIONS
Options for Scapy are:
.TP
\fB\-h\fR
display usage
.TP
\fB\-H\fR
header-less mode, also reduces verbosity.
.TP
\fB\-d\fR
increase log verbosity. Can be used many times.
.TP
\fB\-s\fR FILE
use FILE to save/load session values (variables, functions, instances, ...) 
.TP
\fB\-p\fR PRESTART_FILE 
use PRESTART_FILE instead of $HOME/.config/scapy/prestart.py as pre-startup file
.TP
\fB\-P\fR
do not run prestart file
.TP
\fB\-c\fR STARTUP_FILE 
use STARTUP_FILE instead of $HOME/.config/scapy/startup.py as startup file
.TP
\fB\-C\fR
do not run startup file

.SH COMMANDS
Only the vital commands to begin are listed here for the moment.
.TP
\fBls()\fR
lists supported protocol layers.
If a protocol layer is given as parameter, lists its fields and types of fields.
If a string is given as parameter, it is used to filter the layers.
.TP
\fBlsc()\fR
lists scapy's main user commands.
.TP
\fBconf\fR
this object contains the configuration. 

.SH FILES
\fB$HOME/.config/scapy/prestart.py\fR
This file is run before Scapy core is loaded. Only the \fBconf\fP object
is available. This file can be used to configure the CLI, configure
parameters such as the \fBconf.load_layers\fP  list to choose which layers
will be loaded, or change the logging level (for instance):

.nf
conf.interactive_shell = "bpython"
log_loading.setLevel(logging.WARNING)
conf.load_layers.remove("bluetooth")
conf.load_layers.append("new_layer")
.fi

\fB$HOME/.config/scapy/startup.py\fR
This file is run after Scapy is loaded. It can be used to configure
more of Scapy behaviors, like un-registering layers:

.nf
conf.prog.pdfreader = "xpdf"
split_layers(UDP,DNS)
.fi

.SH EXAMPLES

More verbose examples are available in the documentation at
\fIhttps://scapy.readthedocs.io/\fP.
Just run \fBscapy\fP and try the following commands in the interpreter.

.LP
Test the robustness of a network stack with invalid packets:
.nf
sr(IP(dst="**********", ihl=2, options=["verb$2"], version=3)/ICMP(), timeout=2)
.fi

.LP
Packet sniffing and dissection (with a bpf filter or tshark-like output):
.nf
a=sniff(filter="tcp port 110")
a=sniff(prn = lambda x: x.show) 
.fi

.LP
Sniffed packet re-emission:
.nf
a=sniff(filter="tcp port 110")
sendp(a)
.fi

.LP
Pcap file packet re-emission:
.nf
sendp(rdpcap("file.cap"))
.fi

.LP
Manual TCP traceroute:
.nf
sr(IP(dst="www.google.com", ttl=(1,30))/TCP(seq=RandInt(), sport=RandShort(), dport=dport)
.fi

.LP
Protocol scan:
.nf
sr(IP(dst="***********", proto=(1,254)))
.fi

.LP
ARP ping:
.nf
srp(Ether(dst="ff:ff:ff:ff:ff:ff")/ARP(pdst="**********/24"))
.fi

.LP
ACK scan:
.nf
sr(IP(dst="***********")/TCP(dport=(1,1024), flags="A"))
.fi

.LP
Passive OS fingerprinting:
.nf
sniff(prn=prnp0f) 
.fi

.LP
Active OS fingerprinting:
.nf
nmap_fp("************")
.fi


.LP
ARP cache poisoning:
.nf
sendp(Ether(dst=tmac)/ARP(op="who-has", psrc=victim, pdst=target))
.fi

.LP
Reporting:
.nf
report_ports("************", (20,30))
.fi

.SH SEE ALSO

.nf
The official website: \fIhttps://scapy.net/\fP
The GitHub Development repository: \fIhttps://github.com/secdev/scapy/\fP
The official documentation: \fIhttps://scapy.readthedocs.io/en/latest/\fP
.fi

.SH BUGS
Does not give the right source IP for routes that use interface aliases.

May miss packets under heavy load. This is a restriction from python itself

Session saving is limited by Python ability to marshal objects. As a 
consequence, lambda functions and generators can't be saved, which seriously
reduce the usefulness of this feature. 

BPF filters don't work on Point-to-point interfaces.


.SH AUTHOR
Philippe Biondi and the Scapy community.
