#!/usr/bin/env python3
"""
Zara AI Assistant - LiveKit Agent Runner
This module handles the LiveKit connection and agent execution
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from livekit import agents
from livekit.agents import AgentSession, RoomInputOptions
from zara_voice_assistant import ZaraAssistant

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ZaraAgentRunner:
    """Manages the LiveKit agent connection and execution"""
    
    def __init__(self):
        self.agent = None
        self.session = None
        
    async def start_agent(self, room_name: str = "zara-room"):
        """Start the Zara agent with LiveKit connection"""
        try:
            # Validate environment variables
            required_env_vars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET']
            missing_vars = [var for var in required_env_vars if not os.getenv(var)]
            
            if missing_vars:
                logger.error(f"Missing environment variables: {missing_vars}")
                logger.error("Please check your .env file")
                return False
            
            logger.info("🚀 Starting Zara AI Agent...")
            logger.info(f"LiveKit URL: {os.getenv('LIVEKIT_URL')}")
            logger.info(f"Room: {room_name}")
            
            # Create Zara agent instance
            self.agent = ZaraAssistant()
            
            # Start the agent with LiveKit
            await agents.run_agent(
                agent=self.agent,
                room_url=os.getenv('LIVEKIT_URL'),
                api_key=os.getenv('LIVEKIT_API_KEY'),
                api_secret=os.getenv('LIVEKIT_API_SECRET'),
                room_name=room_name,
                options=RoomInputOptions(
                    auto_subscribe=True,
                    auto_publish=True,
                )
            )
            
            logger.info("✅ Zara agent started successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start Zara agent: {e}")
            return False
    
    async def stop_agent(self):
        """Stop the Zara agent"""
        try:
            if self.session:
                await self.session.close()
            logger.info("✅ Zara agent stopped")
        except Exception as e:
            logger.error(f"❌ Error stopping agent: {e}")

async def main():
    """Main entry point for running Zara agent"""
    runner = ZaraAgentRunner()
    
    try:
        await runner.start_agent()
    except KeyboardInterrupt:
        logger.info("👋 Zara agent shutdown requested")
        await runner.stop_agent()
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
