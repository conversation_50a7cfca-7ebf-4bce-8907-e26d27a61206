#!/usr/bin/env python3
"""
Zara AI Assistant - LiveKit Agent Runner
This module handles the LiveKit connection and agent execution
Based on Nova's implementation pattern
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from livekit import agents
from livekit.agents import AgentSession, RoomInputOptions
from livekit.plugins import noise_cancellation
from zara_voice_assistant import ZaraAssistant

# Load environment variables
load_dotenv()

# Configure logging with UTF-8 encoding to handle emojis
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zara_agent.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def entrypoint(ctx: agents.JobContext):
    """Main entry point for the Zara agent - based on Nova's pattern"""
    max_retries = 3
    retry_delay = 5

    for attempt in range(1, max_retries + 1):
        try:
            print(f"🚀 Starting Zara AI Agent (attempt {attempt})...")
            print(f"🔗 LiveKit URL: {os.getenv('LIVEKIT_URL')}")
            print(f"🏠 Room: {ctx.room.name}")

            # Create Zara agent first
            agent = ZaraAssistant()

            # Create session with the agent
            session = AgentSession()

            # Start session with proper configuration
            try:
                await asyncio.wait_for(
                    session.start(
                        room=ctx.room,
                        agent=agent,
                        room_input_options=RoomInputOptions(
                            video_enabled=True,
                            noise_cancellation=noise_cancellation.BVC(),
                        ),
                    ),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                raise Exception("Connection timeout")

            # Connect to the room
            await ctx.connect()

            # Generate startup message
            try:
                await session.generate_reply(
                    instructions="नमस्ते! मैं Zara हूँ। मैं आपकी सहायता के लिए तैयार हूँ। आप मुझसे हिंदी या अंग्रेजी में बात कर सकते हैं।"
                )
            except Exception as e:
                print(f"⚠️ Failed to generate startup message: {e}")
                await session.generate_reply(instructions="Hello! I'm Zara, ready to assist you.")

            # Check for reminders
            try:
                await agent._check_reminders()
            except Exception as e:
                print(f"⚠️ Failed to check reminders: {e}")

            print("✅ Zara agent session started successfully and is now live!")
            break

        except Exception as e:
            print(f"❌ Entrypoint failed on attempt {attempt}: {e}")
            if attempt < max_retries:
                print(f"🔄 Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
            else:
                print("❌ Max retries exceeded. Agent startup failed.")
                raise

class ZaraAgentRunner:
    """Manages the LiveKit agent using the CLI runner"""

    def __init__(self):
        self.worker_options = None

    async def start_agent(self):
        """Start the Zara agent using LiveKit CLI runner"""
        try:
            # Validate environment variables
            required_env_vars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET']
            missing_vars = [var for var in required_env_vars if not os.getenv(var)]

            if missing_vars:
                print(f"❌ Missing environment variables: {missing_vars}")
                print("Please check your .env file")
                return False

            # Create worker options
            self.worker_options = agents.WorkerOptions(entrypoint_fnc=entrypoint)

            # Run the agent using LiveKit CLI
            agents.cli.run_app(self.worker_options)

            return True

        except Exception as e:
            print(f"❌ Failed to start Zara agent: {e}")
            return False

    async def stop_agent(self):
        """Stop the Zara agent"""
        try:
            print("✅ Zara agent stopped")
        except Exception as e:
            print(f"❌ Error stopping agent: {e}")

def main():
    """Main entry point for running Zara agent"""
    # Validate environment variables first
    required_env_vars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET']
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please check your .env file")
        return

    # Run using LiveKit CLI
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))

if __name__ == "__main__":
    main()
