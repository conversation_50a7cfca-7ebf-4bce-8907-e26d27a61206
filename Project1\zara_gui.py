#!/usr/bin/env python3
"""
Zara AI Assistant - GUI Application
Created by <PERSON><PERSON><PERSON>
Based on Nova architecture but customized for Zara
"""

import sys
import asyncio
import threading
import logging
import os
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from livekit.agents import Agent, AgentSession, RoomInputOptions
from livekit import rtc
import json
import os

# Import Zara components
from zara_voice_assistant import ZaraAssistant
from firebase_config import firebase_manager
from zara_agent_runner import ZaraAgentRunner

class ZaraGUI(QMainWindow):
    """Main GUI window for Zara AI Assistant"""
    
    def __init__(self):
        super().__init__()
        self.agent_runner = None
        self.agent = None
        self.session = None
        self.is_connected = False
        self.agent_thread = None
        self.setup_ui()
        self.setup_logging()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("Zara AI Assistant v1.0")
        self.setGeometry(100, 100, 800, 600)
        
        # Set window icon and styling
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a2e, stop:1 #16213e);
            }
            QLabel {
                color: #ffffff;
                font-family: 'Segoe UI';
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #764ba2, stop:1 #667eea);
            }
            QPushButton:pressed {
                background: #4a5568;
            }
            QTextEdit {
                background: rgba(255, 255, 255, 0.1);
                color: #ffffff;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', monospace;
            }
        """)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = QHBoxLayout()
        
        # Zara logo/title
        title_label = QLabel("🤖 ZARA AI ASSISTANT")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin: 10px;
        """)
        header_layout.addWidget(title_label)
        
        # Status indicator
        self.status_label = QLabel("🔴 Disconnected")
        self.status_label.setStyleSheet("font-size: 14px; margin: 10px;")
        header_layout.addWidget(self.status_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.connect_btn = QPushButton("🚀 Start Zara")
        self.connect_btn.clicked.connect(self.toggle_connection)
        button_layout.addWidget(self.connect_btn)
        
        self.visual_btn = QPushButton("👁️ Enable Vision")
        self.visual_btn.clicked.connect(self.toggle_visual_analysis)
        self.visual_btn.setEnabled(False)
        button_layout.addWidget(self.visual_btn)
        
        self.settings_btn = QPushButton("⚙️ Settings")
        self.settings_btn.clicked.connect(self.show_settings)
        button_layout.addWidget(self.settings_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # Chat log
        self.chat_log = QTextEdit()
        self.chat_log.setReadOnly(True)
        self.chat_log.setPlaceholderText("Zara's conversation log will appear here...")
        layout.addWidget(self.chat_log)
        
        # Input area
        input_layout = QHBoxLayout()
        
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("Type a message to Zara... (or use voice)")
        self.input_field.setStyleSheet("""
            QLineEdit {
                background: rgba(255, 255, 255, 0.1);
                color: #ffffff;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        self.input_field.returnPressed.connect(self.send_text_message)
        input_layout.addWidget(self.input_field)
        
        send_btn = QPushButton("📤 Send")
        send_btn.clicked.connect(self.send_text_message)
        input_layout.addWidget(send_btn)
        
        layout.addLayout(input_layout)
        
        # Status bar
        self.statusBar().showMessage("Ready to start Zara AI Assistant")
        
        # Add welcome message
        self.add_chat_message("System", "Welcome to Zara AI Assistant! Click 'Start Zara' to begin.", "#667eea")
        
    def setup_logging(self):
        """Setup logging for the application"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('zara_gui.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def add_chat_message(self, sender: str, message: str, color: str = "#ffffff"):
        """Add a message to the chat log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"<span style='color: #888888;'>[{timestamp}]</span> <span style='color: {color}; font-weight: bold;'>{sender}:</span> <span style='color: #ffffff;'>{message}</span>"
        self.chat_log.append(formatted_message)
        
        # Auto-scroll to bottom
        scrollbar = self.chat_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def toggle_connection(self):
        """Toggle Zara connection"""
        if not self.is_connected:
            self.start_zara()
        else:
            self.stop_zara()
            
    def start_zara(self):
        """Start Zara AI Assistant"""
        try:
            self.add_chat_message("System", "Starting Zara AI Assistant...", "#00ff00")

            # Check environment variables
            required_vars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET']
            missing_vars = [var for var in required_vars if not os.getenv(var)]

            if missing_vars:
                self.add_chat_message("Error", f"Missing environment variables: {', '.join(missing_vars)}", "#ff0000")
                self.add_chat_message("System", "Please check your .env file and restart Zara", "#ffaa00")
                return

            # For GUI mode, we'll create a local agent instance for text interaction
            # The full voice agent should be run separately using the CLI
            self.agent = ZaraAssistant()

            # Update UI
            self.is_connected = True
            self.connect_btn.setText("🛑 Stop Zara")
            self.visual_btn.setEnabled(True)
            self.status_label.setText("🟢 Connected (Text Mode)")
            self.statusBar().showMessage("Zara is active in text mode!")

            self.add_chat_message("Zara", "नमस्ते! मैं Zara हूँ। मैं आपकी सहायता के लिए तैयार हूँ। आप मुझसे हिंदी या अंग्रेजी में बात कर सकते हैं।", "#667eea")
            self.add_chat_message("System", "Text mode active. For voice interaction, run: python run_zara.py voice", "#888888")
            self.add_chat_message("System", f"LiveKit URL: {os.getenv('LIVEKIT_URL')}", "#888888")

        except Exception as e:
            self.add_chat_message("Error", f"Failed to start Zara: {str(e)}", "#ff0000")
            self.logger.error(f"Failed to start Zara: {e}")
            
    def stop_zara(self):
        """Stop Zara AI Assistant"""
        try:
            self.add_chat_message("System", "Stopping Zara AI Assistant...", "#ffaa00")

            if self.agent_runner:
                # Stop the agent runner
                asyncio.create_task(self.agent_runner.stop_agent())
                self.agent_runner = None

            if self.agent:
                # Cleanup agent
                self.agent = None

            # Update UI
            self.is_connected = False
            self.connect_btn.setText("🚀 Start Zara")
            self.visual_btn.setEnabled(False)
            self.status_label.setText("🔴 Disconnected")
            self.statusBar().showMessage("Zara has been stopped")

            self.add_chat_message("Zara", "अलविदा! मैं बंद हो रही हूँ। फिर मिलते हैं!", "#667eea")

        except Exception as e:
            self.add_chat_message("Error", f"Error stopping Zara: {str(e)}", "#ff0000")
            self.logger.error(f"Error stopping Zara: {e}")
            
    def toggle_visual_analysis(self):
        """Toggle visual analysis"""
        if self.agent and hasattr(self.agent, '_visual_analysis_enabled'):
            current_state = self.agent._visual_analysis_enabled
            new_state = not current_state
            
            # This would need to be implemented as an async call in a real implementation
            if new_state:
                self.visual_btn.setText("👁️ Disable Vision")
                self.add_chat_message("System", "Visual analysis enabled - Zara can now see!", "#00ff00")
            else:
                self.visual_btn.setText("👁️ Enable Vision")
                self.add_chat_message("System", "Visual analysis disabled", "#ffaa00")
                
    def send_text_message(self):
        """Send a text message to Zara"""
        message = self.input_field.text().strip()
        if not message:
            return
            
        if not self.is_connected:
            self.add_chat_message("Error", "Please start Zara first!", "#ff0000")
            return
            
        # Add user message to chat
        self.add_chat_message("You", message, "#00ffff")
        self.input_field.clear()
        
        # In a real implementation, this would send the message to the agent
        # For now, we'll just simulate a response
        self.simulate_zara_response(message)
        
    def simulate_zara_response(self, user_message: str):
        """Simulate Zara's response (placeholder for real implementation)"""
        # This is a placeholder - in the real implementation, 
        # this would interface with the actual Zara agent
        
        responses = [
            "मैं आपकी बात समझ गई हूँ। मैं इस पर काम कर रही हूँ।",
            "यह एक दिलचस्प सवाल है। मुझे इसका जवाब ढूंढने दीजिए।",
            "आपकी मदद करना मेरी खुशी है। क्या आप और कुछ जानना चाहते हैं?",
            "I understand your request. Let me help you with that.",
            "That's a great question! I'm processing your request now."
        ]
        
        import random
        response = random.choice(responses)
        
        # Simulate processing delay
        QTimer.singleShot(1000, lambda: self.add_chat_message("Zara", response, "#667eea"))
        
    def show_settings(self):
        """Show settings dialog"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Zara Settings")
        dialog.setModal(True)
        dialog.resize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # Firebase status
        firebase_status = "✅ Connected" if firebase_manager.db_client else "❌ Not Connected"
        layout.addWidget(QLabel(f"Firebase Status: {firebase_status}"))
        
        # Language preference
        layout.addWidget(QLabel("Preferred Language:"))
        lang_combo = QComboBox()
        lang_combo.addItems(["Hindi (हिंदी)", "English", "Mixed"])
        layout.addWidget(lang_combo)
        
        # Voice settings
        layout.addWidget(QLabel("Voice Settings:"))
        voice_combo = QComboBox()
        voice_combo.addItems(["Aoede", "Charon", "Default"])
        layout.addWidget(voice_combo)
        
        # Buttons
        button_layout = QHBoxLayout()
        save_btn = QPushButton("Save")
        save_btn.clicked.connect(dialog.accept)
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        dialog.exec_()
        
    def closeEvent(self, event):
        """Handle application close"""
        if self.is_connected:
            self.stop_zara()
        event.accept()

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Zara AI Assistant")
    app.setApplicationVersion("1.0")
    
    # Set application icon
    app.setWindowIcon(QIcon("zara_icon.png"))  # Add icon file if available
    
    window = ZaraGUI()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
