import asyncio
from livekit import rtc
from livekit.agents.utils import images
import asyncio
from livekit.rtc import VideoBufferType
from typing import Any, List, Optional
from dotenv import load_dotenv
from datetime import datetime
from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.plugins import noise_cancellation
from livekit.plugins import google
from livekit.agents.llm.chat_context import ChatContext
import numpy as np
import cv2
from collections import deque
import json

# Import prompts and tools
from prompts import (
    AGENT_INSTRUCTION,
    SESSION_INSTRUCTION,
    AGENT_INSTRUCTION_FOR_TOOLS
)

from tools import (
    get_weather,
    search_web,
    play_media,
    get_time_info,
    system_power_action,
    manage_window,
    desktop_control,
    list_active_windows,
    manage_window_state,
    get_today_reminder_message_from_db,
    say_reminder,
    send_whatsapp_message,
    write_in_notepad,
    open_app,
    press_key,
    get_system_info,
    type_user_message_auto,
    scan_system_for_viruses,
    get_analysis_report,
    get_analysis_status,
    get_top_insights,
    get_data_summary,
    export_results,
    full_analysis_with_report,
    create_quick_advanced_graph,
    advanced_network_scan
)

try:
    from livekit.plugins.google.beta.realtime.custom_plugins import EffectPlugin
except ImportError:
    # Create a dummy EffectPlugin class if not available
    class EffectPlugin:
        @staticmethod
        def register_effect_callback(callback):
            pass

import time
import logging
from logging.handlers import RotatingFileHandler  # For log rotation

load_dotenv()

class ZaraAssistant(Agent):
    def __init__(self) -> None:
        # Initialize tools with validation
        import tools
        tools.assistant_instance = self
        self._tools = self._initialize_tools([
            get_weather,
            search_web,
            play_media,
            get_time_info,
            system_power_action,
            manage_window,
            desktop_control,
            list_active_windows,
            manage_window_state,
            send_whatsapp_message,
            write_in_notepad,
            open_app,
            press_key,
            get_system_info,
            type_user_message_auto,
            scan_system_for_viruses,
            get_analysis_status,
            get_analysis_report,
            get_data_summary,
            get_top_insights,
            full_analysis_with_report,      
            export_results,
            create_quick_advanced_graph,
            # advanced_network_scan
        ])

        super().__init__(
            instructions=self._build_instructions(),
            llm=google.beta.realtime.RealtimeModel(
                voice="Aoede",  # Changed to a different voice for Zara
                temperature=0.6,  # Slightly more creative
                top_p=0.85,
            ),
            tools=self._tools,
        )
        
        # State tracking
        self._last_tool_used: Optional[str] = None
        self._last_tool_success: bool = False
        self._chat_log_path = "zara_chat_log.txt"  # Zara's own log file
        EffectPlugin.register_effect_callback(self._trigger_gui_effect)
        self._min_frame_interval: float = 0.4  # Slightly faster processing
        self._max_buffer_size: int = 25        # Larger buffer for better context
        self._max_frames_to_send: int = 4      # Send more frames for better analysis
        
        # Visual Analysis Runtime State
        self._frame_buffer = deque(maxlen=self._max_buffer_size)
        self._last_frame_time: float = 0
        self._visual_analysis_enabled: bool = False
        self._is_analyzing: bool = False
        self._analysis_lock = asyncio.Lock()

    async def enable_visual_analysis(self, enable: bool):
        """Toggle visual analysis with proper frame refresh"""
        self._visual_analysis_enabled = enable
        if enable:
            self._frame_buffer.clear()
            self._last_frame_time = 0
            return "Visual analysis activated - Zara is now watching"
        else:
            self._frame_buffer.clear()
            if hasattr(self.llm, "session") and self.llm.session():
                self.llm.session().clear_video()
            return "Visual analysis deactivated - Zara's eyes are closed"

    async def process_visual_frame(self, frame: rtc.VideoFrame):
        """
        Process incoming frames with enhanced efficiency for Zara.
        """
        if not self._visual_analysis_enabled:
            return
            
        current_time = time.time()
        if current_time - self._last_frame_time < self._min_frame_interval:
            return
            
        try:
            if frame.type != VideoBufferType.RGBA:
                frame = frame.convert(VideoBufferType.RGBA)
                
            frame.timestamp = current_time
            self._frame_buffer.append(frame)
            self._last_frame_time = current_time
            
        except Exception as e:
            logger = getattr(self, 'logger', logging)
            logger.error(f"Zara frame processing error: {str(e)}")
            self._frame_buffer.clear()

    async def analyze_current_scene(self, prompt: str) -> str:
        """
        Zara's enhanced scene analysis with improved context understanding.
        """
        if not self._visual_analysis_enabled:
            return "Please activate my visual sensors first"
            
        if not self._frame_buffer:
            return "No visual data available for analysis"

        async with self._analysis_lock:
            try:
                self._is_analyzing = True
                
                # Enhanced frame selection for better analysis
                frames_to_send = []
                buffer_len = len(self._frame_buffer)
                
                if buffer_len > 0:
                    # More sophisticated frame selection
                    if buffer_len <= self._max_frames_to_send:
                        frames_to_send = list(self._frame_buffer)
                    else:
                        # Select frames with better distribution
                        step = buffer_len // self._max_frames_to_send
                        indices = [i * step for i in range(self._max_frames_to_send)]
                        frames_to_send = [self._frame_buffer[i] for i in indices]

                if not frames_to_send:
                    return "No frames selected for analysis"
                    
                session = self.llm.session()
                if not session:
                    return "Session not available"
                
                session.clear_video()
                for frame in frames_to_send:
                    session.push_video(frame)
                
                enhanced_prompt = (
                    f"{prompt}\n"
                    f"Context: Analyzing {len(frames_to_send)} representative frames.\n"
                    "Please provide detailed analysis with insights and observations."
                )
                
                response = await session.generate_reply(
                    instructions=enhanced_prompt,
                    timeout=25.0
                )
                return response.text_content
                
            except asyncio.TimeoutError:
                return "Analysis timeout - please try again"
            except Exception as e:
                logger = getattr(self, 'logger', logging)
                logger.error(f"Zara analysis error: {str(e)}")
                return "Analysis error occurred"
            finally:
                self._is_analyzing = False

    def _trigger_gui_effect(self):
        # This will be called by the plugin system
        pass  # Implementation in zara_gui.py

    def _initialize_tools(self, tools: List[Any]) -> List[Any]:
        """Validate and initialize tools with proper error handling."""
        validated_tools = []
        for tool in tools:
            try:
                if not callable(tool):
                    raise ValueError(f"Tool {getattr(tool, '__name__', str(tool))} is not callable")
                if not tool.__doc__:
                    print(f"⚠️ Warning: Tool {tool.__name__} missing docstring")
                
                # Add tool description metadata
                tool.metadata = {
                    'description': tool.__doc__.strip() if tool.__doc__ else f"Tool: {tool.__name__}",
                    'last_used': None,
                    'usage_count': 0
                }
                validated_tools.append(tool)
                print(f"✅ Zara initialized tool: {tool.__name__}")
            except Exception as e:
                print(f"⚠️ Zara failed to initialize tool {getattr(tool, '__name__', str(tool))}: {str(e)}")
        
        print(f"🤖 Zara total tools initialized: {len(validated_tools)}")
        return validated_tools

    def _build_instructions(self) -> str:
        """Construct the complete instruction set for Zara."""
        tool_descriptions = []
        for tool in self._tools:
            tool_name = tool.__name__
            tool_doc = tool.__doc__ if tool.__doc__ else "No description available"
            tool_descriptions.append(f"- {tool_name}: {tool_doc.strip()}")
        
        available_tools = "\n".join(tool_descriptions)
        
        return "\n".join([
            AGENT_INSTRUCTION,
            SESSION_INSTRUCTION,
            AGENT_INSTRUCTION_FOR_TOOLS,
            "You are Zara, a sophisticated multilingual AI assistant with advanced capabilities.",
            f"\nAvailable tools:\n{available_tools}",
            "\nWhen a user request requires tool usage, actively use the appropriate tool and provide feedback about the action taken."
        ])

    async def on_tool_call_start(self, tool_call):
        """Handle tool call start event."""
        print(f"🔧 Zara starting tool call: {tool_call.function_info.name}")
        self._last_tool_used = tool_call.function_info.name
        return await super().on_tool_call_start(tool_call)

    async def on_tool_call_end(self, tool_call, result):
        """Handle tool call completion."""
        success = result and not isinstance(result, Exception)
        self._last_tool_success = success
        
        print(f"🔧 Zara tool call completed: {tool_call.function_info.name} - {'✅ Success' if success else '❌ Failed'}")
        if not success and result:
            print(f"   Error: {result}")
        
        return await super().on_tool_call_end(tool_call, result)

    async def on_user_turn_completed(self, turn_ctx, new_message):
        """Handle post-processing after user turn completion."""
        user_message = turn_ctx.user_message.text_content if turn_ctx.user_message else "[no user input]"
        assistant_message = new_message.text_content if new_message else "[no assistant reply]"

        # Log conversation
        print(f"\n🗣️ USER: {user_message}")
        print(f"🤖 ZARA: {assistant_message}")
        await self._log_conversation("User", user_message)
        await self._log_conversation("Zara", assistant_message)

        # Update tool usage tracking
        if self._last_tool_used:
            for tool in self._tools:
                if tool.__name__ == self._last_tool_used:
                    tool.metadata['last_used'] = datetime.now()
                    tool.metadata['usage_count'] += 1
                    break

        return await super().on_user_turn_completed(turn_ctx, new_message)

    async def _log_conversation(self, sender: str, message: str) -> None:
        """Log conversation to file with proper encoding and error handling."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        try:
            with open(self._chat_log_path, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {sender}: {message}\n")
        except IOError as e:
            print(f"⚠️ Zara failed to log conversation: {str(e)}")

    async def _check_reminders(self) -> None:
        """Check and announce any reminders."""
        try:
            reminder_text = await get_today_reminder_message_from_db()
            if reminder_text:
                await say_reminder(reminder_text)
        except Exception as e:
            print(f"⚠️ Zara failed to check reminders: {str(e)}")
