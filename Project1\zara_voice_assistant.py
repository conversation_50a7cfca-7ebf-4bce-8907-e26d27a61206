import asyncio
from livekit import rtc
from livekit.agents.utils import images
import asyncio
from livekit.rtc import VideoBufferType
from typing import Any, List, Optional
from dotenv import load_dotenv
from datetime import datetime
from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.plugins import noise_cancellation
from livekit.plugins import google
from livekit.agents.llm.chat_context import ChatContext
import numpy as np
import cv2
from collections import deque
import json

# Import Advanced Zara Systems
try:
    from advanced_zara_brain import AdvancedZaraBrain
    from advanced_vision_system import AdvancedVisionSystem
    from advanced_automation_system import AdvancedAutomationSystem
    ADVANCED_SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Advanced systems not available: {e}")
    ADVANCED_SYSTEMS_AVAILABLE = False

# Import prompts and tools
from prompts import (
    AGENT_INSTRUCTION,
    SESSION_INSTRUCTION,
    AGENT_INSTRUCTION_FOR_TOOLS
)

from tools import (
    get_weather,
    search_web,
    play_media,
    get_time_info,
    system_power_action,
    manage_window,
    desktop_control,
    list_active_windows,
    manage_window_state,
    get_today_reminder_message_from_db,
    say_reminder,
    send_whatsapp_message,
    write_in_notepad,
    open_app,
    press_key,
    get_system_info,
    type_user_message_auto,
    scan_system_for_viruses,
    get_analysis_report,
    get_analysis_status,
    get_top_insights,
    get_data_summary,
    export_results,
    full_analysis_with_report,
    create_quick_advanced_graph,
    advanced_network_scan
)

try:
    from livekit.plugins.google.beta.realtime.custom_plugins import EffectPlugin
except ImportError:
    # Create a dummy EffectPlugin class if not available
    class EffectPlugin:
        @staticmethod
        def register_effect_callback(callback):
            pass

import time
import logging
from logging.handlers import RotatingFileHandler  # For log rotation

load_dotenv()

class ZaraAssistant(Agent):
    def __init__(self) -> None:
        # Initialize tools with validation
        import tools
        tools.assistant_instance = self
        self._tools = self._initialize_tools([
            get_weather,
            search_web,
            play_media,
            get_time_info,
            system_power_action,
            manage_window,
            desktop_control,
            list_active_windows,
            manage_window_state,
            send_whatsapp_message,
            write_in_notepad,
            open_app,
            press_key,
            get_system_info,
            type_user_message_auto,
            scan_system_for_viruses,
            get_analysis_status,
            get_analysis_report,
            get_data_summary,
            get_top_insights,
            full_analysis_with_report,
            export_results,
            create_quick_advanced_graph,
            # advanced_network_scan
        ])

        # Initialize Advanced Systems
        self.advanced_brain = None
        self.vision_system = None
        self.automation_system = None

        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                self.advanced_brain = AdvancedZaraBrain()
                self.vision_system = AdvancedVisionSystem()
                self.automation_system = AdvancedAutomationSystem()
                print("✅ Advanced Zara systems initialized")
            except Exception as e:
                print(f"⚠️ Failed to initialize advanced systems: {e}")
                ADVANCED_SYSTEMS_AVAILABLE = False

        super().__init__(
            instructions=self._build_instructions(),
            llm=google.beta.realtime.RealtimeModel(
                voice="Aoede",  # Changed to a different voice for Zara
                temperature=0.6,  # Slightly more creative
                top_p=0.85,
            ),
            tools=self._tools,
        )
        
        # State tracking
        self._last_tool_used: Optional[str] = None
        self._last_tool_success: bool = False
        self._chat_log_path = "zara_chat_log.txt"  # Zara's own log file
        EffectPlugin.register_effect_callback(self._trigger_gui_effect)
        self._min_frame_interval: float = 0.4  # Slightly faster processing
        self._max_buffer_size: int = 25        # Larger buffer for better context
        self._max_frames_to_send: int = 4      # Send more frames for better analysis
        
        # Visual Analysis Runtime State
        self._frame_buffer = deque(maxlen=self._max_buffer_size)
        self._last_frame_time: float = 0
        self._visual_analysis_enabled: bool = False
        self._is_analyzing: bool = False
        self._analysis_lock = asyncio.Lock()

    async def enable_visual_analysis(self, enable: bool):
        """Toggle visual analysis with proper frame refresh"""
        self._visual_analysis_enabled = enable
        if enable:
            self._frame_buffer.clear()
            self._last_frame_time = 0
            return "Visual analysis activated - Zara is now watching"
        else:
            self._frame_buffer.clear()
            if hasattr(self.llm, "session") and self.llm.session():
                self.llm.session().clear_video()
            return "Visual analysis deactivated - Zara's eyes are closed"

    async def process_visual_frame(self, frame: rtc.VideoFrame):
        """
        Process incoming frames with enhanced efficiency for Zara.
        """
        if not self._visual_analysis_enabled:
            return
            
        current_time = time.time()
        if current_time - self._last_frame_time < self._min_frame_interval:
            return
            
        try:
            if frame.type != VideoBufferType.RGBA:
                frame = frame.convert(VideoBufferType.RGBA)
                
            frame.timestamp = current_time
            self._frame_buffer.append(frame)
            self._last_frame_time = current_time
            
        except Exception as e:
            logger = getattr(self, 'logger', logging)
            logger.error(f"Zara frame processing error: {str(e)}")
            self._frame_buffer.clear()

    async def analyze_current_scene(self, prompt: str) -> str:
        """
        Zara's enhanced scene analysis with improved context understanding.
        """
        if not self._visual_analysis_enabled:
            return "Please activate my visual sensors first"
            
        if not self._frame_buffer:
            return "No visual data available for analysis"

        async with self._analysis_lock:
            try:
                self._is_analyzing = True
                
                # Enhanced frame selection for better analysis
                frames_to_send = []
                buffer_len = len(self._frame_buffer)
                
                if buffer_len > 0:
                    # More sophisticated frame selection
                    if buffer_len <= self._max_frames_to_send:
                        frames_to_send = list(self._frame_buffer)
                    else:
                        # Select frames with better distribution
                        step = buffer_len // self._max_frames_to_send
                        indices = [i * step for i in range(self._max_frames_to_send)]
                        frames_to_send = [self._frame_buffer[i] for i in indices]

                if not frames_to_send:
                    return "No frames selected for analysis"
                    
                session = self.llm.session()
                if not session:
                    return "Session not available"
                
                session.clear_video()
                for frame in frames_to_send:
                    session.push_video(frame)
                
                enhanced_prompt = (
                    f"{prompt}\n"
                    f"Context: Analyzing {len(frames_to_send)} representative frames.\n"
                    "Please provide detailed analysis with insights and observations."
                )
                
                response = await session.generate_reply(
                    instructions=enhanced_prompt,
                    timeout=25.0
                )
                return response.text_content
                
            except asyncio.TimeoutError:
                return "Analysis timeout - please try again"
            except Exception as e:
                logger = getattr(self, 'logger', logging)
                logger.error(f"Zara analysis error: {str(e)}")
                return "Analysis error occurred"
            finally:
                self._is_analyzing = False

    def _trigger_gui_effect(self):
        # This will be called by the plugin system
        pass  # Implementation in zara_gui.py

    def _initialize_tools(self, tools: List[Any]) -> List[Any]:
        """Validate and initialize tools with proper error handling."""
        validated_tools = []
        for tool in tools:
            try:
                if not callable(tool):
                    raise ValueError(f"Tool {getattr(tool, '__name__', str(tool))} is not callable")
                if not tool.__doc__:
                    print(f"⚠️ Warning: Tool {tool.__name__} missing docstring")
                
                # Add tool description metadata
                tool.metadata = {
                    'description': tool.__doc__.strip() if tool.__doc__ else f"Tool: {tool.__name__}",
                    'last_used': None,
                    'usage_count': 0
                }
                validated_tools.append(tool)
                print(f"✅ Zara initialized tool: {tool.__name__}")
            except Exception as e:
                print(f"⚠️ Zara failed to initialize tool {getattr(tool, '__name__', str(tool))}: {str(e)}")
        
        print(f"🤖 Zara total tools initialized: {len(validated_tools)}")
        return validated_tools

    def _build_instructions(self) -> str:
        """Construct the complete instruction set for Zara."""
        tool_descriptions = []
        for tool in self._tools:
            tool_name = tool.__name__
            tool_doc = tool.__doc__ if tool.__doc__ else "No description available"
            tool_descriptions.append(f"- {tool_name}: {tool_doc.strip()}")
        
        available_tools = "\n".join(tool_descriptions)
        
        return "\n".join([
            AGENT_INSTRUCTION,
            SESSION_INSTRUCTION,
            AGENT_INSTRUCTION_FOR_TOOLS,
            "You are Zara, a sophisticated multilingual AI assistant with advanced capabilities.",
            f"\nAvailable tools:\n{available_tools}",
            "\nWhen a user request requires tool usage, actively use the appropriate tool and provide feedback about the action taken."
        ])

    async def on_tool_call_start(self, tool_call):
        """Handle tool call start event."""
        print(f"🔧 Zara starting tool call: {tool_call.function_info.name}")
        self._last_tool_used = tool_call.function_info.name
        return await super().on_tool_call_start(tool_call)

    async def on_tool_call_end(self, tool_call, result):
        """Handle tool call completion."""
        success = result and not isinstance(result, Exception)
        self._last_tool_success = success
        
        print(f"🔧 Zara tool call completed: {tool_call.function_info.name} - {'✅ Success' if success else '❌ Failed'}")
        if not success and result:
            print(f"   Error: {result}")
        
        return await super().on_tool_call_end(tool_call, result)

    async def on_user_turn_completed(self, turn_ctx, new_message):
        """Handle post-processing after user turn completion."""
        user_message = turn_ctx.user_message.text_content if turn_ctx.user_message else "[no user input]"
        assistant_message = new_message.text_content if new_message else "[no assistant reply]"

        # Log conversation
        print(f"\n🗣️ USER: {user_message}")
        print(f"🤖 ZARA: {assistant_message}")
        await self._log_conversation("User", user_message)
        await self._log_conversation("Zara", assistant_message)

        # Update tool usage tracking
        if self._last_tool_used:
            for tool in self._tools:
                if tool.__name__ == self._last_tool_used:
                    tool.metadata['last_used'] = datetime.now()
                    tool.metadata['usage_count'] += 1
                    break

        return await super().on_user_turn_completed(turn_ctx, new_message)

    async def _log_conversation(self, sender: str, message: str) -> None:
        """Log conversation to file with proper encoding and error handling."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        try:
            with open(self._chat_log_path, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {sender}: {message}\n")
        except IOError as e:
            print(f"⚠️ Zara failed to log conversation: {str(e)}")

    async def _check_reminders(self) -> None:
        """Check and announce any reminders."""
        try:
            reminder_text = await get_today_reminder_message_from_db()
            if reminder_text:
                await say_reminder(reminder_text)
        except Exception as e:
            print(f"⚠️ Zara failed to check reminders: {str(e)}")

    async def start_advanced_systems(self):
        """Start all advanced Zara systems"""
        if not ADVANCED_SYSTEMS_AVAILABLE:
            print("⚠️ Advanced systems not available")
            return

        try:
            print("🧠 Starting Zara's advanced brain...")
            if self.advanced_brain:
                asyncio.create_task(self.advanced_brain.start_cognitive_loop())

            print("👁️ Starting advanced vision system...")
            if self.vision_system:
                asyncio.create_task(self.vision_system.start_vision_system())

            print("🤖 Starting automation system...")
            if self.automation_system:
                asyncio.create_task(self.automation_system.start_automation_system())

            print("✅ All advanced systems started")

        except Exception as e:
            print(f"❌ Failed to start advanced systems: {e}")

    async def process_advanced_command(self, command: str) -> str:
        """Process commands using advanced systems"""
        if not ADVANCED_SYSTEMS_AVAILABLE:
            return "Advanced systems not available"

        try:
            # Let the advanced brain process the command
            if self.advanced_brain:
                await self.advanced_brain.process_voice_command(command)

            # Check if it's an automation request
            if any(keyword in command.lower() for keyword in ['automate', 'workflow', 'execute']):
                return await self._handle_automation_request(command)

            # Check if it's a vision request
            if any(keyword in command.lower() for keyword in ['see', 'look', 'analyze', 'visual']):
                return await self._handle_vision_request(command)

            # Check if it's a learning request
            if any(keyword in command.lower() for keyword in ['learn', 'remember', 'pattern']):
                return await self._handle_learning_request(command)

            return "Command processed by advanced systems"

        except Exception as e:
            return f"Error processing advanced command: {str(e)}"

    async def _handle_automation_request(self, command: str) -> str:
        """Handle automation-related requests"""
        if not self.automation_system:
            return "Automation system not available"

        try:
            # Parse automation request
            if 'email' in command.lower():
                success = await self.automation_system.execute_workflow('send_email')
                return "Email workflow executed" if success else "Email workflow failed"

            elif 'organize' in command.lower() and 'download' in command.lower():
                success = await self.automation_system.execute_workflow('organize_downloads')
                return "Downloads organized" if success else "Organization failed"

            elif 'research' in command.lower():
                # Extract topic from command
                topic = command.lower().replace('research', '').strip()
                success = await self.automation_system.execute_workflow('web_research', {'topic': topic})
                return f"Research on '{topic}' completed" if success else "Research failed"

            elif 'cleanup' in command.lower() or 'maintenance' in command.lower():
                success = await self.automation_system.execute_workflow('system_cleanup')
                return "System cleanup completed" if success else "Cleanup failed"

            else:
                workflows = self.automation_system.get_available_workflows()
                return f"Available workflows: {', '.join(workflows)}"

        except Exception as e:
            return f"Automation error: {str(e)}"

    async def _handle_vision_request(self, command: str) -> str:
        """Handle vision-related requests"""
        if not self.vision_system:
            return "Vision system not available"

        try:
            if 'click' in command.lower():
                # Extract what to click
                target = command.lower().replace('click', '').replace('on', '').strip()
                success = await self.vision_system.smart_click(target)
                return f"Clicked on '{target}'" if success else f"Could not find '{target}'"

            elif 'analyze' in command.lower() or 'see' in command.lower():
                summary = self.vision_system.get_visual_summary()
                return f"Visual analysis: {summary['detected_objects']} objects, {summary['ui_elements']} UI elements, {summary['text_regions']} text regions detected"

            elif 'find' in command.lower():
                # Extract what to find
                target = command.lower().replace('find', '').strip()
                element = self.vision_system.find_element_by_text(target)
                return f"Found '{target}' at {element['center']}" if element else f"Could not find '{target}'"

            else:
                return "Vision system is active and monitoring"

        except Exception as e:
            return f"Vision error: {str(e)}"

    async def _handle_learning_request(self, command: str) -> str:
        """Handle learning-related requests"""
        if not self.advanced_brain:
            return "Learning system not available"

        try:
            status = self.advanced_brain.get_status()
            return f"Learning status: {status['knowledge_entries']} knowledge entries, {status['visual_memory_size']} visual memories"

        except Exception as e:
            return f"Learning error: {str(e)}"

    def get_advanced_status(self) -> Dict[str, Any]:
        """Get status of all advanced systems"""
        status = {
            'advanced_systems_available': ADVANCED_SYSTEMS_AVAILABLE,
            'brain_active': False,
            'vision_active': False,
            'automation_active': False
        }

        if ADVANCED_SYSTEMS_AVAILABLE:
            if self.advanced_brain:
                brain_status = self.advanced_brain.get_status()
                status.update({
                    'brain_active': brain_status['is_active'],
                    'cognitive_state': brain_status['cognitive_state'],
                    'active_tasks': brain_status['active_tasks']
                })

            if self.vision_system:
                vision_status = self.vision_system.get_visual_summary()
                status.update({
                    'vision_active': vision_status['is_active'],
                    'detected_objects': vision_status['detected_objects'],
                    'ui_elements': vision_status['ui_elements']
                })

            if self.automation_system:
                automation_stats = self.automation_system.get_execution_stats()
                status.update({
                    'automation_active': True,
                    'total_automations': automation_stats.get('total_executions', 0),
                    'success_rate': automation_stats.get('success_rate', 0)
                })

        return status

    async def shutdown_advanced_systems(self):
        """Shutdown all advanced systems"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                if self.advanced_brain:
                    await self.advanced_brain.shutdown()

                if self.vision_system:
                    await self.vision_system.shutdown()

                if self.automation_system:
                    await self.automation_system.shutdown()

                print("✅ All advanced systems shutdown")

            except Exception as e:
                print(f"❌ Error shutting down advanced systems: {e}")
