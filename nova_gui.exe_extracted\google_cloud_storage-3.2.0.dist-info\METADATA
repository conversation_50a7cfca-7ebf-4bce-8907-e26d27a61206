Metadata-Version: 2.4
Name: google-cloud-storage
Version: 3.2.0
Summary: Google Cloud Storage API client library
Home-page: https://github.com/googleapis/python-storage
Author: Google LLC
Author-email: <EMAIL>
License: Apache 2.0
Platform: Posix; MacOS X; Windows
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Operating System :: OS Independent
Classifier: Topic :: Internet
Requires-Python: >=3.7
License-File: LICENSE
Requires-Dist: google-auth<3.0.0,>=2.26.1
Requires-Dist: google-api-core<3.0.0,>=2.15.0
Requires-Dist: google-cloud-core<3.0.0,>=2.4.2
Requires-Dist: google-resumable-media<3.0.0,>=2.7.2
Requires-Dist: requests<3.0.0,>=2.22.0
Requires-Dist: google-crc32c<2.0.0,>=1.1.3
Provides-Extra: protobuf
Requires-Dist: protobuf<7.0.0,>=3.20.2; extra == "protobuf"
Provides-Extra: tracing
Requires-Dist: opentelemetry-api<2.0.0,>=1.1.0; extra == "tracing"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Python Client for Google Cloud Storage
======================================

|stable| |pypi| |versions|

`Google Cloud Storage`_ is a managed service for storing unstructured data. Cloud Storage
allows world-wide storage and retrieval of any amount of data at any time. You can use
Cloud Storage for a range of scenarios including serving website content, storing data
for archival and disaster recovery, or distributing large data objects to users via direct download.

**NOTE**: `3.0 Major Version Notes`_ are available. Feedback welcome.

A comprehensive list of changes in each version may be found in the `CHANGELOG`_.

- `Product Documentation`_
- `Client Library Documentation`_
- `github.com/googleapis/python-storage`_

Certain control plane and long-running operations for Cloud Storage (including Folder
and Managed Folder operations) are supported via the `Storage Control Client`_.
The `Storage Control API`_ creates one space to perform metadata-specific, control plane,
and long-running operations apart from the Storage API.

Read more about the client libraries for Cloud APIs, including the older
Google APIs Client Libraries, in `Client Libraries Explained`_.

.. |stable| image:: https://img.shields.io/badge/support-stable-gold.svg
   :target: https://github.com/googleapis/google-cloud-python/blob/main/README.rst#stability-levels
.. |pypi| image:: https://img.shields.io/pypi/v/google-cloud-storage.svg
   :target: https://pypi.org/project/google-cloud-storage/
.. |versions| image:: https://img.shields.io/pypi/pyversions/google-cloud-storage.svg
   :target: https://pypi.org/project/google-cloud-storage/
.. _Google Cloud Storage: https://cloud.google.com/storage
.. _Client Library Documentation: https://cloud.google.com/python/docs/reference/storage/latest/summary_overview
.. _Product Documentation:  https://cloud.google.com/storage
.. _CHANGELOG:  https://github.com/googleapis/python-storage/blob/main/CHANGELOG.md
.. _github.com/googleapis/python-storage: https://github.com/googleapis/python-storage
.. _Storage Control Client: https://cloud.google.com/python/docs/reference/google-cloud-storage-control/latest
.. _Storage Control API: https://cloud.google.com/storage/docs/reference/rpc/google.storage.control.v2
.. _Client Libraries Explained: https://cloud.google.com/apis/docs/client-libraries-explained


Quick Start
-----------

In order to use this library, you first need to go through the following steps.
A step-by-step guide may also be found in `Get Started with Client Libraries`_.

1. `Select or create a Cloud Platform project.`_
2. `Enable billing for your project.`_
3. `Enable the Google Cloud Storage API.`_
4. `Setup Authentication.`_

.. _Get Started with Client Libraries: https://cloud.google.com/storage/docs/reference/libraries#client-libraries-install-python
.. _Select or create a Cloud Platform project.: https://console.cloud.google.com/project
.. _Enable billing for your project.: https://cloud.google.com/billing/docs/how-to/modify-project#enable_billing_for_a_project
.. _Enable the Google Cloud Storage API.:  https://console.cloud.google.com/flows/enableapi?apiid=storage-api.googleapis.com
.. _Setup Authentication.: https://cloud.google.com/docs/authentication/client-libraries

Installation
~~~~~~~~~~~~

Install this library in a virtual environment using `venv`_. `venv`_ is a tool that
creates isolated Python environments. These isolated environments can have separate
versions of Python packages, which allows you to isolate one project's dependencies
from the dependencies of other projects.

With `venv`_, it's possible to install this library without needing system
install permissions, and without clashing with the installed system
dependencies.

.. _`venv`: https://docs.python.org/3/library/venv.html


Code samples and snippets
~~~~~~~~~~~~~~~~~~~~~~~~~

Code samples and snippets live in the `samples/`_ folder.

.. _`samples/`: https://github.com/googleapis/python-storage/tree/main/samples


Supported Python Versions
^^^^^^^^^^^^^^^^^^^^^^^^^
Our client libraries are compatible with all current `active`_ and `maintenance`_ versions of
Python.

Python >= 3.7

.. _active: https://devguide.python.org/devcycle/#in-development-main-branch
.. _maintenance: https://devguide.python.org/devcycle/#maintenance-branches

Unsupported Python Versions
^^^^^^^^^^^^^^^^^^^^^^^^^^^
Python <= 3.6

If you are using an `end-of-life`_
version of Python, we recommend that you update as soon as possible to an actively supported version.

.. _end-of-life: https://devguide.python.org/devcycle/#end-of-life-branches

Mac/Linux
^^^^^^^^^

.. code-block:: console

    python3 -m venv <your-env>
    source <your-env>/bin/activate
    pip install google-cloud-storage


Windows
^^^^^^^

.. code-block:: console

    py -m venv <your-env>
    .\<your-env>\Scripts\activate
    pip install google-cloud-storage


Example Usage
~~~~~~~~~~~~~

.. code-block:: python

    # Imports the Google Cloud client library
    from google.cloud import storage

    # Instantiates a client
    storage_client = storage.Client()

    # The name for the new bucket
    bucket_name = "my-new-bucket"

    # Creates the new bucket
    bucket = storage_client.create_bucket(bucket_name)

    print(f"Bucket {bucket.name} created.")


Tracing With OpenTelemetry
~~~~~~~~~~~~~~~~~~~~~~~~~~

This is a PREVIEW FEATURE: Coverage and functionality are still in development and subject to change.

This library can be configured to use `OpenTelemetry`_ to generate traces on calls to Google Cloud Storage.
For information on the benefits and utility of tracing, read the `Cloud Trace Overview <https://cloud.google.com/trace/docs/overview>`_.

To enable OpenTelemetry tracing in the Cloud Storage client, first install OpenTelemetry:

.. code-block:: console

    pip install google-cloud-storage[tracing]

Set the ``ENABLE_GCS_PYTHON_CLIENT_OTEL_TRACES`` environment variable to selectively opt-in tracing for the Cloud Storage client:

.. code-block:: console

    export ENABLE_GCS_PYTHON_CLIENT_OTEL_TRACES=True

You will also need to tell OpenTelemetry which exporter to use. An example to export traces to Google Cloud Trace can be found below.

.. code-block:: console

    # Install the Google Cloud Trace exporter and propagator, however you can use any exporter of your choice.
    pip install opentelemetry-exporter-gcp-trace opentelemetry-propagator-gcp

    # [Optional] Install the OpenTelemetry Requests Instrumentation to trace the underlying HTTP requests.
    pip install opentelemetry-instrumentation-requests

.. code-block:: python

    from opentelemetry import trace
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.exporter.cloud_trace import CloudTraceSpanExporter

    tracer_provider = TracerProvider()
    tracer_provider.add_span_processor(BatchSpanProcessor(CloudTraceSpanExporter()))
    trace.set_tracer_provider(tracer_provider)

    # Optional yet recommended to instrument the requests HTTP library
    from opentelemetry.instrumentation.requests import RequestsInstrumentor
    RequestsInstrumentor().instrument(tracer_provider=tracer_provider)

In this example, tracing data will be published to the `Google Cloud Trace`_ console.
Tracing is most effective when many libraries are instrumented to provide insight over the entire lifespan of a request.
For a list of libraries that can be instrumented, refer to the `OpenTelemetry Registry`_.

.. _OpenTelemetry: https://opentelemetry.io
.. _OpenTelemetry Registry: https://opentelemetry.io/ecosystem/registry
.. _Google Cloud Trace: https://cloud.google.com/trace


3.0 Major Version Notes
-----------------------

Feedback Welcome
~~~~~~~~~~~~~~~~

If you experience that backwards compatibility for your application is broken
with this major version release, please let us know through the Github issues
system. While some breaks of backwards compatibility may be unavoidable due to
new features in the major version release, we will do our best to minimize
them. Thank you.

Exception Handling
~~~~~~~~~~~~~~~~~~

In Python Storage 3.0, the dependency ``google-resumable-media`` was integrated.
The ``google-resumable-media`` dependency included exceptions
``google.resumable_media.common.InvalidResponse`` and
``google.resumable_media.common.DataCorruption``, which were often imported
directly in user application code. The replacements for these exceptions are
``google.cloud.storage.exceptions.InvalidResponse`` and
``google.cloud.storage.exceptions.DataCorruption``. Please update application code
to import and use these exceptions instead.

For backwards compatibility, if ``google-resumable-media`` is installed, the new
exceptions will be defined as subclasses of the old exceptions, so applications
should continue to work without modification. This backwards compatibility
feature may be removed in a future major version update.

Some users may be using the original exception classes from the
``google-resumable-media`` library without explicitly installing that library. So
as not to break user applications following this pattern,
``google-resumable-media`` is still in the list of dependencies in this package's
setup.py file. Applications which do not import directly from
``google-resumable-media`` can safely disregard this dependency.
This backwards compatibility feature **will be removed** in a future major
version update. Please migrate to using the ``google.cloud.storage.exceptions``
classes as above.

Checksum Defaults
~~~~~~~~~~~~~~~~~

In Python Storage 3.0, uploads and downloads now have a default of "auto" where
applicable. "Auto" will use crc32c checksums, except for unusual cases where the
fast (C extension) crc32c implementation is not available, in which case it will
use md5 instead. Before Python Storage 3.0, the default was md5 for most
downloads and None for most uploads. Note that ranged downloads ("start" or
"end" set) still do not support any checksumming, and some features in
``transfer_manager.py`` still support crc32c only.

Note: The method ``Blob.upload_from_file()`` requires a file in bytes mode, but
when checksum is set to None, as was the previous default, would not throw an
error if passed a file in string mode under some circumstances. With the new
defaults, it will now raise a TypeError. Please use a file opened in bytes
reading mode as required.

Miscellaneous
~~~~~~~~~~~~~

- The ``BlobWriter`` class now attempts to terminate an ongoing resumable upload if
  the writer exits with an exception.
- Retry behavior is now identical between media operations (uploads and
  downloads) and other operations, and custom predicates are now supported for
  media operations as well.
- ``Blob.download_as_filename()`` will now delete the empty file if it results in a
  google.cloud.exceptions.NotFound exception (HTTP 404).
- Previously, object upload, metadata update, and delete methods had retries
  disabled by default unless the generation or metageneration was specified in
  the request. This has now changed so that retries are enabled by default.


Next Steps
~~~~~~~~~~

-  Read the `Google Cloud Storage Product documentation`_ to learn
   more about the product and see How-to Guides.
-  Read the `Client Library Documentation`_ for Google Cloud Storage API
   to see other available methods on the client.
-  View this `README`_ to see the full list of Cloud
   APIs that we cover.

.. _Google Cloud Storage Product documentation:  https://cloud.google.com/storage
.. _README: https://github.com/googleapis/google-cloud-python/blob/main/README.rst
