Metadata-Version: 2.4
Name: livekit
Version: 1.0.11
Summary: Python Real-time SDK for LiveKit
Home-page: https://github.com/livekit/python-sdks
License: Apache-2.0
Project-URL: Documentation, https://docs.livekit.io
Project-URL: Website, https://livekit.io/
Project-URL: Source, https://github.com/livekit/python-sdks/
Keywords: webrtc,realtime,audio,video,livekit
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Video
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.9.0
Description-Content-Type: text/markdown
Requires-Dist: protobuf>=4.25.0
Requires-Dist: types-protobuf>=3
Requires-Dist: aiofiles>=24
Requires-Dist: numpy>=1.26
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: project-url
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# LiveKit SDK for Python

Python SDK to integrate LiveKit's real-time video, audio, and data capabilities into your Python applications using WebRTC. Designed for use with [LiveKit Agents](https://github.com/livekit/agents) to build powerful voice AI apps.

See https://docs.livekit.io/ for more information.

