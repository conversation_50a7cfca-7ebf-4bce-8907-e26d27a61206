# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: room.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import e2ee_pb2 as e2ee__pb2
from . import handle_pb2 as handle__pb2
from . import participant_pb2 as participant__pb2
from . import track_pb2 as track__pb2
from . import video_frame_pb2 as video__frame__pb2
from . import stats_pb2 as stats__pb2
from . import data_stream_pb2 as data__stream__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nroom.proto\x12\rlivekit.proto\x1a\ne2ee.proto\x1a\x0chandle.proto\x1a\x11participant.proto\x1a\x0btrack.proto\x1a\x11video_frame.proto\x1a\x0bstats.proto\x1a\x11\x64\x61ta_stream.proto\"Y\n\x0e\x43onnectRequest\x12\x0b\n\x03url\x18\x01 \x02(\t\x12\r\n\x05token\x18\x02 \x02(\t\x12+\n\x07options\x18\x03 \x02(\x0b\x32\x1a.livekit.proto.RoomOptions\"#\n\x0f\x43onnectResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\xbf\x03\n\x0f\x43onnectCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x0f\n\x05\x65rror\x18\x02 \x01(\tH\x00\x12\x37\n\x06result\x18\x03 \x01(\x0b\x32%.livekit.proto.ConnectCallback.ResultH\x00\x1a\x89\x01\n\x15ParticipantWithTracks\x12\x34\n\x0bparticipant\x18\x01 \x02(\x0b\x32\x1f.livekit.proto.OwnedParticipant\x12:\n\x0cpublications\x18\x02 \x03(\x0b\x32$.livekit.proto.OwnedTrackPublication\x1a\xb8\x01\n\x06Result\x12&\n\x04room\x18\x01 \x02(\x0b\x32\x18.livekit.proto.OwnedRoom\x12:\n\x11local_participant\x18\x02 \x02(\x0b\x32\x1f.livekit.proto.OwnedParticipant\x12J\n\x0cparticipants\x18\x03 \x03(\x0b\x32\x34.livekit.proto.ConnectCallback.ParticipantWithTracksB\t\n\x07message\"(\n\x11\x44isconnectRequest\x12\x13\n\x0broom_handle\x18\x01 \x02(\x04\"&\n\x12\x44isconnectResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"&\n\x12\x44isconnectCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\x82\x01\n\x13PublishTrackRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x14\n\x0ctrack_handle\x18\x02 \x02(\x04\x12\x33\n\x07options\x18\x03 \x02(\x0b\x32\".livekit.proto.TrackPublishOptions\"(\n\x14PublishTrackResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\x81\x01\n\x14PublishTrackCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x0f\n\x05\x65rror\x18\x02 \x01(\tH\x00\x12;\n\x0bpublication\x18\x03 \x01(\x0b\x32$.livekit.proto.OwnedTrackPublicationH\x00\x42\t\n\x07message\"g\n\x15UnpublishTrackRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\x12\x19\n\x11stop_on_unpublish\x18\x03 \x02(\x08\"*\n\x16UnpublishTrackResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"9\n\x16UnpublishTrackCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"\xb9\x01\n\x12PublishDataRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x10\n\x08\x64\x61ta_ptr\x18\x02 \x02(\x04\x12\x10\n\x08\x64\x61ta_len\x18\x03 \x02(\x04\x12\x10\n\x08reliable\x18\x04 \x02(\x08\x12\x1c\n\x10\x64\x65stination_sids\x18\x05 \x03(\tB\x02\x18\x01\x12\r\n\x05topic\x18\x06 \x01(\t\x12\x1e\n\x16\x64\x65stination_identities\x18\x07 \x03(\t\"\'\n\x13PublishDataResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"6\n\x13PublishDataCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"\xa6\x01\n\x1bPublishTranscriptionRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x1c\n\x14participant_identity\x18\x02 \x02(\t\x12\x10\n\x08track_id\x18\x03 \x02(\t\x12\x35\n\x08segments\x18\x04 \x03(\x0b\x32#.livekit.proto.TranscriptionSegment\"0\n\x1cPublishTranscriptionResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"?\n\x1cPublishTranscriptionCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"v\n\x15PublishSipDtmfRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x02(\r\x12\r\n\x05\x64igit\x18\x03 \x02(\t\x12\x1e\n\x16\x64\x65stination_identities\x18\x04 \x03(\t\"*\n\x16PublishSipDtmfResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"9\n\x16PublishSipDtmfCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"M\n\x17SetLocalMetadataRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x10\n\x08metadata\x18\x02 \x02(\t\",\n\x18SetLocalMetadataResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\";\n\x18SetLocalMetadataCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"\x84\x01\n\x16SendChatMessageRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x0f\n\x07message\x18\x02 \x02(\t\x12\x1e\n\x16\x64\x65stination_identities\x18\x03 \x03(\t\x12\x17\n\x0fsender_identity\x18\x04 \x01(\t\"\xbc\x01\n\x16\x45\x64itChatMessageRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x11\n\tedit_text\x18\x02 \x02(\t\x12\x34\n\x10original_message\x18\x03 \x02(\x0b\x32\x1a.livekit.proto.ChatMessage\x12\x1e\n\x16\x64\x65stination_identities\x18\x04 \x03(\t\x12\x17\n\x0fsender_identity\x18\x05 \x01(\t\"+\n\x17SendChatMessageResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"{\n\x17SendChatMessageCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x0f\n\x05\x65rror\x18\x02 \x01(\tH\x00\x12\x32\n\x0c\x63hat_message\x18\x03 \x01(\x0b\x32\x1a.livekit.proto.ChatMessageH\x00\x42\t\n\x07message\"q\n\x19SetLocalAttributesRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x32\n\nattributes\x18\x02 \x03(\x0b\x32\x1e.livekit.proto.AttributesEntry\"-\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x02(\t\x12\r\n\x05value\x18\x02 \x02(\t\".\n\x1aSetLocalAttributesResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"=\n\x1aSetLocalAttributesCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"E\n\x13SetLocalNameRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x0c\n\x04name\x18\x02 \x02(\t\"(\n\x14SetLocalNameResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"7\n\x14SetLocalNameCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"E\n\x14SetSubscribedRequest\x12\x11\n\tsubscribe\x18\x01 \x02(\x08\x12\x1a\n\x12publication_handle\x18\x02 \x02(\x04\"\x17\n\x15SetSubscribedResponse\"-\n\x16GetSessionStatsRequest\x12\x13\n\x0broom_handle\x18\x01 \x02(\x04\"+\n\x17GetSessionStatsResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\xf7\x01\n\x17GetSessionStatsCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x0f\n\x05\x65rror\x18\x02 \x01(\tH\x00\x12?\n\x06result\x18\x03 \x01(\x0b\x32-.livekit.proto.GetSessionStatsCallback.ResultH\x00\x1am\n\x06Result\x12\x30\n\x0fpublisher_stats\x18\x01 \x03(\x0b\x32\x17.livekit.proto.RtcStats\x12\x31\n\x10subscriber_stats\x18\x02 \x03(\x0b\x32\x17.livekit.proto.RtcStatsB\t\n\x07message\";\n\rVideoEncoding\x12\x13\n\x0bmax_bitrate\x18\x01 \x02(\x04\x12\x15\n\rmax_framerate\x18\x02 \x02(\x01\"$\n\rAudioEncoding\x12\x13\n\x0bmax_bitrate\x18\x01 \x02(\x04\"\x9a\x02\n\x13TrackPublishOptions\x12\x34\n\x0evideo_encoding\x18\x01 \x01(\x0b\x32\x1c.livekit.proto.VideoEncoding\x12\x34\n\x0e\x61udio_encoding\x18\x02 \x01(\x0b\x32\x1c.livekit.proto.AudioEncoding\x12.\n\x0bvideo_codec\x18\x03 \x01(\x0e\x32\x19.livekit.proto.VideoCodec\x12\x0b\n\x03\x64tx\x18\x04 \x01(\x08\x12\x0b\n\x03red\x18\x05 \x01(\x08\x12\x11\n\tsimulcast\x18\x06 \x01(\x08\x12*\n\x06source\x18\x07 \x01(\x0e\x32\x1a.livekit.proto.TrackSource\x12\x0e\n\x06stream\x18\x08 \x01(\t\"=\n\tIceServer\x12\x0c\n\x04urls\x18\x01 \x03(\t\x12\x10\n\x08username\x18\x02 \x01(\t\x12\x10\n\x08password\x18\x03 \x01(\t\"\xc4\x01\n\tRtcConfig\x12;\n\x12ice_transport_type\x18\x01 \x01(\x0e\x32\x1f.livekit.proto.IceTransportType\x12K\n\x1a\x63ontinual_gathering_policy\x18\x02 \x01(\x0e\x32\'.livekit.proto.ContinualGatheringPolicy\x12-\n\x0bice_servers\x18\x03 \x03(\x0b\x32\x18.livekit.proto.IceServer\"\xbe\x01\n\x0bRoomOptions\x12\x16\n\x0e\x61uto_subscribe\x18\x01 \x01(\x08\x12\x17\n\x0f\x61\x64\x61ptive_stream\x18\x02 \x01(\x08\x12\x10\n\x08\x64ynacast\x18\x03 \x01(\x08\x12(\n\x04\x65\x32\x65\x65\x18\x04 \x01(\x0b\x32\x1a.livekit.proto.E2eeOptions\x12,\n\nrtc_config\x18\x05 \x01(\x0b\x32\x18.livekit.proto.RtcConfig\x12\x14\n\x0cjoin_retries\x18\x06 \x01(\r\"w\n\x14TranscriptionSegment\x12\n\n\x02id\x18\x01 \x02(\t\x12\x0c\n\x04text\x18\x02 \x02(\t\x12\x12\n\nstart_time\x18\x03 \x02(\x04\x12\x10\n\x08\x65nd_time\x18\x04 \x02(\x04\x12\r\n\x05\x66inal\x18\x05 \x02(\x08\x12\x10\n\x08language\x18\x06 \x02(\t\"0\n\nBufferInfo\x12\x10\n\x08\x64\x61ta_ptr\x18\x01 \x02(\x04\x12\x10\n\x08\x64\x61ta_len\x18\x02 \x02(\x04\"e\n\x0bOwnedBuffer\x12-\n\x06handle\x18\x01 \x02(\x0b\x32\x1d.livekit.proto.FfiOwnedHandle\x12\'\n\x04\x64\x61ta\x18\x02 \x02(\x0b\x32\x19.livekit.proto.BufferInfo\"\xc6\x13\n\tRoomEvent\x12\x13\n\x0broom_handle\x18\x01 \x02(\x04\x12\x44\n\x15participant_connected\x18\x02 \x01(\x0b\x32#.livekit.proto.ParticipantConnectedH\x00\x12J\n\x18participant_disconnected\x18\x03 \x01(\x0b\x32&.livekit.proto.ParticipantDisconnectedH\x00\x12\x43\n\x15local_track_published\x18\x04 \x01(\x0b\x32\".livekit.proto.LocalTrackPublishedH\x00\x12G\n\x17local_track_unpublished\x18\x05 \x01(\x0b\x32$.livekit.proto.LocalTrackUnpublishedH\x00\x12\x45\n\x16local_track_subscribed\x18\x06 \x01(\x0b\x32#.livekit.proto.LocalTrackSubscribedH\x00\x12\x38\n\x0ftrack_published\x18\x07 \x01(\x0b\x32\x1d.livekit.proto.TrackPublishedH\x00\x12<\n\x11track_unpublished\x18\x08 \x01(\x0b\x32\x1f.livekit.proto.TrackUnpublishedH\x00\x12:\n\x10track_subscribed\x18\t \x01(\x0b\x32\x1e.livekit.proto.TrackSubscribedH\x00\x12>\n\x12track_unsubscribed\x18\n \x01(\x0b\x32 .livekit.proto.TrackUnsubscribedH\x00\x12K\n\x19track_subscription_failed\x18\x0b \x01(\x0b\x32&.livekit.proto.TrackSubscriptionFailedH\x00\x12\x30\n\x0btrack_muted\x18\x0c \x01(\x0b\x32\x19.livekit.proto.TrackMutedH\x00\x12\x34\n\rtrack_unmuted\x18\r \x01(\x0b\x32\x1b.livekit.proto.TrackUnmutedH\x00\x12G\n\x17\x61\x63tive_speakers_changed\x18\x0e \x01(\x0b\x32$.livekit.proto.ActiveSpeakersChangedH\x00\x12\x43\n\x15room_metadata_changed\x18\x0f \x01(\x0b\x32\".livekit.proto.RoomMetadataChangedH\x00\x12\x39\n\x10room_sid_changed\x18\x10 \x01(\x0b\x32\x1d.livekit.proto.RoomSidChangedH\x00\x12Q\n\x1cparticipant_metadata_changed\x18\x11 \x01(\x0b\x32).livekit.proto.ParticipantMetadataChangedH\x00\x12I\n\x18participant_name_changed\x18\x12 \x01(\x0b\x32%.livekit.proto.ParticipantNameChangedH\x00\x12U\n\x1eparticipant_attributes_changed\x18\x13 \x01(\x0b\x32+.livekit.proto.ParticipantAttributesChangedH\x00\x12M\n\x1a\x63onnection_quality_changed\x18\x14 \x01(\x0b\x32\'.livekit.proto.ConnectionQualityChangedH\x00\x12I\n\x18\x63onnection_state_changed\x18\x15 \x01(\x0b\x32%.livekit.proto.ConnectionStateChangedH\x00\x12\x33\n\x0c\x64isconnected\x18\x16 \x01(\x0b\x32\x1b.livekit.proto.DisconnectedH\x00\x12\x33\n\x0creconnecting\x18\x17 \x01(\x0b\x32\x1b.livekit.proto.ReconnectingH\x00\x12\x31\n\x0breconnected\x18\x18 \x01(\x0b\x32\x1a.livekit.proto.ReconnectedH\x00\x12=\n\x12\x65\x32\x65\x65_state_changed\x18\x19 \x01(\x0b\x32\x1f.livekit.proto.E2eeStateChangedH\x00\x12%\n\x03\x65os\x18\x1a \x01(\x0b\x32\x16.livekit.proto.RoomEOSH\x00\x12\x41\n\x14\x64\x61ta_packet_received\x18\x1b \x01(\x0b\x32!.livekit.proto.DataPacketReceivedH\x00\x12\x46\n\x16transcription_received\x18\x1c \x01(\x0b\x32$.livekit.proto.TranscriptionReceivedH\x00\x12:\n\x0c\x63hat_message\x18\x1d \x01(\x0b\x32\".livekit.proto.ChatMessageReceivedH\x00\x12I\n\x16stream_header_received\x18\x1e \x01(\x0b\x32\'.livekit.proto.DataStreamHeaderReceivedH\x00\x12G\n\x15stream_chunk_received\x18\x1f \x01(\x0b\x32&.livekit.proto.DataStreamChunkReceivedH\x00\x12K\n\x17stream_trailer_received\x18  \x01(\x0b\x32(.livekit.proto.DataStreamTrailerReceivedH\x00\x12i\n\"data_channel_low_threshold_changed\x18! \x01(\x0b\x32;.livekit.proto.DataChannelBufferedAmountLowThresholdChangedH\x00\x12=\n\x12\x62yte_stream_opened\x18\" \x01(\x0b\x32\x1f.livekit.proto.ByteStreamOpenedH\x00\x12=\n\x12text_stream_opened\x18# \x01(\x0b\x32\x1f.livekit.proto.TextStreamOpenedH\x00\x12/\n\x0croom_updated\x18$ \x01(\x0b\x32\x17.livekit.proto.RoomInfoH\x00\x12(\n\x05moved\x18% \x01(\x0b\x32\x17.livekit.proto.RoomInfoH\x00\x12\x42\n\x14participants_updated\x18& \x01(\x0b\x32\".livekit.proto.ParticipantsUpdatedH\x00\x42\t\n\x07message\"\xc9\x02\n\x08RoomInfo\x12\x0b\n\x03sid\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x02(\t\x12\x10\n\x08metadata\x18\x03 \x02(\t\x12.\n&lossy_dc_buffered_amount_low_threshold\x18\x04 \x02(\x04\x12\x31\n)reliable_dc_buffered_amount_low_threshold\x18\x05 \x02(\x04\x12\x15\n\rempty_timeout\x18\x06 \x02(\r\x12\x19\n\x11\x64\x65parture_timeout\x18\x07 \x02(\r\x12\x18\n\x10max_participants\x18\x08 \x02(\r\x12\x15\n\rcreation_time\x18\t \x02(\x03\x12\x18\n\x10num_participants\x18\n \x02(\r\x12\x16\n\x0enum_publishers\x18\x0b \x02(\r\x12\x18\n\x10\x61\x63tive_recording\x18\x0c \x02(\x08\"a\n\tOwnedRoom\x12-\n\x06handle\x18\x01 \x02(\x0b\x32\x1d.livekit.proto.FfiOwnedHandle\x12%\n\x04info\x18\x02 \x02(\x0b\x32\x17.livekit.proto.RoomInfo\"K\n\x13ParticipantsUpdated\x12\x34\n\x0cparticipants\x18\x01 \x03(\x0b\x32\x1e.livekit.proto.ParticipantInfo\"E\n\x14ParticipantConnected\x12-\n\x04info\x18\x01 \x02(\x0b\x32\x1f.livekit.proto.OwnedParticipant\"s\n\x17ParticipantDisconnected\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12:\n\x11\x64isconnect_reason\x18\x02 \x02(\x0e\x32\x1f.livekit.proto.DisconnectReason\"(\n\x13LocalTrackPublished\x12\x11\n\ttrack_sid\x18\x01 \x02(\t\"0\n\x15LocalTrackUnpublished\x12\x17\n\x0fpublication_sid\x18\x01 \x02(\t\")\n\x14LocalTrackSubscribed\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\"i\n\x0eTrackPublished\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x39\n\x0bpublication\x18\x02 \x02(\x0b\x32$.livekit.proto.OwnedTrackPublication\"I\n\x10TrackUnpublished\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x17\n\x0fpublication_sid\x18\x02 \x02(\t\"Y\n\x0fTrackSubscribed\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12(\n\x05track\x18\x02 \x02(\x0b\x32\x19.livekit.proto.OwnedTrack\"D\n\x11TrackUnsubscribed\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\"Y\n\x17TrackSubscriptionFailed\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\x12\r\n\x05\x65rror\x18\x03 \x02(\t\"=\n\nTrackMuted\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\"?\n\x0cTrackUnmuted\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x11\n\ttrack_sid\x18\x02 \x02(\t\"_\n\x10\x45\x32\x65\x65StateChanged\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12-\n\x05state\x18\x02 \x02(\x0e\x32\x1e.livekit.proto.EncryptionState\"7\n\x15\x41\x63tiveSpeakersChanged\x12\x1e\n\x16participant_identities\x18\x01 \x03(\t\"\'\n\x13RoomMetadataChanged\x12\x10\n\x08metadata\x18\x01 \x02(\t\"\x1d\n\x0eRoomSidChanged\x12\x0b\n\x03sid\x18\x01 \x02(\t\"L\n\x1aParticipantMetadataChanged\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x10\n\x08metadata\x18\x02 \x02(\t\"\xac\x01\n\x1cParticipantAttributesChanged\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x32\n\nattributes\x18\x02 \x03(\x0b\x32\x1e.livekit.proto.AttributesEntry\x12:\n\x12\x63hanged_attributes\x18\x03 \x03(\x0b\x32\x1e.livekit.proto.AttributesEntry\"D\n\x16ParticipantNameChanged\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x0c\n\x04name\x18\x02 \x02(\t\"k\n\x18\x43onnectionQualityChanged\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x31\n\x07quality\x18\x02 \x02(\x0e\x32 .livekit.proto.ConnectionQuality\"E\n\nUserPacket\x12(\n\x04\x64\x61ta\x18\x01 \x02(\x0b\x32\x1a.livekit.proto.OwnedBuffer\x12\r\n\x05topic\x18\x02 \x01(\t\"y\n\x0b\x43hatMessage\x12\n\n\x02id\x18\x01 \x02(\t\x12\x11\n\ttimestamp\x18\x02 \x02(\x03\x12\x0f\n\x07message\x18\x03 \x02(\t\x12\x16\n\x0e\x65\x64it_timestamp\x18\x04 \x01(\x03\x12\x0f\n\x07\x64\x65leted\x18\x05 \x01(\x08\x12\x11\n\tgenerated\x18\x06 \x01(\x08\"`\n\x13\x43hatMessageReceived\x12+\n\x07message\x18\x01 \x02(\x0b\x32\x1a.livekit.proto.ChatMessage\x12\x1c\n\x14participant_identity\x18\x02 \x02(\t\"&\n\x07SipDTMF\x12\x0c\n\x04\x63ode\x18\x01 \x02(\r\x12\r\n\x05\x64igit\x18\x02 \x01(\t\"\xbf\x01\n\x12\x44\x61taPacketReceived\x12+\n\x04kind\x18\x01 \x02(\x0e\x32\x1d.livekit.proto.DataPacketKind\x12\x1c\n\x14participant_identity\x18\x02 \x02(\t\x12)\n\x04user\x18\x04 \x01(\x0b\x32\x19.livekit.proto.UserPacketH\x00\x12*\n\x08sip_dtmf\x18\x05 \x01(\x0b\x32\x16.livekit.proto.SipDTMFH\x00\x42\x07\n\x05value\"\x7f\n\x15TranscriptionReceived\x12\x1c\n\x14participant_identity\x18\x01 \x01(\t\x12\x11\n\ttrack_sid\x18\x02 \x01(\t\x12\x35\n\x08segments\x18\x03 \x03(\x0b\x32#.livekit.proto.TranscriptionSegment\"G\n\x16\x43onnectionStateChanged\x12-\n\x05state\x18\x01 \x02(\x0e\x32\x1e.livekit.proto.ConnectionState\"\x0b\n\tConnected\"?\n\x0c\x44isconnected\x12/\n\x06reason\x18\x01 \x02(\x0e\x32\x1f.livekit.proto.DisconnectReason\"\x0e\n\x0cReconnecting\"\r\n\x0bReconnected\"\t\n\x07RoomEOS\"\x8e\x07\n\nDataStream\x1a\xaa\x01\n\nTextHeader\x12?\n\x0eoperation_type\x18\x01 \x02(\x0e\x32\'.livekit.proto.DataStream.OperationType\x12\x0f\n\x07version\x18\x02 \x01(\x05\x12\x1a\n\x12reply_to_stream_id\x18\x03 \x01(\t\x12\x1b\n\x13\x61ttached_stream_ids\x18\x04 \x03(\t\x12\x11\n\tgenerated\x18\x05 \x01(\x08\x1a\x1a\n\nByteHeader\x12\x0c\n\x04name\x18\x01 \x02(\t\x1a\xeb\x02\n\x06Header\x12\x11\n\tstream_id\x18\x01 \x02(\t\x12\x11\n\ttimestamp\x18\x02 \x02(\x03\x12\x11\n\tmime_type\x18\x03 \x02(\t\x12\r\n\x05topic\x18\x04 \x02(\t\x12\x14\n\x0ctotal_length\x18\x05 \x01(\x04\x12\x44\n\nattributes\x18\x06 \x03(\x0b\x32\x30.livekit.proto.DataStream.Header.AttributesEntry\x12;\n\x0btext_header\x18\x07 \x01(\x0b\x32$.livekit.proto.DataStream.TextHeaderH\x00\x12;\n\x0b\x62yte_header\x18\x08 \x01(\x0b\x32$.livekit.proto.DataStream.ByteHeaderH\x00\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x10\n\x0e\x63ontent_header\x1a]\n\x05\x43hunk\x12\x11\n\tstream_id\x18\x01 \x02(\t\x12\x13\n\x0b\x63hunk_index\x18\x02 \x02(\x04\x12\x0f\n\x07\x63ontent\x18\x03 \x02(\x0c\x12\x0f\n\x07version\x18\x04 \x01(\x05\x12\n\n\x02iv\x18\x05 \x01(\x0c\x1a\xa6\x01\n\x07Trailer\x12\x11\n\tstream_id\x18\x01 \x02(\t\x12\x0e\n\x06reason\x18\x02 \x02(\t\x12\x45\n\nattributes\x18\x03 \x03(\x0b\x32\x31.livekit.proto.DataStream.Trailer.AttributesEntry\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"A\n\rOperationType\x12\n\n\x06\x43REATE\x10\x00\x12\n\n\x06UPDATE\x10\x01\x12\n\n\x06\x44\x45LETE\x10\x02\x12\x0c\n\x08REACTION\x10\x03\"j\n\x18\x44\x61taStreamHeaderReceived\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x30\n\x06header\x18\x02 \x02(\x0b\x32 .livekit.proto.DataStream.Header\"g\n\x17\x44\x61taStreamChunkReceived\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12.\n\x05\x63hunk\x18\x02 \x02(\x0b\x32\x1f.livekit.proto.DataStream.Chunk\"m\n\x19\x44\x61taStreamTrailerReceived\x12\x1c\n\x14participant_identity\x18\x01 \x02(\t\x12\x32\n\x07trailer\x18\x02 \x02(\x0b\x32!.livekit.proto.DataStream.Trailer\"\xa6\x01\n\x17SendStreamHeaderRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x30\n\x06header\x18\x02 \x02(\x0b\x32 .livekit.proto.DataStream.Header\x12\x1e\n\x16\x64\x65stination_identities\x18\x03 \x03(\t\x12\x17\n\x0fsender_identity\x18\x04 \x02(\t\"\xa3\x01\n\x16SendStreamChunkRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12.\n\x05\x63hunk\x18\x02 \x02(\x0b\x32\x1f.livekit.proto.DataStream.Chunk\x12\x1e\n\x16\x64\x65stination_identities\x18\x03 \x03(\t\x12\x17\n\x0fsender_identity\x18\x04 \x02(\t\"\xa9\x01\n\x18SendStreamTrailerRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x32\n\x07trailer\x18\x02 \x02(\x0b\x32!.livekit.proto.DataStream.Trailer\x12\x1e\n\x16\x64\x65stination_identities\x18\x03 \x03(\t\x12\x17\n\x0fsender_identity\x18\x04 \x02(\t\",\n\x18SendStreamHeaderResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"+\n\x17SendStreamChunkResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"-\n\x19SendStreamTrailerResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\";\n\x18SendStreamHeaderCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\":\n\x17SendStreamChunkCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"<\n\x19SendStreamTrailerCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"\x93\x01\n/SetDataChannelBufferedAmountLowThresholdRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x11\n\tthreshold\x18\x02 \x02(\x04\x12+\n\x04kind\x18\x03 \x02(\x0e\x32\x1d.livekit.proto.DataPacketKind\"2\n0SetDataChannelBufferedAmountLowThresholdResponse\"n\n,DataChannelBufferedAmountLowThresholdChanged\x12+\n\x04kind\x18\x01 \x02(\x0e\x32\x1d.livekit.proto.DataPacketKind\x12\x11\n\tthreshold\x18\x02 \x02(\x04\"f\n\x10\x42yteStreamOpened\x12\x34\n\x06reader\x18\x01 \x02(\x0b\x32$.livekit.proto.OwnedByteStreamReader\x12\x1c\n\x14participant_identity\x18\x02 \x02(\t\"f\n\x10TextStreamOpened\x12\x34\n\x06reader\x18\x01 \x02(\x0b\x32$.livekit.proto.OwnedTextStreamReader\x12\x1c\n\x14participant_identity\x18\x02 \x02(\t*P\n\x10IceTransportType\x12\x13\n\x0fTRANSPORT_RELAY\x10\x00\x12\x14\n\x10TRANSPORT_NOHOST\x10\x01\x12\x11\n\rTRANSPORT_ALL\x10\x02*C\n\x18\x43ontinualGatheringPolicy\x12\x0f\n\x0bGATHER_ONCE\x10\x00\x12\x16\n\x12GATHER_CONTINUALLY\x10\x01*`\n\x11\x43onnectionQuality\x12\x10\n\x0cQUALITY_POOR\x10\x00\x12\x10\n\x0cQUALITY_GOOD\x10\x01\x12\x15\n\x11QUALITY_EXCELLENT\x10\x02\x12\x10\n\x0cQUALITY_LOST\x10\x03*S\n\x0f\x43onnectionState\x12\x15\n\x11\x43ONN_DISCONNECTED\x10\x00\x12\x12\n\x0e\x43ONN_CONNECTED\x10\x01\x12\x15\n\x11\x43ONN_RECONNECTING\x10\x02*3\n\x0e\x44\x61taPacketKind\x12\x0e\n\nKIND_LOSSY\x10\x00\x12\x11\n\rKIND_RELIABLE\x10\x01\x42\x10\xaa\x02\rLiveKit.Proto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'room_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\252\002\rLiveKit.Proto'
  _globals['_PUBLISHDATAREQUEST'].fields_by_name['destination_sids']._options = None
  _globals['_PUBLISHDATAREQUEST'].fields_by_name['destination_sids']._serialized_options = b'\030\001'
  _globals['_DATASTREAM_HEADER_ATTRIBUTESENTRY']._options = None
  _globals['_DATASTREAM_HEADER_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_DATASTREAM_TRAILER_ATTRIBUTESENTRY']._options = None
  _globals['_DATASTREAM_TRAILER_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_ICETRANSPORTTYPE']._serialized_start=12812
  _globals['_ICETRANSPORTTYPE']._serialized_end=12892
  _globals['_CONTINUALGATHERINGPOLICY']._serialized_start=12894
  _globals['_CONTINUALGATHERINGPOLICY']._serialized_end=12961
  _globals['_CONNECTIONQUALITY']._serialized_start=12963
  _globals['_CONNECTIONQUALITY']._serialized_end=13059
  _globals['_CONNECTIONSTATE']._serialized_start=13061
  _globals['_CONNECTIONSTATE']._serialized_end=13144
  _globals['_DATAPACKETKIND']._serialized_start=13146
  _globals['_DATAPACKETKIND']._serialized_end=13197
  _globals['_CONNECTREQUEST']._serialized_start=138
  _globals['_CONNECTREQUEST']._serialized_end=227
  _globals['_CONNECTRESPONSE']._serialized_start=229
  _globals['_CONNECTRESPONSE']._serialized_end=264
  _globals['_CONNECTCALLBACK']._serialized_start=267
  _globals['_CONNECTCALLBACK']._serialized_end=714
  _globals['_CONNECTCALLBACK_PARTICIPANTWITHTRACKS']._serialized_start=379
  _globals['_CONNECTCALLBACK_PARTICIPANTWITHTRACKS']._serialized_end=516
  _globals['_CONNECTCALLBACK_RESULT']._serialized_start=519
  _globals['_CONNECTCALLBACK_RESULT']._serialized_end=703
  _globals['_DISCONNECTREQUEST']._serialized_start=716
  _globals['_DISCONNECTREQUEST']._serialized_end=756
  _globals['_DISCONNECTRESPONSE']._serialized_start=758
  _globals['_DISCONNECTRESPONSE']._serialized_end=796
  _globals['_DISCONNECTCALLBACK']._serialized_start=798
  _globals['_DISCONNECTCALLBACK']._serialized_end=836
  _globals['_PUBLISHTRACKREQUEST']._serialized_start=839
  _globals['_PUBLISHTRACKREQUEST']._serialized_end=969
  _globals['_PUBLISHTRACKRESPONSE']._serialized_start=971
  _globals['_PUBLISHTRACKRESPONSE']._serialized_end=1011
  _globals['_PUBLISHTRACKCALLBACK']._serialized_start=1014
  _globals['_PUBLISHTRACKCALLBACK']._serialized_end=1143
  _globals['_UNPUBLISHTRACKREQUEST']._serialized_start=1145
  _globals['_UNPUBLISHTRACKREQUEST']._serialized_end=1248
  _globals['_UNPUBLISHTRACKRESPONSE']._serialized_start=1250
  _globals['_UNPUBLISHTRACKRESPONSE']._serialized_end=1292
  _globals['_UNPUBLISHTRACKCALLBACK']._serialized_start=1294
  _globals['_UNPUBLISHTRACKCALLBACK']._serialized_end=1351
  _globals['_PUBLISHDATAREQUEST']._serialized_start=1354
  _globals['_PUBLISHDATAREQUEST']._serialized_end=1539
  _globals['_PUBLISHDATARESPONSE']._serialized_start=1541
  _globals['_PUBLISHDATARESPONSE']._serialized_end=1580
  _globals['_PUBLISHDATACALLBACK']._serialized_start=1582
  _globals['_PUBLISHDATACALLBACK']._serialized_end=1636
  _globals['_PUBLISHTRANSCRIPTIONREQUEST']._serialized_start=1639
  _globals['_PUBLISHTRANSCRIPTIONREQUEST']._serialized_end=1805
  _globals['_PUBLISHTRANSCRIPTIONRESPONSE']._serialized_start=1807
  _globals['_PUBLISHTRANSCRIPTIONRESPONSE']._serialized_end=1855
  _globals['_PUBLISHTRANSCRIPTIONCALLBACK']._serialized_start=1857
  _globals['_PUBLISHTRANSCRIPTIONCALLBACK']._serialized_end=1920
  _globals['_PUBLISHSIPDTMFREQUEST']._serialized_start=1922
  _globals['_PUBLISHSIPDTMFREQUEST']._serialized_end=2040
  _globals['_PUBLISHSIPDTMFRESPONSE']._serialized_start=2042
  _globals['_PUBLISHSIPDTMFRESPONSE']._serialized_end=2084
  _globals['_PUBLISHSIPDTMFCALLBACK']._serialized_start=2086
  _globals['_PUBLISHSIPDTMFCALLBACK']._serialized_end=2143
  _globals['_SETLOCALMETADATAREQUEST']._serialized_start=2145
  _globals['_SETLOCALMETADATAREQUEST']._serialized_end=2222
  _globals['_SETLOCALMETADATARESPONSE']._serialized_start=2224
  _globals['_SETLOCALMETADATARESPONSE']._serialized_end=2268
  _globals['_SETLOCALMETADATACALLBACK']._serialized_start=2270
  _globals['_SETLOCALMETADATACALLBACK']._serialized_end=2329
  _globals['_SENDCHATMESSAGEREQUEST']._serialized_start=2332
  _globals['_SENDCHATMESSAGEREQUEST']._serialized_end=2464
  _globals['_EDITCHATMESSAGEREQUEST']._serialized_start=2467
  _globals['_EDITCHATMESSAGEREQUEST']._serialized_end=2655
  _globals['_SENDCHATMESSAGERESPONSE']._serialized_start=2657
  _globals['_SENDCHATMESSAGERESPONSE']._serialized_end=2700
  _globals['_SENDCHATMESSAGECALLBACK']._serialized_start=2702
  _globals['_SENDCHATMESSAGECALLBACK']._serialized_end=2825
  _globals['_SETLOCALATTRIBUTESREQUEST']._serialized_start=2827
  _globals['_SETLOCALATTRIBUTESREQUEST']._serialized_end=2940
  _globals['_ATTRIBUTESENTRY']._serialized_start=2942
  _globals['_ATTRIBUTESENTRY']._serialized_end=2987
  _globals['_SETLOCALATTRIBUTESRESPONSE']._serialized_start=2989
  _globals['_SETLOCALATTRIBUTESRESPONSE']._serialized_end=3035
  _globals['_SETLOCALATTRIBUTESCALLBACK']._serialized_start=3037
  _globals['_SETLOCALATTRIBUTESCALLBACK']._serialized_end=3098
  _globals['_SETLOCALNAMEREQUEST']._serialized_start=3100
  _globals['_SETLOCALNAMEREQUEST']._serialized_end=3169
  _globals['_SETLOCALNAMERESPONSE']._serialized_start=3171
  _globals['_SETLOCALNAMERESPONSE']._serialized_end=3211
  _globals['_SETLOCALNAMECALLBACK']._serialized_start=3213
  _globals['_SETLOCALNAMECALLBACK']._serialized_end=3268
  _globals['_SETSUBSCRIBEDREQUEST']._serialized_start=3270
  _globals['_SETSUBSCRIBEDREQUEST']._serialized_end=3339
  _globals['_SETSUBSCRIBEDRESPONSE']._serialized_start=3341
  _globals['_SETSUBSCRIBEDRESPONSE']._serialized_end=3364
  _globals['_GETSESSIONSTATSREQUEST']._serialized_start=3366
  _globals['_GETSESSIONSTATSREQUEST']._serialized_end=3411
  _globals['_GETSESSIONSTATSRESPONSE']._serialized_start=3413
  _globals['_GETSESSIONSTATSRESPONSE']._serialized_end=3456
  _globals['_GETSESSIONSTATSCALLBACK']._serialized_start=3459
  _globals['_GETSESSIONSTATSCALLBACK']._serialized_end=3706
  _globals['_GETSESSIONSTATSCALLBACK_RESULT']._serialized_start=3586
  _globals['_GETSESSIONSTATSCALLBACK_RESULT']._serialized_end=3695
  _globals['_VIDEOENCODING']._serialized_start=3708
  _globals['_VIDEOENCODING']._serialized_end=3767
  _globals['_AUDIOENCODING']._serialized_start=3769
  _globals['_AUDIOENCODING']._serialized_end=3805
  _globals['_TRACKPUBLISHOPTIONS']._serialized_start=3808
  _globals['_TRACKPUBLISHOPTIONS']._serialized_end=4090
  _globals['_ICESERVER']._serialized_start=4092
  _globals['_ICESERVER']._serialized_end=4153
  _globals['_RTCCONFIG']._serialized_start=4156
  _globals['_RTCCONFIG']._serialized_end=4352
  _globals['_ROOMOPTIONS']._serialized_start=4355
  _globals['_ROOMOPTIONS']._serialized_end=4545
  _globals['_TRANSCRIPTIONSEGMENT']._serialized_start=4547
  _globals['_TRANSCRIPTIONSEGMENT']._serialized_end=4666
  _globals['_BUFFERINFO']._serialized_start=4668
  _globals['_BUFFERINFO']._serialized_end=4716
  _globals['_OWNEDBUFFER']._serialized_start=4718
  _globals['_OWNEDBUFFER']._serialized_end=4819
  _globals['_ROOMEVENT']._serialized_start=4822
  _globals['_ROOMEVENT']._serialized_end=7324
  _globals['_ROOMINFO']._serialized_start=7327
  _globals['_ROOMINFO']._serialized_end=7656
  _globals['_OWNEDROOM']._serialized_start=7658
  _globals['_OWNEDROOM']._serialized_end=7755
  _globals['_PARTICIPANTSUPDATED']._serialized_start=7757
  _globals['_PARTICIPANTSUPDATED']._serialized_end=7832
  _globals['_PARTICIPANTCONNECTED']._serialized_start=7834
  _globals['_PARTICIPANTCONNECTED']._serialized_end=7903
  _globals['_PARTICIPANTDISCONNECTED']._serialized_start=7905
  _globals['_PARTICIPANTDISCONNECTED']._serialized_end=8020
  _globals['_LOCALTRACKPUBLISHED']._serialized_start=8022
  _globals['_LOCALTRACKPUBLISHED']._serialized_end=8062
  _globals['_LOCALTRACKUNPUBLISHED']._serialized_start=8064
  _globals['_LOCALTRACKUNPUBLISHED']._serialized_end=8112
  _globals['_LOCALTRACKSUBSCRIBED']._serialized_start=8114
  _globals['_LOCALTRACKSUBSCRIBED']._serialized_end=8155
  _globals['_TRACKPUBLISHED']._serialized_start=8157
  _globals['_TRACKPUBLISHED']._serialized_end=8262
  _globals['_TRACKUNPUBLISHED']._serialized_start=8264
  _globals['_TRACKUNPUBLISHED']._serialized_end=8337
  _globals['_TRACKSUBSCRIBED']._serialized_start=8339
  _globals['_TRACKSUBSCRIBED']._serialized_end=8428
  _globals['_TRACKUNSUBSCRIBED']._serialized_start=8430
  _globals['_TRACKUNSUBSCRIBED']._serialized_end=8498
  _globals['_TRACKSUBSCRIPTIONFAILED']._serialized_start=8500
  _globals['_TRACKSUBSCRIPTIONFAILED']._serialized_end=8589
  _globals['_TRACKMUTED']._serialized_start=8591
  _globals['_TRACKMUTED']._serialized_end=8652
  _globals['_TRACKUNMUTED']._serialized_start=8654
  _globals['_TRACKUNMUTED']._serialized_end=8717
  _globals['_E2EESTATECHANGED']._serialized_start=8719
  _globals['_E2EESTATECHANGED']._serialized_end=8814
  _globals['_ACTIVESPEAKERSCHANGED']._serialized_start=8816
  _globals['_ACTIVESPEAKERSCHANGED']._serialized_end=8871
  _globals['_ROOMMETADATACHANGED']._serialized_start=8873
  _globals['_ROOMMETADATACHANGED']._serialized_end=8912
  _globals['_ROOMSIDCHANGED']._serialized_start=8914
  _globals['_ROOMSIDCHANGED']._serialized_end=8943
  _globals['_PARTICIPANTMETADATACHANGED']._serialized_start=8945
  _globals['_PARTICIPANTMETADATACHANGED']._serialized_end=9021
  _globals['_PARTICIPANTATTRIBUTESCHANGED']._serialized_start=9024
  _globals['_PARTICIPANTATTRIBUTESCHANGED']._serialized_end=9196
  _globals['_PARTICIPANTNAMECHANGED']._serialized_start=9198
  _globals['_PARTICIPANTNAMECHANGED']._serialized_end=9266
  _globals['_CONNECTIONQUALITYCHANGED']._serialized_start=9268
  _globals['_CONNECTIONQUALITYCHANGED']._serialized_end=9375
  _globals['_USERPACKET']._serialized_start=9377
  _globals['_USERPACKET']._serialized_end=9446
  _globals['_CHATMESSAGE']._serialized_start=9448
  _globals['_CHATMESSAGE']._serialized_end=9569
  _globals['_CHATMESSAGERECEIVED']._serialized_start=9571
  _globals['_CHATMESSAGERECEIVED']._serialized_end=9667
  _globals['_SIPDTMF']._serialized_start=9669
  _globals['_SIPDTMF']._serialized_end=9707
  _globals['_DATAPACKETRECEIVED']._serialized_start=9710
  _globals['_DATAPACKETRECEIVED']._serialized_end=9901
  _globals['_TRANSCRIPTIONRECEIVED']._serialized_start=9903
  _globals['_TRANSCRIPTIONRECEIVED']._serialized_end=10030
  _globals['_CONNECTIONSTATECHANGED']._serialized_start=10032
  _globals['_CONNECTIONSTATECHANGED']._serialized_end=10103
  _globals['_CONNECTED']._serialized_start=10105
  _globals['_CONNECTED']._serialized_end=10116
  _globals['_DISCONNECTED']._serialized_start=10118
  _globals['_DISCONNECTED']._serialized_end=10181
  _globals['_RECONNECTING']._serialized_start=10183
  _globals['_RECONNECTING']._serialized_end=10197
  _globals['_RECONNECTED']._serialized_start=10199
  _globals['_RECONNECTED']._serialized_end=10212
  _globals['_ROOMEOS']._serialized_start=10214
  _globals['_ROOMEOS']._serialized_end=10223
  _globals['_DATASTREAM']._serialized_start=10226
  _globals['_DATASTREAM']._serialized_end=11136
  _globals['_DATASTREAM_TEXTHEADER']._serialized_start=10241
  _globals['_DATASTREAM_TEXTHEADER']._serialized_end=10411
  _globals['_DATASTREAM_BYTEHEADER']._serialized_start=10413
  _globals['_DATASTREAM_BYTEHEADER']._serialized_end=10439
  _globals['_DATASTREAM_HEADER']._serialized_start=10442
  _globals['_DATASTREAM_HEADER']._serialized_end=10805
  _globals['_DATASTREAM_HEADER_ATTRIBUTESENTRY']._serialized_start=10738
  _globals['_DATASTREAM_HEADER_ATTRIBUTESENTRY']._serialized_end=10787
  _globals['_DATASTREAM_CHUNK']._serialized_start=10807
  _globals['_DATASTREAM_CHUNK']._serialized_end=10900
  _globals['_DATASTREAM_TRAILER']._serialized_start=10903
  _globals['_DATASTREAM_TRAILER']._serialized_end=11069
  _globals['_DATASTREAM_TRAILER_ATTRIBUTESENTRY']._serialized_start=10738
  _globals['_DATASTREAM_TRAILER_ATTRIBUTESENTRY']._serialized_end=10787
  _globals['_DATASTREAM_OPERATIONTYPE']._serialized_start=11071
  _globals['_DATASTREAM_OPERATIONTYPE']._serialized_end=11136
  _globals['_DATASTREAMHEADERRECEIVED']._serialized_start=11138
  _globals['_DATASTREAMHEADERRECEIVED']._serialized_end=11244
  _globals['_DATASTREAMCHUNKRECEIVED']._serialized_start=11246
  _globals['_DATASTREAMCHUNKRECEIVED']._serialized_end=11349
  _globals['_DATASTREAMTRAILERRECEIVED']._serialized_start=11351
  _globals['_DATASTREAMTRAILERRECEIVED']._serialized_end=11460
  _globals['_SENDSTREAMHEADERREQUEST']._serialized_start=11463
  _globals['_SENDSTREAMHEADERREQUEST']._serialized_end=11629
  _globals['_SENDSTREAMCHUNKREQUEST']._serialized_start=11632
  _globals['_SENDSTREAMCHUNKREQUEST']._serialized_end=11795
  _globals['_SENDSTREAMTRAILERREQUEST']._serialized_start=11798
  _globals['_SENDSTREAMTRAILERREQUEST']._serialized_end=11967
  _globals['_SENDSTREAMHEADERRESPONSE']._serialized_start=11969
  _globals['_SENDSTREAMHEADERRESPONSE']._serialized_end=12013
  _globals['_SENDSTREAMCHUNKRESPONSE']._serialized_start=12015
  _globals['_SENDSTREAMCHUNKRESPONSE']._serialized_end=12058
  _globals['_SENDSTREAMTRAILERRESPONSE']._serialized_start=12060
  _globals['_SENDSTREAMTRAILERRESPONSE']._serialized_end=12105
  _globals['_SENDSTREAMHEADERCALLBACK']._serialized_start=12107
  _globals['_SENDSTREAMHEADERCALLBACK']._serialized_end=12166
  _globals['_SENDSTREAMCHUNKCALLBACK']._serialized_start=12168
  _globals['_SENDSTREAMCHUNKCALLBACK']._serialized_end=12226
  _globals['_SENDSTREAMTRAILERCALLBACK']._serialized_start=12228
  _globals['_SENDSTREAMTRAILERCALLBACK']._serialized_end=12288
  _globals['_SETDATACHANNELBUFFEREDAMOUNTLOWTHRESHOLDREQUEST']._serialized_start=12291
  _globals['_SETDATACHANNELBUFFEREDAMOUNTLOWTHRESHOLDREQUEST']._serialized_end=12438
  _globals['_SETDATACHANNELBUFFEREDAMOUNTLOWTHRESHOLDRESPONSE']._serialized_start=12440
  _globals['_SETDATACHANNELBUFFEREDAMOUNTLOWTHRESHOLDRESPONSE']._serialized_end=12490
  _globals['_DATACHANNELBUFFEREDAMOUNTLOWTHRESHOLDCHANGED']._serialized_start=12492
  _globals['_DATACHANNELBUFFEREDAMOUNTLOWTHRESHOLDCHANGED']._serialized_end=12602
  _globals['_BYTESTREAMOPENED']._serialized_start=12604
  _globals['_BYTESTREAMOPENED']._serialized_end=12706
  _globals['_TEXTSTREAMOPENED']._serialized_start=12708
  _globals['_TEXTSTREAMOPENED']._serialized_end=12810
# @@protoc_insertion_point(module_scope)
