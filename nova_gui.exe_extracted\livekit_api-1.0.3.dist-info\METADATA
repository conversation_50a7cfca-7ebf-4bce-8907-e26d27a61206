Metadata-Version: 2.4
Name: livekit-api
Version: 1.0.3
Summary: Python Server API for LiveKit
Home-page: https://github.com/livekit/python-sdks
License: Apache-2.0
Project-URL: Documentation, https://docs.livekit.io
Project-URL: Website, https://livekit.io/
Project-URL: Source, https://github.com/livekit/python-sdks/
Keywords: webrtc,realtime,audio,video,livekit
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Video
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.9.0
Description-Content-Type: text/markdown
Requires-Dist: pyjwt>=2.0.0
Requires-Dist: aiohttp>=3.9.0
Requires-Dist: protobuf>=4
Requires-Dist: types-protobuf>=4
Requires-Dist: livekit-protocol<2.0.0,>=1.0.4
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: project-url
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# LiveKit Server APIs

Access LiveKit server APIs and generate access tokens.

See https://docs.livekit.io/reference/server/server-apis for more information.

