Metadata-Version: 2.4
Name: livekit-plugins-silero
Version: 1.1.5
Summary: Agent Framework Plugin for Silero
Project-URL: Documentation, https://docs.livekit.io
Project-URL: Website, https://livekit.io/
Project-URL: Source, https://github.com/livekit/agents
Author-email: LiveKit <<EMAIL>>
License-Expression: Apache-2.0
Keywords: audio,livekit,realtime,video,webrtc
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Video
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.9.0
Requires-Dist: livekit-agents>=1.1.5
Requires-Dist: numpy>=1.26
Requires-Dist: onnxruntime>=1.18
Description-Content-Type: text/markdown

# Silero VAD plugin for LiveKit Agents

Support for VAD-based turn detection.

See [https://docs.livekit.io/agents/build/turns/vad/](https://docs.livekit.io/agents/build/turns/vad/) for more information.

## Installation

```bash
pip install livekit-plugins-silero
```

This plugin contains model files that would need to be downloaded prior to use.
