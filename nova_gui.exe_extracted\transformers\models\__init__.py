# Copyright 2020 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from typing import TYPE_CHECKING

from ..utils import _LazyModule
from ..utils.import_utils import define_import_structure


if TYPE_CHECKING:
    from .albert import *
    from .align import *
    from .altclip import *
    from .arcee import *
    from .aria import *
    from .audio_spectrogram_transformer import *
    from .auto import *
    from .autoformer import *
    from .aya_vision import *
    from .bamba import *
    from .bark import *
    from .bart import *
    from .barthez import *
    from .bartpho import *
    from .beit import *
    from .bert import *
    from .bert_generation import *
    from .bert_japanese import *
    from .bertweet import *
    from .big_bird import *
    from .bigbird_pegasus import *
    from .biogpt import *
    from .bit import *
    from .bitnet import *
    from .blenderbot import *
    from .blenderbot_small import *
    from .blip import *
    from .blip_2 import *
    from .bloom import *
    from .bridgetower import *
    from .bros import *
    from .byt5 import *
    from .camembert import *
    from .canine import *
    from .chameleon import *
    from .chinese_clip import *
    from .clap import *
    from .clip import *
    from .clipseg import *
    from .clvp import *
    from .code_llama import *
    from .codegen import *
    from .cohere import *
    from .cohere2 import *
    from .colpali import *
    from .colqwen2 import *
    from .conditional_detr import *
    from .convbert import *
    from .convnext import *
    from .convnextv2 import *
    from .cpm import *
    from .cpmant import *
    from .csm import *
    from .ctrl import *
    from .cvt import *
    from .d_fine import *
    from .dab_detr import *
    from .dac import *
    from .data2vec import *
    from .dbrx import *
    from .deberta import *
    from .deberta_v2 import *
    from .decision_transformer import *
    from .deepseek_v3 import *
    from .deformable_detr import *
    from .deit import *
    from .deprecated import *
    from .depth_anything import *
    from .depth_pro import *
    from .detr import *
    from .dia import *
    from .dialogpt import *
    from .diffllama import *
    from .dinat import *
    from .dinov2 import *
    from .dinov2_with_registers import *
    from .distilbert import *
    from .dit import *
    from .donut import *
    from .dots1 import *
    from .dpr import *
    from .dpt import *
    from .efficientnet import *
    from .electra import *
    from .emu3 import *
    from .encodec import *
    from .encoder_decoder import *
    from .ernie import *
    from .esm import *
    from .falcon import *
    from .falcon_h1 import *
    from .falcon_mamba import *
    from .fastspeech2_conformer import *
    from .flaubert import *
    from .flava import *
    from .fnet import *
    from .focalnet import *
    from .fsmt import *
    from .funnel import *
    from .fuyu import *
    from .gemma import *
    from .gemma2 import *
    from .gemma3 import *
    from .git import *
    from .glm import *
    from .glm4 import *
    from .glpn import *
    from .got_ocr2 import *
    from .gpt2 import *
    from .gpt_bigcode import *
    from .gpt_neo import *
    from .gpt_neox import *
    from .gpt_neox_japanese import *
    from .gpt_sw3 import *
    from .gptj import *
    from .granite import *
    from .granite_speech import *
    from .granitemoe import *
    from .granitemoehybrid import *
    from .granitemoeshared import *
    from .grounding_dino import *
    from .groupvit import *
    from .helium import *
    from .herbert import *
    from .hgnet_v2 import *
    from .hiera import *
    from .hubert import *
    from .ibert import *
    from .idefics import *
    from .idefics2 import *
    from .idefics3 import *
    from .ijepa import *
    from .imagegpt import *
    from .informer import *
    from .instructblip import *
    from .instructblipvideo import *
    from .internvl import *
    from .jamba import *
    from .janus import *
    from .jetmoe import *
    from .kosmos2 import *
    from .kyutai_speech_to_text import *
    from .layoutlm import *
    from .layoutlmv2 import *
    from .layoutlmv3 import *
    from .layoutxlm import *
    from .led import *
    from .levit import *
    from .lightglue import *
    from .lilt import *
    from .llama import *
    from .llama4 import *
    from .llava import *
    from .llava_next import *
    from .llava_next_video import *
    from .llava_onevision import *
    from .longformer import *
    from .longt5 import *
    from .luke import *
    from .lxmert import *
    from .m2m_100 import *
    from .mamba import *
    from .mamba2 import *
    from .marian import *
    from .markuplm import *
    from .mask2former import *
    from .maskformer import *
    from .mbart import *
    from .mbart50 import *
    from .megatron_bert import *
    from .megatron_gpt2 import *
    from .mgp_str import *
    from .mimi import *
    from .minimax import *
    from .mistral import *
    from .mistral3 import *
    from .mixtral import *
    from .mlcd import *
    from .mllama import *
    from .mluke import *
    from .mobilebert import *
    from .mobilenet_v1 import *
    from .mobilenet_v2 import *
    from .mobilevit import *
    from .mobilevitv2 import *
    from .modernbert import *
    from .moonshine import *
    from .moshi import *
    from .mpnet import *
    from .mpt import *
    from .mra import *
    from .mt5 import *
    from .musicgen import *
    from .musicgen_melody import *
    from .mvp import *
    from .myt5 import *
    from .nemotron import *
    from .nllb import *
    from .nllb_moe import *
    from .nougat import *
    from .nystromformer import *
    from .olmo import *
    from .olmo2 import *
    from .olmoe import *
    from .omdet_turbo import *
    from .oneformer import *
    from .openai import *
    from .opt import *
    from .owlv2 import *
    from .owlvit import *
    from .paligemma import *
    from .patchtsmixer import *
    from .patchtst import *
    from .pegasus import *
    from .pegasus_x import *
    from .perceiver import *
    from .persimmon import *
    from .phi import *
    from .phi3 import *
    from .phi4_multimodal import *
    from .phimoe import *
    from .phobert import *
    from .pix2struct import *
    from .pixtral import *
    from .plbart import *
    from .poolformer import *
    from .pop2piano import *
    from .prompt_depth_anything import *
    from .prophetnet import *
    from .pvt import *
    from .pvt_v2 import *
    from .qwen2 import *
    from .qwen2_5_vl import *
    from .qwen2_audio import *
    from .qwen2_moe import *
    from .qwen2_vl import *
    from .qwen3 import *
    from .qwen3_moe import *
    from .rag import *
    from .recurrent_gemma import *
    from .reformer import *
    from .regnet import *
    from .rembert import *
    from .resnet import *
    from .roberta import *
    from .roberta_prelayernorm import *
    from .roc_bert import *
    from .roformer import *
    from .rt_detr import *
    from .rt_detr_v2 import *
    from .rwkv import *
    from .sam import *
    from .sam_hq import *
    from .seamless_m4t import *
    from .seamless_m4t_v2 import *
    from .segformer import *
    from .seggpt import *
    from .sew import *
    from .sew_d import *
    from .shieldgemma2 import *
    from .siglip import *
    from .siglip2 import *
    from .smolvlm import *
    from .speech_encoder_decoder import *
    from .speech_to_text import *
    from .speecht5 import *
    from .splinter import *
    from .squeezebert import *
    from .stablelm import *
    from .starcoder2 import *
    from .superglue import *
    from .superpoint import *
    from .swiftformer import *
    from .swin import *
    from .swin2sr import *
    from .swinv2 import *
    from .switch_transformers import *
    from .t5 import *
    from .t5gemma import *
    from .table_transformer import *
    from .tapas import *
    from .textnet import *
    from .time_series_transformer import *
    from .timesfm import *
    from .timesformer import *
    from .timm_backbone import *
    from .timm_wrapper import *
    from .trocr import *
    from .tvp import *
    from .udop import *
    from .umt5 import *
    from .unispeech import *
    from .unispeech_sat import *
    from .univnet import *
    from .upernet import *
    from .video_llava import *
    from .videomae import *
    from .vilt import *
    from .vipllava import *
    from .vision_encoder_decoder import *
    from .vision_text_dual_encoder import *
    from .visual_bert import *
    from .vit import *
    from .vit_mae import *
    from .vit_msn import *
    from .vitdet import *
    from .vitmatte import *
    from .vitpose import *
    from .vitpose_backbone import *
    from .vits import *
    from .vivit import *
    from .vjepa2 import *
    from .wav2vec2 import *
    from .wav2vec2_bert import *
    from .wav2vec2_conformer import *
    from .wav2vec2_phoneme import *
    from .wav2vec2_with_lm import *
    from .wavlm import *
    from .whisper import *
    from .x_clip import *
    from .xglm import *
    from .xlm import *
    from .xlm_roberta import *
    from .xlm_roberta_xl import *
    from .xlnet import *
    from .xmod import *
    from .yolos import *
    from .yoso import *
    from .zamba import *
    from .zamba2 import *
    from .zoedepth import *
else:
    import sys

    _file = globals()["__file__"]
    sys.modules[__name__] = _LazyModule(__name__, _file, define_import_structure(_file), module_spec=__spec__)
